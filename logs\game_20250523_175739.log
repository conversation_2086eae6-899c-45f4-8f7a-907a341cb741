[2025-05-23 17:57:39] [WARNING] [main.py:119] 游戏启动
[2025-05-23 17:57:39] [WARNING] [main.py:254] 开始创建游戏界面
[2025-05-23 17:57:41] [WARNING] [main.py:360] 游戏界面创建完成
[2025-05-23 17:57:41] [ERROR] [main.py:546] 初始化系统托盘图标失败: 'ResourceManager' object has no attribute 'get_image_path'
[2025-05-23 17:57:41] [ERROR] [main.py:547] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 521, in _init_tray_icon
    icon_path = resources.get_image_path("assets/images/icon.png")
                ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ResourceManager' object has no attribute 'get_image_path'. Did you mean: 'image_path'?

