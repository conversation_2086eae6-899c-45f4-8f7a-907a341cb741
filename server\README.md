# RPG Game Server

这是一个基于FastAPI的游戏服务器，用于支持RPG游戏的在线功能。

## 功能

- 用户认证（注册、登录）
- 角色管理
- 游戏状态同步
- 数据持久化

## 安装

1. 安装MongoDB数据库
2. 安装Python依赖：

```bash
pip install -r requirements.txt
```

## 配置

在`app/config.py`中配置服务器参数，或者创建`.env`文件设置环境变量。

## 运行

```bash
python run.py
```

服务器将在`http://localhost:8000`上运行。

## API文档

启动服务器后，访问`http://localhost:8000/docs`查看API文档。

## 主要API端点

- `/api/auth/register` - 注册新用户
- `/api/auth/token` - 用户登录获取令牌
- `/api/game/players` - 创建角色
- `/api/game/players/me` - 获取当前用户的角色
- `/api/game/state` - 获取/更新游戏状态
