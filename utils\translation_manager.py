import json
import os
from utils.logger import logger

class TranslationManager:
    _instance = None
    _translations = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TranslationManager, cls).__new__(cls)
            cls._instance._load_translations()
        return cls._instance
    
    def _load_translations(self):
        """加载翻译文件"""
        try:
            with open('data/translations.json', 'r', encoding='utf-8') as f:
                self._translations = json.load(f)
            logger.info("成功加载翻译文件")
        except Exception as e:
            logger.error(f"加载翻译文件失败: {e}")
            self._translations = {}
    
    def to_english(self, chinese_text, category=None):
        """将中文转换为英文"""
        if not self._translations:
            return chinese_text
            
        if category:
            # 如果在指定类别中查找
            if category in self._translations:
                for eng, chn in self._translations[category].items():
                    if chn == chinese_text:
                        return eng
        else:
            # 在所有类别中查找
            for category in self._translations.values():
                for eng, chn in category.items():
                    if chn == chinese_text:
                        return eng
                        
        return chinese_text
    
    def to_chinese(self, english_text, category=None):
        """将英文转换为中文"""
        if not self._translations:
            return english_text
            
        if category:
            # 如果在指定类别中查找
            if category in self._translations:
                return self._translations[category].get(english_text, english_text)
        else:
            # 在所有类别中查找
            for category in self._translations.values():
                if english_text in category:
                    return category[english_text]
                    
        return english_text
    
    def translate_data(self, data, to_english=True):
        """递归翻译数据结构"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                # 转换键
                translated_key = self.to_english(key) if to_english else self.to_chinese(key)
                # 转换值
                result[translated_key] = self.translate_data(value, to_english)
            return result
        elif isinstance(data, list):
            return [self.translate_data(item, to_english) for item in data]
        elif isinstance(data, str):
            return self.to_english(data) if to_english else self.to_chinese(data)
        else:
            return data 