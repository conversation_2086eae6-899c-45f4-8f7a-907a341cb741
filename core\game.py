import os
import time
import random
import pygame
import json
import traceback  # 添加traceback模块引入
import shutil  # 添加shutil模块引入
from typing import Dict, List, Any, Optional, Tuple, Union, Callable, TYPE_CHECKING
from pathlib import Path # 确保导入 Path
import logging
from utils.logger import logger
from utils.resource_manager import resources
from core.config import GameConfig
from core.monster import Monster
from core.battle import BattleSystem
from utils.auto_potion import AutoPotionSystem
import math

# 类型提示导入
if TYPE_CHECKING:
    from core.player import Player
    from core.types import PlayerType

logger = logging.getLogger(__name__)

# 添加防作弊系统类
class AntiCheatSystem:
    """简单的防作弊系统"""

    def __init__(self, game):
        self.game = game
        self.last_check_time = time.time()
        self.last_exp = 0
        self.last_gold = 0
        self.check_interval = 60  # 检查间隔（秒）

        # 时间加速检测相关变量
        self.system_time_reference = time.time()  # 系统时间参考点
        self.game_time_reference = 0  # 游戏时间参考点 (游戏开始时的累计帧时间)
        self.game_frame_count = 0  # 游戏帧计数
        self.last_frame_time = time.time()  # 上一帧时间
        self.frame_times = []  # 记录最近100帧的帧间隔
        self.max_frame_times = 100  # 最多保存100帧的数据
        # --- 修改：提高时间加速检测阈值，减少误报 ---
        self.time_acceleration_threshold = 1.8  # 加速检测阈值，原为 1.3
        # --- 结束修改 ---
        self.acceleration_warnings = 0  # 加速警告计数
        self.max_acceleration_warnings = 3  # 最大警告次数，超过则认为是作弊

        # 新增变量 - 检测游戏加速和系统时钟修改
        self.time_samples = []  # 时间采样数据
        self.max_time_samples = 10  # 最多保存的时间采样数
        self.time_check_anomalies = 0  # 时间异常计数
        self.time_drift_threshold = 5.0  # 系统时钟漂移阈值 (秒)
        self.last_system_time = time.time()  # 上次系统时间
        self.suspicious_time_jumps = 0  # 可疑的时间跳跃次数

        # 地图探索相关属性
        self.map_width = 500
        self.map_height = 300
        self.map_active_monsters = []
        self.game_mode = 'idle' # 可选值: 'idle', 'hunting', 'in_battle' (或者直接用 self.in_battle 判断战斗状态)
        self.last_map_update_time = time.time() # 用于地图模式下的增量时间计算

    def update_frame_time(self, dt):
        """更新帧时间数据，由游戏主循环每帧调用"""
        current_time = time.time()
        frame_time = current_time - self.last_frame_time
        self.last_frame_time = current_time

        # 添加到帧时间列表
        self.frame_times.append(frame_time)
        # 保持列表在最大长度以内
        if len(self.frame_times) > self.max_frame_times:
            self.frame_times.pop(0)

        # 游戏帧计数增加
        self.game_frame_count += 1

        # 每10帧采样一次系统时间数据用于时间漂移检测
        if self.game_frame_count % 10 == 0:
            # 检测系统时间回退
            if current_time < self.last_system_time:
                logging.warning(f"系统时间回退检测: {self.last_system_time} -> {current_time}, 差值: {self.last_system_time - current_time:.2f}秒")
                self.time_check_anomalies += 1

            time_diff = current_time - self.last_system_time
            # 记录时间采样
            self.time_samples.append({
                'system_time': current_time,
                'game_frame': self.game_frame_count,
                'time_diff': time_diff
            })

            # 保持采样数据在最大长度以内
            if len(self.time_samples) > self.max_time_samples:
                self.time_samples.pop(0)

            # 检测异常的时间跳跃
            if time_diff > 1.0 and len(self.time_samples) > 1:
                expected_diff = 10 * sum(self.frame_times[-10:]) if len(self.frame_times) >= 10 else 0.16
                if time_diff > expected_diff * 2:
                    logging.warning(f"检测到可疑的时间跳跃: 实际差值 {time_diff:.2f}秒, 预期差值 {expected_diff:.2f}秒")
                    self.suspicious_time_jumps += 1

            self.last_system_time = current_time

    def run_checks(self) -> bool:
        """运行所有检查

        Returns:
            bool: 是否所有检查通过
        """
        current_time = time.time()

        # 每分钟执行一次检查
        if current_time - self.last_check_time < self.check_interval:
            return True

        # 记录检查时间间隔
        time_since_last_check = current_time - self.last_check_time
        self.last_check_time = current_time

        # 运行所有检查
        checks = [
            self.check_time_acceleration,
            self.check_stats_anomaly
        ]

        for check in checks:
            try:
                if not check():
                    return False
            except Exception as e:
                logger.error(f"防作弊检查失败: {e}")

        return True

    def check_time_acceleration(self) -> bool:
        """检查时间加速（游戏速度异常）

        Returns:
            bool: 检查是否通过
        """
        # 如果帧数据不足，无法进行检测
        if len(self.frame_times) < 30:  # 至少需要30帧数据才能可靠检测
            return True

        # 计算最近帧的平均间隔时间（秒）
        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        expected_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 60.0

        # 计算系统时间流逝
        current_time = time.time()
        elapsed_system_time = current_time - self.system_time_reference

        # 获取实际游戏内部计时
        game_time_since_reference = self.game_frame_count / 60.0  # 基于帧数的游戏时间估计

        # 避免除零错误
        if elapsed_system_time < 0.001:
            return True

        # 计算时间流逝比率：游戏时间/系统时间
        # 如果比率显著大于1，说明游戏时间流逝快于系统时间，可能存在加速
        time_ratio = game_time_since_reference / elapsed_system_time

        # 记录调试信息
        logger.debug(f"时间加速检测: 系统时间流逝={elapsed_system_time:.2f}秒, "
                    f"游戏时间估计={game_time_since_reference:.2f}秒, "
                    f"比率={time_ratio:.2f}, 平均FPS={expected_fps:.2f}")

        # 如果比率超过阈值，可能存在加速
        if time_ratio > self.time_acceleration_threshold:
            self.acceleration_warnings += 1
            logger.warning(f"检测到可能的时间加速! 比率={time_ratio:.2f}, "
                          f"警告计数={self.acceleration_warnings}/{self.max_acceleration_warnings}")

            # 如果系统时间被回调（时间倒流），这是作弊的明显标志
            if elapsed_system_time < 0:
                logger.error("检测到系统时间被回调！这是明显的作弊行为")
                return False

            # 如果累计警告次数达到阈值，则认定为作弊
            if self.acceleration_warnings >= self.max_acceleration_warnings:
                logger.error(f"确认检测到时间加速作弊! 游戏时间流逝比系统时间快 {time_ratio:.2f} 倍")
                return False

            # 更新参考点，避免持续误报
            self.system_time_reference = current_time
            self.game_frame_count = 0
        else:
            # 正常情况下，逐渐减少警告计数
            self.acceleration_warnings = max(0, self.acceleration_warnings - 0.5)

            # 检测非常低的时间比率，可能表示时间被刻意减慢
            if time_ratio < 0.5 and elapsed_system_time > 10:
                logger.warning(f"检测到游戏时间异常缓慢: 比率={time_ratio:.2f}")
                # 这种情况下我们不认为是作弊，但会重置参考点
                self.system_time_reference = current_time
                self.game_frame_count = 0

            # 定期更新参考点，避免长时间累积误差
            if elapsed_system_time > 300:  # 5分钟
                self.system_time_reference = current_time
                self.game_frame_count = 0

        return True

    def check_stats_anomaly(self) -> bool:
        """检查属性异常

        Returns:
            bool: 检查是否通过
        """
        if not self.game.player:
            return True

        # 获取当前状态
        current_exp = self.game.player.exp
        current_gold = self.game.player.gold
        current_level = self.game.player.level

        # 检查经验值突增
        if self.last_exp > 0:
            exp_gain = current_exp - self.last_exp
            if exp_gain > 1000000:  # 100万经验值增长
                logger.warning(f"检测到异常经验值增长: +{exp_gain}")
                return False

        # 检查金币突增
        if self.last_gold > 0:
            gold_gain = current_gold - self.last_gold
            if gold_gain > 100000:  # 10万金币增长
                logger.warning(f"检测到异常金币增长: +{gold_gain}")
                return False

        # 更新上次状态
        self.last_exp = current_exp
        self.last_gold = current_gold

        return True

class Game:
    def __init__(self):
        """初始化游戏对象"""
        logger.info("初始化游戏对象")

        # 初始化游戏状态
        self.player = None
        self.maps = {}
        self.current_map = None
        self.current_enemy = None
        self.in_battle = False
        self.battle_enabled = True
        self.auto_battle = False
        self.loaded_auto_battle_state = False  # 添加这个属性，用于存档中恢复自动战斗状态
        self.auto_save = False # 初始化 auto_save 属性
        self.next_monster_time = time.time()
        self.player_last_attack_time = time.time()  # 添加玩家上次攻击时间
        self.monster_last_attack_time = time.time()  # 添加怪物上次攻击时间
        self.monster_stun_end_time = 0  # 怪物眩晕结束时间初始化为0

        # 防作弊相关
        self.last_check_time = time.time()  # 上次检查时间，用于检测时间操作

        # --- 新增：战斗准备状态 ---
        self.preparing_battle = False
        self.prepare_battle_start_time = 0
        # --- 结束新增 ---

        # 副本系统已移除
        self.dungeon_completion = 0.0  # 副本完成度

        # 战斗相关
        self.battle_system = BattleSystem()  # 这里不需要传入self作为参数
        self.last_battle_time = 0
        self.last_player_attack = 0
        self.last_enemy_attack = 0
        # --- 新增：初始化伤害数字列表 ---
        self.damage_numbers = []
        # --- 结束新增 ---

        # 初始化自动吃药系统
        self.auto_potion = AutoPotionSystem(self)

        # 初始化防作弊系统
        self.anti_cheat = AntiCheatSystem(self)
        logger.info("初始化防作弊系统")

        # 地图探索相关属性
        self.map_width = 500
        self.map_height = 300
        self.map_active_monsters = []
        self.game_mode = 'idle' # 可选值: 'idle', 'hunting', 'in_battle' (或者直接用 self.in_battle 判断战斗状态)
        self.last_map_update_time = time.time() # 用于地图模式下的增量时间计算
        self.last_monster_replenish_time = 0  # 上次补充怪物的时间
        self.monster_replenish_cooldown = 3.0  # 怪物补充冷却时间（秒）

        # 掉落物品缓存
        self.last_dropped_items = []

        # 地面效果列表
        self.ground_effects = []

        # 战斗日志
        self.battle_logs = []  # 正确属性名为复数形式
        self.max_battle_logs = 100

        # 战斗统计数据
        self.battle_stats = {
            "exp_gained": 0,
            "gold_gained": 0,
            "monsters_killed": 0,
            "battles_count": 0,
            "battle_logs": [],
            "equipment_drops": {
                "普通": 0,
                "精良": 0,
                "稀有": 0,
                "史诗": 0,
                "传说": 0
            },
            "auto_battle_time": 0,
            "battle_start_time": 0
        }

        # 加载游戏配置
        self.config = GameConfig

        # 系统日志
        self.system_log = []  # 添加系统日志列表

        # 自动保存相关
        self.last_save = time.time()

        # 在线模式相关
        self.online_mode = False
        self.api = None
        self.token = None
        self.sync_interval = 60  # 数据同步间隔（秒）
        self.last_sync_time = time.time()

        # 导入API类
        from network.api import GameAPI

        # 加载地图数据
        try:
            self.load_maps()
        except Exception as e:
            logger.error(f"加载地图数据失败: {e}")

        # 确保装备数据已加载，并设置好首饰类型
        if not GameConfig.loaded_equipment:
            logger.info("预加载装备数据")
            GameConfig.load_equipment()

        # 设置战斗系统回调
        self._set_battle_callbacks()

        logger.info("游戏对象初始化完成")

    def load_maps(self):
        """加载地图数据"""
        logger.info("开始加载地图数据")
        try:
            # 从游戏配置中获取地图数据
            maps_data = GameConfig.MAPS_DATA
            if not maps_data:
                logger.warning("地图数据为空")
                return

            # 将地图数据添加到游戏对象并加载图片
            self.maps = {}
            for map_name, map_data in maps_data.items():
                # 加载地图图片
                if "image" in map_data:
                    try:
                        map_data["image_surface"] = resources.load_image(map_data["image"])
                    except Exception as e:
                        logger.error(f"加载地图图片失败: {map_data['image']} - {e}")
                        map_data["image_surface"] = None
                self.maps[map_name] = map_data

            # 设置初始地图
            initial_maps = [name for name, data in self.maps.items()
                           if data.get("level_required", 1) <= 1]
            if initial_maps:
                self.current_map = initial_maps[0]
                logger.info(f"设置初始地图: {self.current_map}")
            else:
                logger.warning("没有找到适合初始玩家的地图")

            logger.info(f"成功加载 {len(self.maps)} 个地图")
        except Exception as e:
            logger.error(f"加载地图数据过程中发生错误: {e}")
            raise

    def _set_battle_callbacks(self):
        """设置战斗系统回调函数"""
        logger.info("设置战斗系统回调")
        if self.battle_system:
            # 设置怪物死亡回调
            self.battle_system.set_monster_death_callback(self._on_monster_death_callback)

            # 设置玩家死亡回调
            self.battle_system.set_player_death_callback(self._on_player_death_callback)

            logger.info("战斗系统回调设置完成")

    def create_new_character(self, character_class="战士", name="", gender="男"):
        """创建新角色

        Args:
            character_class: 角色职业
            name: 角色名称
            gender: 角色性别

        Returns:
            bool: 是否成功创建角色
        """
        try:
            # 检查名称是否为空
            if not name:
                logger.warning("创建角色时未提供名称")
                name = f"{character_class}玩家"  # 提供默认名称

            # 创建新玩家 - 使用延迟导入避免循环导入
            from core.player import Player
            self.player = Player(character_class, name, gender)
            self.player.game = self  # 设置对游戏实例的引用

            # 添加初始装备
            self.player.add_initial_equipment()

            # 记录创建成功
            logger.info(f"成功创建名为 {name} 的{character_class}角色，性别: {gender}")
            self.add_log(f"创建了名为 {name} 的{character_class}角色")

            return True
        except Exception as e:
            logger.error(f"创建角色失败: {e}")
            self.add_log("创建角色失败")
            return False

    def can_enter_map(self, map_name: str) -> bool:
        """检查玩家是否可以进入指定地图

        Args:
            map_name: 地图名称

        Returns:
            bool: 是否可以进入该地图
        """
        # 检查地图是否存在
        if map_name not in self.maps:
            return False

        # 获取地图信息
        map_info = self.maps[map_name]

        # 如果玩家不存在，只允许进入初始地图
        if self.player is None:
            # 默认只允许进入等级要求为1的地图
            required_level = map_info.get("level_required", 1)
            return required_level <= 1

        # 检查玩家等级是否满足要求
        required_level = map_info.get("level_required", 1)
        if self.player.level < required_level:
            return False

        return True

    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
                return

            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # 左键点击
                    mouse_pos = pygame.mouse.get_pos()
                    # 将点击事件传递给UI处理
        if self.ui:
            self.ui.handle_click(mouse_pos)

    def get_map_info(self, map_name):
        """获取地图信息"""
        return GameConfig.MAPS_DATA.get(map_name, {})

    def get_map_monsters(self, map_name: str) -> List[Dict[str, Union[str, int]]]:
        """获取指定地图的怪物列表"""
        try:
            # 检查地图是否存在
            if not GameConfig.MAPS_DATA:
                logger.error("地图配置数据为空")
                return []

            if map_name not in GameConfig.MAPS_DATA:
                logger.error(f"地图 {map_name} 不存在，可用地图: {list(GameConfig.MAPS_DATA.keys())}")
                return []

            # 获取地图数据
            map_data = GameConfig.MAPS_DATA[map_name]
            if not isinstance(map_data, dict):
                logger.error(f"地图 {map_name} 的数据格式错误，应为字典，实际为 {type(map_data)}")
                return []

            # 检查是否有怪物配置
            if "monsters" not in map_data:
                logger.error(f"地图 {map_name} 没有配置怪物")
                return []

            # 获取怪物列表
            monsters = map_data["monsters"]
            if not isinstance(monsters, list):
                logger.error(f"地图 {map_name} 的怪物配置格式错误，应为列表，实际为 {type(monsters)}")
                return []

            if len(monsters) == 0:
                logger.error(f"地图 {map_name} 的怪物配置为空列表")
                return []

            # 验证每个怪物配置
            valid_monsters = []
            for monster in monsters:
                if not isinstance(monster, dict):
                    logger.warning(f"怪物配置格式错误，应为字典，实际为 {type(monster)}")
                    continue

                if "name" not in monster:
                    logger.warning("怪物配置缺少name字段")
                    continue

                monster_name = monster["name"]
                if monster_name not in GameConfig.MONSTER_DATA:
                    logger.warning(f"怪物 {monster_name} 的数据不存在于MONSTER_DATA中")
                    continue

                valid_monsters.append(monster)

            # 检查有效怪物数量
            if len(valid_monsters) == 0:
                logger.error(f"地图 {map_name} 没有有效的怪物配置")
                return []

            logger.debug(f"地图 {map_name} 有 {len(valid_monsters)} 种有效怪物")
            return valid_monsters

        except Exception as e:
            logger.error(f"获取地图 {map_name} 的怪物列表时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def validate_monster_config(self, monster_config: Dict[str, Union[str, int]]) -> Optional[str]:
        if not isinstance(monster_config, dict):
            return "怪物配置必须是字典类型"

        monster_name = monster_config.get("name")
        if not monster_name or not isinstance(monster_name, str):
            return "怪物配置缺少有效的name字段"

        if monster_name not in GameConfig.MONSTER_DATA:
            return f"未找到怪物 {monster_name} 的数据"

        monster_data = GameConfig.MONSTER_DATA[monster_name]
        if not isinstance(monster_data, dict):
            return f"怪物 {monster_name} 的数据格式错误"

        return None

    def create_monster(self, monster_name: str, stats: Tuple[Union[int, float], ...]) -> Optional[Monster]:
        try:
            # 优先使用GameConfig.MONSTER_DATA中的数据
            if monster_name in GameConfig.MONSTER_DATA:
                monster_data = GameConfig.MONSTER_DATA[monster_name]
                monster = Monster(
                    name=monster_name,
                    level=monster_data["level"],
                    hp=monster_data["hp"],
                    defense=monster_data["defense"],
                    magic_defense=monster_data["magic_defense"],
                    attack_range=monster_data["attack_range"],
                    exp=monster_data.get("exp", monster_data["level"] * 10),
                    attack_speed=monster_data["attack_speed"],
                    gold=monster_data.get("gold", monster_data["level"] * 20)  # 添加金币参数
                )
            else:
                # 兼容旧代码，使用传入的stats
                monster = Monster(
                    name=monster_name,
                    level=int(stats[0]),
                    hp=int(stats[1]),
                    defense=int(stats[2]),
                    magic_defense=int(stats[3]),
                    attack_range=stats[4],
                    exp=int(stats[7]) if len(stats) > 7 else int(stats[0]) * 10,  # 使用与加载代码一致的经验值位置
                    attack_speed=float(stats[6]),
                    gold=int(stats[8]) if len(stats) > 8 else int(stats[0]) * 20  # 添加金币参数
                )
            monster.last_attack_time = time.time()
            return monster
        except (ValueError, TypeError) as e:
            logger.error(f"创建怪物 {monster_name} 失败: {e}")
            return None

    def generate_monster(self):
        """生成怪物"""
        try:
            # 添加详细日志
            logger.debug("开始生成怪物，准备检查必要条件...")

            # 检查当前地图
            if not self.current_map:
                logger.error("当前地图未设置，无法生成怪物")
                return None

            logger.debug(f"当前地图: {self.current_map}")

            # 检查GameConfig.MAPS_DATA是否正确初始化
            if not GameConfig.MAPS_DATA:
                logger.error("无法生成怪物：GameConfig.MAPS_DATA为空。")
                # 尝试重新加载地图数据
                GameConfig.load_maps()
                if not GameConfig.MAPS_DATA:
                    logger.error("重新加载地图数据失败，无法生成怪物。")
                    return None
                logger.info(f"重新加载地图数据成功，现在有 {len(GameConfig.MAPS_DATA)} 个地图。")

            # 检查当前地图是否存在于MAPS_DATA中
            if self.current_map not in GameConfig.MAPS_DATA:
                logger.error(f"无法生成怪物：当前地图 {self.current_map} 不存在于MAPS_DATA中。")
                logger.info(f"可用地图: {list(GameConfig.MAPS_DATA.keys())}")
                return None

            # 获取当前地图的怪物列表
            monsters_on_map_config = self.get_map_monsters(self.current_map) # 重命名变量以区分
            if not monsters_on_map_config:
                logger.error(f"地图 {self.current_map} 没有配置怪物，无法生成")
                return None

            logger.debug(f"地图 {self.current_map} 的怪物配置: {len(monsters_on_map_config)} 个怪物种类")

            # 检查地图配置是否有效
            if not isinstance(monsters_on_map_config, list):
                logger.error(f"地图 {self.current_map} 的怪物配置格式错误，应为列表，实际为 {type(monsters_on_map_config)}")
                return None

            if len(monsters_on_map_config) == 0:
                logger.error(f"地图 {self.current_map} 的怪物配置为空列表")
                return None

            # 直接使用地图上配置的所有怪物类型进行权重选择
            available_monsters_config = monsters_on_map_config

            if not available_monsters_config:
                logger.warning(f"地图 {self.current_map} 中没有可用的怪物配置用于生成怪物实例。")
                return None

            logger.debug(f"将从 {len(available_monsters_config)} 种怪物配置中按权重选择生成。")

            # --- 恢复基于权重的随机选择怪物种类的逻辑 ---
            monster_weights = [m_config.get("weight", 1) for m_config in available_monsters_config]
            if not monster_weights or sum(monster_weights) <= 0:
                logger.warning(f"地图 {self.current_map} 的怪物权重配置无效，将使用均等权重。")
                monster_weights = [1] * len(available_monsters_config)

            logger.info(f"怪物权重列表: {monster_weights}")

            # 使用权重随机选择一个怪物配置
            selected_monster_config = random.choices(available_monsters_config, weights=monster_weights, k=1)[0]
            monster_name = selected_monster_config["name"]

            logger.info(f"按权重随机选择的怪物种类: {monster_name}，其配置权重为: {selected_monster_config.get('weight', 1)}")
            # --- 结束恢复基于权重的选择逻辑 ---

            # 确保怪物数据存在
            if not GameConfig.MONSTER_DATA:
                logger.error("怪物数据库为空")
                return None

            # 检查怪物数据
            if monster_name not in GameConfig.MONSTER_DATA:
                logger.error(f"怪物 {monster_name} 的数据不存在于怪物配置中")
                return None

            # 创建怪物实例
            monster_data = GameConfig.MONSTER_DATA[monster_name]

            # 检查monster_data是否为列表
            if isinstance(monster_data, list):
                logger.debug(f"处理列表格式的怪物数据: {monster_name} - {monster_data}")
                # 设置默认值
                level = 1
                hp = 10
                defense = 0
                magic_defense = 0 # 默认魔法防御为0
                attack_range = {"min": 1, "max": 3}
                exp_value = 10
                attack_speed = 1.0
                gold = 20  # 默认金币值

                # 安全地读取数据
                if len(monster_data) > 0:
                    level = monster_data[0]
                if len(monster_data) > 1:
                    hp = monster_data[1]
                if len(monster_data) > 2:
                    defense = monster_data[2]
                if len(monster_data) > 3:
                    magic_defense = monster_data[3] # 正确读取魔法防御 (索引3)

                # 处理攻击范围 (索引4)
                if len(monster_data) > 4 and isinstance(monster_data[4], list) and len(monster_data[4]) >= 2:
                    attack_range = {"min": monster_data[4][0], "max": monster_data[4][1]}

                # 正确读取攻击速度 (索引6)
                if len(monster_data) > 6:
                    attack_speed = monster_data[6]

                # 正确读取经验值 (索引7)
                if len(monster_data) > 7:
                    exp_value = monster_data[7]
                else:
                    # 如果没有明确的经验值，根据等级估算 (或者记录警告)
                    exp_value = level * 10
                    logger.warning(f"怪物 {monster_name} 的列表数据缺少经验值 (索引7)，使用默认值 {exp_value}")

                # 读取金币值 (索引8)
                if len(monster_data) > 8:
                    gold = monster_data[8]
                else:
                    # 如果没有明确的金币值，根据等级估算
                    gold = level * 20
                    logger.debug(f"怪物 {monster_name} 的列表数据缺少金币值 (索引8)，使用默认值 {gold}")

            else:
                # 处理字典格式的怪物数据 (这部分逻辑保持不变)
                logger.debug(f"处理字典格式的怪物数据: {monster_name}")
                level = monster_data.get("level", 1)
                hp = monster_data.get("hp", 10)
                defense = monster_data.get("defense", 0)
                magic_defense = monster_data.get("magic_defense", 0)
                attack_range = monster_data.get("attack_range", {"min": 1, "max": 3})
                exp_value = monster_data.get("exp", level * 10)
                attack_speed = monster_data.get("attack_speed", 1.0)
                gold = monster_data.get("gold", level * 20)  # 添加金币读取

            # 创建Monster实例 (确保传递了magic_defense)
            monster = Monster(
                name=monster_name,
                level=level,
                hp=hp,
                defense=defense,
                magic_defense=magic_defense, # 确保传递了读取到的魔法防御
                attack_range=attack_range,
                exp=exp_value,
                attack_speed=attack_speed,
                gold=gold  # 传递读取到的金币值
            )

            logger.info(f"成功生成怪物: {monster_name}, 等级: {monster.level}, 生命值: {monster.hp}, 经验值: {monster.exp}")
            return monster
        except Exception as e:
            logger.error(f"生成怪物时发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    def change_map(self, map_name):
        """切换地图"""
        if map_name in GameConfig.MAPS_DATA and self.can_enter_map(map_name):
            # 副本系统已移除

            self.current_map = map_name
            self.add_log(f"进入了{map_name}")
            return True
        else:
            self.add_log(f"无法进入{map_name}")
            return False

    def add_log(self, message, is_battle=False):
        """添加日志"""
        # 判断是否为战斗日志或者包含召唤物相关信息的日志
        if is_battle or (hasattr(self, 'player') and self.player and
                         hasattr(self.player, 'summons') and self.player.summons and
                         any(summon.name in message for summon in self.player.summons)) or \
           "造成" in message or "伤害" in message or "使用了技能" in message:  # 添加技能和伤害相关的关键词
            self.battle_logs.append(message)
            if len(self.battle_logs) > self.max_battle_logs:  # 使用类属性限制数量
                self.battle_logs.pop(0)
            # 关键战斗信息才输出到控制台
            if "掉落" in message or "造成" in message:
                logger.info(f"战斗日志: {message}")
        else:
            self.system_log.append(message)
            if len(self.system_log) > 100:  # 限制日志数量
                self.system_log.pop(0)

    def update(self, dt: float):
        """更新游戏状态"""
        current_time = time.time()

        # 检查时间操作
        self.check_time_manipulation()

        # 处理战斗中的逻辑
        if self.game_mode == 'in_battle' or self.in_battle:
            # 战斗模式 (包含准备阶段和实际战斗)
            if self.preparing_battle:
                if current_time - self.prepare_battle_start_time >= 1.0:
                    self.preparing_battle = False
                    # self.in_battle = True # 已在碰撞时设置
                    if self.battle_system and self.current_enemy and self.player:
                        logger.info(f"战斗准备完成，正式开始与 {self.current_enemy.name} 的战斗！")
                        self.battle_system.start_battle(self.player, self.current_enemy)
                        self.battle_stats["battles_count"] += 1
                        if self.auto_battle:
                            self.battle_stats["battle_start_time"] = current_time
                    else:
                        logger.error("战斗准备阶段后未能开始战斗：缺少战斗系统、敌人或玩家。")
                        self.in_battle = False # 重置战斗状态
                        self.game_mode = 'idle' # 或切换回寻怪前的状态
            elif self.in_battle and self.battle_system and self.battle_enabled:
                self.battle_system.update(current_time) # battle_system.update 内部应使用 current_time
                if self.auto_battle and self.battle_stats.get("battle_start_time", 0) > 0:
                    self.battle_stats["auto_battle_time"] += (current_time - self.battle_stats.get("battle_start_time")) # 累计时间
                    self.battle_stats["battle_start_time"] = current_time # 重置开始时间以在下一帧继续计时

        # --- 通用更新逻辑 (无论何种模式都可能需要) ---
        if self.player:
            if not (self.game_mode == 'in_battle' or self.in_battle or self.preparing_battle): # 不在战斗或准备中才恢复
                self.player.update_regeneration(current_time)

            if self.player.is_dead:
                if current_time - self.player.last_death_time >= 10: # 10秒复活
                    self.player.revive()
                    self.add_log("你已复活！")
                    self.game_mode = 'idle' # 复活后回到idle状态
                    self.in_battle = False

            self._update_player_buffs(current_time)
            if self.auto_potion:
                self.auto_potion.update(current_time)
            self.player.update_cooldowns()
            self._update_charmed_monsters()

        if self.auto_save and current_time - self.last_save > 60:  # 每60秒自动保存一次
            self.save_game()
            self.last_save = current_time

        if self.player and self.anti_cheat:
            if not (self.game_mode == 'in_battle' or self.in_battle or self.preparing_battle) or self.battle_enabled:
                if not self.anti_cheat.run_checks():
                    self.apply_cheat_penalty()

        self.damage_numbers = [(num, pos, t - dt, color) for num, pos, t, color in self.damage_numbers if t > 0]
        # AntiCheatSystem 的 update_frame_time 应该由主游戏循环的 dt 调用，而不是这里的 map_delta_time
        # 如果 Game 类的 update 被设计为接收主循环的 dt，那么可以直接传递
        if self.anti_cheat: # 确保 anti_cheat 对象存在
             self.anti_cheat.update_frame_time(dt) # 使用传入的 dt

    def _update_player_buffs(self, current_time):
        """更新玩家的buff效果"""
        if not self.player:
            return

        # 检查玩家是否有buff系统
        if not hasattr(self.player, "buffs"):
            self.player.buffs = {}
            return

        # 获取所有buff
        buffs = self.player.buffs
        if not buffs:
            return

        # 检查是否有过期的buff
        expired_buffs = []
        active_buffs = {}

        # 使用list()创建副本，避免在迭代过程中修改字典
        for buff_id, buff in list(buffs.items()):
            # 计算剩余时间
            elapsed = current_time - buff["start_time"]
            remaining = buff["duration"] - elapsed

            if remaining <= 0:
                # buff已过期
                expired_buffs.append(buff_id)
                logger.info(f"检测到过期Buff: '{buff.get('skill_name', buff_id)}' (ID: {buff_id})")
            else:
                # buff还在生效
                buff_type = buff["type"]
                if buff_type not in active_buffs:
                    active_buffs[buff_type] = []
                active_buffs[buff_type].append({
                    "name": buff.get("skill_name", buff_id),
                    "value": buff.get("value", 0),
                    "remaining": int(remaining)
                })
                logger.debug(f"活跃Buff: {buff.get('skill_name', buff_id)} - 类型: {buff_type}, 值: {buff.get('value', 0)}, 剩余: {int(remaining)}秒")

        # 移除过期的buff
        need_recalculate = False
        for buff_id in expired_buffs:
            if buff_id in buffs:  # 确保buff_id仍然存在
                buff = buffs[buff_id]
                buff_name = buff.get("skill_name", buff_id)

                # 显示buff过期消息
                self.add_log(f"【{buff_name}】效果已结束")
                logger.info(f"移除过期Buff: '{buff_name}' (ID: {buff_id})")

                # 删除buff
                del buffs[buff_id]
                need_recalculate = True

        # 如果有buff移除，需要重新计算玩家属性
        if need_recalculate:
            logger.info("由于Buff过期，重新计算玩家属性")
            self.player.recalculate_stats()

        # 记录当前活跃的buff，便于调试
        if active_buffs:
            buff_types = ", ".join([f"{buff_type}({len(buffs)})" for buff_type, buffs in active_buffs.items()])
            logger.debug(f"当前活跃Buff类型: {buff_types}")

    def _run_anti_cheat_checks(self):
        """运行所有反作弊检查"""
        checks = [
            self.anti_cheat.check_time_acceleration,
            self.anti_cheat.check_stats_anomaly
        ]

        for check in checks:
            try:
                if not check():
                    # 检测到作弊，执行惩罚
                    self.apply_cheat_penalty()
                    return False
            except Exception as e:
                logger.error(f"反作弊检查出错: {e}")

        return True

    def apply_cheat_penalty(self):
        """应用作弊惩罚"""
        logger.warning("检测到作弊行为，开始应用惩罚")

        if not self.player:
            logger.warning("无法应用惩罚：玩家对象不存在")
            return

        # 记录作弊行为在存档中
        self.player.cheat_detected = True
        self.player.cheat_count = getattr(self.player, 'cheat_count', 0) + 1

        # 根据作弊次数应用不同程度的惩罚
        cheat_count = getattr(self.player, 'cheat_count', 1)

        # 惩罚措施可能包括：
        # 1. 减少金币/经验
        # 2. 降低属性
        # 3. 移除道具
        # 4. 提高游戏难度

        penalties = [
            # 第一次惩罚相对较轻
            lambda: self._apply_mild_penalty("首次检测到作弊，这是警告"),
            # 第二次惩罚加重
            lambda: self._apply_moderate_penalty("再次检测到作弊，已进行惩罚"),
            # 第三次以上惩罚严重
            lambda: self._apply_severe_penalty("多次作弊，已进行严重惩罚")
        ]

        # 应用对应等级的惩罚
        penalty_index = min(cheat_count - 1, len(penalties) - 1)
        penalties[penalty_index]()

        # 保存游戏，确保惩罚记录被保存
        self.save_game()

        logger.warning(f"已对作弊行为实施惩罚，当前作弊计数: {cheat_count}")

    def _apply_mild_penalty(self, message):
        """应用轻度惩罚"""
        logger.info("应用轻度惩罚")

        # 显示警告消息
        self.add_log(message)
        self.add_log("检测到游戏加速，已记录警告")

        # 如果玩家在战斗中，强制结束战斗
        if self.in_battle:
            self.end_battle(False)
            self.add_log("由于检测到异常，已结束当前战斗")

    def _apply_moderate_penalty(self, message):
        """应用中度惩罚"""
        logger.info("应用中度惩罚")

        # 显示警告消息
        self.add_log(message)
        self.add_log("再次检测到游戏加速，已实施惩罚")

        # 减少10%的金币
        if self.player.gold > 0:
            penalty_gold = int(self.player.gold * 0.1)
            if penalty_gold > 0:
                self.player.gold -= penalty_gold
                self.add_log(f"已扣除 {penalty_gold} 金币作为惩罚")

        # 如果玩家在战斗中，强制结束战斗
        if self.in_battle:
            self.end_battle(False)
            self.add_log("由于检测到异常，已结束当前战斗")

    def _apply_severe_penalty(self, message):
        """应用严重惩罚"""
        logger.info("应用严重惩罚")

        # 显示警告消息
        self.add_log(message)
        self.add_log("多次检测到游戏加速，已实施严重惩罚")

        # 减少30%的金币
        if self.player.gold > 0:
            penalty_gold = int(self.player.gold * 0.3)
            if penalty_gold > 0:
                self.player.gold -= penalty_gold
                self.add_log(f"已扣除 {penalty_gold} 金币作为惩罚")

        # 减少部分经验
        if self.player.exp > 0:
            penalty_exp = int(self.player.exp * 0.1)  # 减少10%经验
            if penalty_exp > 0:
                self.player.exp -= penalty_exp
                self.add_log(f"已扣除 {penalty_exp} 经验作为惩罚")

        # 如果玩家在战斗中，强制结束战斗
        if self.in_battle:
            self.end_battle(False)
            self.add_log("由于检测到异常，已结束当前战斗")

    def check_time_manipulation(self):
        """检查时间操作，防止玩家修改系统时间"""
        current_time = time.time()

        # 检查系统时间是否被回调（向前调整）
        if hasattr(self, "last_check_time") and current_time < self.last_check_time:
            time_diff = self.last_check_time - current_time
            logger.warning(f"检测到系统时间被回调: {time_diff:.2f}秒")

            # 副本系统已移除

            # 如果不在副本中，应用一般惩罚
            if hasattr(self, 'player') and self.player:
                self.add_log("检测到系统时间异常，已记录")
                self._apply_mild_penalty("检测到系统时间异常")
                return False

        # 更新上次检查时间
        self.last_check_time = current_time
        return True

    def update_battle(self, current_time):
        """更新战斗逻辑"""
        if not self.battle_enabled:
            return

        # 强制测试：每30秒记录一次当前战斗日志状态
        if hasattr(self, '_last_battle_log_check') and (current_time - self._last_battle_log_check) >= 30:
            if hasattr(self, 'battle_logs'):
                logger.info(f"【测试】当前battle_logs状态: 长度={len(self.battle_logs)}, 最后3条: {self.battle_logs[-3:] if len(self.battle_logs) >= 3 else self.battle_logs}")
            self._last_battle_log_check = current_time
        else:
            if not hasattr(self, '_last_battle_log_check'):
                self._last_battle_log_check = current_time

        # 自动战斗：如果没有怪物且自动战斗开启，生成新怪物
        if self.auto_battle and not self.in_battle:
            if current_time >= self.next_monster_time:
                self.start_battle()
            return

        # 如果不在战斗中，直接返回
        if not self.in_battle:
            return

        # 将战斗逻辑完全委托给战斗系统
        if hasattr(self, 'battle_system') and self.battle_system:
            self.battle_system.update(current_time)
        else:
            logger.error("战斗系统不存在，无法处理战斗")
            self.in_battle = False













    def handle_monster_death(self):
        """处理怪物死亡的相关逻辑"""
        try:
            # 检查必要对象
            if not hasattr(self, 'current_enemy') or not hasattr(self, 'player'):
                logger.error("处理怪物死亡时缺少必要对象")
                return

            if not self.current_enemy or not self.player:
                logger.error("处理怪物死亡时对象为空")
                return

            monster = self.current_enemy
            player = self.player
            # 记录击杀
            self.add_log(f"击败了 {monster.name}！", True)
            # 保存旧等级以检查是否升级
            old_level = player.level
            # 检查是否升级（通过比较等级变化）
            new_level = player.level
            if new_level > old_level:
                self.add_log(f"升级了！当前等级：{new_level}", True)

            # 处理掉落物品
            monster_name = monster.name
            monster_level = getattr(monster, "level", 1)
            logger.info(f"怪物 {monster_name}(Lv.{monster_level}) 已被击杀")

            # 获取怪物基础经验和金币
            exp = monster.exp
            gold = getattr(monster, "gold", monster.level * 20)  # 如果不存在gold属性，则基于怪物等级计算默认金币

            # 应用难度系数 - 每级怪物增加10%的经验和金币
            level_multiplier = 1.0 + (monster_level / 10)
            exp = int(exp * level_multiplier)
            gold = int(gold * level_multiplier)

            # 玩家获得经验和金币
            player.gain_exp(exp)
            player.gold += gold

            # 记录战斗统计
            self.battle_stats.setdefault("kills", 0)
            self.battle_stats["kills"] += 1
            self.battle_stats.setdefault("total_exp", 0)
            self.battle_stats["total_exp"] += exp
            self.battle_stats.setdefault("total_gold", 0)
            self.battle_stats["total_gold"] += gold

            # 添加战斗日志
            battle_log = f"击败了 {monster_name}，获得 {exp} 经验和 {gold} 金币"
            self.battle_system.add_log(battle_log)

            # 更新全局任务状态
            if hasattr(self, "quest_manager") and self.quest_manager:
                self.quest_manager.update_monster_kill(monster_name)

            logger.info(f"玩家获得 {exp} 经验和 {gold} 金币")

            # 处理怪物掉落
            if monster_name in GameConfig.DROP_RATES.get("monsters", {}):
                logger.info(f"开始处理怪物 {monster_name} 的掉落...") # 添加日志
                # 获取VIP掉落率加成
                vip_drop_bonus = getattr(player, "get_vip_drop_rate_bonus", lambda: 1.0)()

                # 不再使用怪物等级掉落率加成
                # 获取怪物等级仅用于日志记录
                monster_level = getattr(monster, "level", 1)
                logger.info(f"怪物 {monster_name} 等级: {monster_level}，不再使用等级掉落加成")

                # 全局掉落概率调整因子 - 将整体掉落概率设置为原来的20%
                global_drop_rate_factor = 0.2 # 降低掉落率

                # 检查玩家是否装备了提高爆率的装备（如多宝塔）
                equipment_drop_rate_bonus = 1.0
                for item in player.equipment.values():
                    if item and isinstance(item, dict) and "drop_rate_bonus" in item:
                        bonus = item.get("drop_rate_bonus", 0)
                        equipment_drop_rate_bonus += bonus
                        logger.info(f"装备 {item.get('name', '未知')} 提供爆率加成: +{bonus*100}%")

                # 添加日志记录修正因子
                logger.info(f"掉落修正因子: VIP={vip_drop_bonus:.2f}, 全局={global_drop_rate_factor:.2f}, 装备爆率={equipment_drop_rate_bonus:.2f}")

                # 优先使用怪物自身的 possible_drops，如果为空则回退到全局掉落配置
                drops = getattr(monster, 'possible_drops', None) or GameConfig.DROP_RATES["monsters"][monster_name].get("drops", [])
                logger.info(f"处理怪物掉落: {monster_name}, 原始掉落项数量: {len(drops)}") # 修改日志

                # 修改掉落逻辑，一个怪物最多掉落一件装备
                # 1. 过滤并提取所有装备和非装备掉落项
                equipment_drops = []
                other_drops = []

                for drop in drops:
                    item_name = drop.get("item", "")
                    if not item_name:
                        continue

                    # 获取物品完整信息
                    item = GameConfig.get_item_info(item_name)
                    if not item:
                        logger.warning(f"找不到物品信息: {item_name}")
                        continue

                    # 确保物品有类别信息
                    if "type" not in item or not item["type"]:
                        item_type = self._infer_item_type(item_name)
                        if item_type:
                            item["type"] = item_type

                    # 根据类型分类
                    item_type = item.get("type", "")
                    equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
                    is_equipment = any(eq_type in item_type for eq_type in equipment_types)

                    if is_equipment:
                        equipment_drops.append((drop, item))
                    else:
                        other_drops.append((drop, item))

                logger.info(f"分类结果: 装备类 {len(equipment_drops)} 项, 非装备类 {len(other_drops)} 项") # 添加日志

                # 2. 每件装备单独计算掉落概率
                # 处理装备掉落
                if equipment_drops:
                    logger.info("开始处理装备掉落...") # 添加日志
                    logger.info(f"装备掉落项数量: {len(equipment_drops)}") # 添加日志

                    # 遍历每件装备，单独计算掉落概率
                    for drop, item in equipment_drops:
                        # 获取基础掉落率
                        base_drop_rate = drop.get("rate", 0)

                        # 应用所有修正因子，不再使用等级加成
                        modified_drop_rate = base_drop_rate * vip_drop_bonus * global_drop_rate_factor * equipment_drop_rate_bonus
                        # 确保概率不超过100%
                        modified_drop_rate = min(1.0, modified_drop_rate)

                        logger.info(f"装备 {item.get('name', '未知')} 掉落概率: 基础={base_drop_rate:.6f}, VIP={vip_drop_bonus:.2f}, 全局={global_drop_rate_factor:.2f}, 装备爆率={equipment_drop_rate_bonus:.2f}, 修正后={modified_drop_rate:.6f}")

                        # 单独判定该装备是否掉落
                        rand_roll = random.random()
                        logger.info(f"装备掉落判定: {item.get('name', '未知')}, 随机数={rand_roll:.6f}, 阈值={modified_drop_rate:.6f}")

                        if rand_roll < modified_drop_rate:
                            logger.info(f"装备 {item.get('name', '未知')} 掉落判定成功!")

                            # 应用装备品质生成逻辑
                            processed_item = self._generate_equipment_quality(item.copy())

                            # 记录装备品质统计
                            quality = processed_item.get("tier", "普通")
                            if "equipment_drops" in self.battle_stats:
                                if quality in self.battle_stats["equipment_drops"]:
                                    self.battle_stats["equipment_drops"][quality] += 1
                                else:
                                    self.battle_stats["equipment_drops"][quality] = 1
                                logger.debug(f"更新装备掉落统计: {quality} +1")

                            # 根据品质设置不同的提示信息
                            item_name = processed_item.get("name", drop.get("item", "未知物品"))
                            if quality != "普通":
                                quality_log = {
                                    "精良": "优质的",
                                    "稀有": "稀有的",
                                    "史诗": "极品的",
                                    "传说": "传说中的"
                                }.get(quality, "")
                                item_name = f"{quality_log}{item_name}"

                                # 对于高品质装备，特殊公告
                                if quality in ["史诗", "传说"]:
                                    self.add_log(f"恭喜！获得了{quality}品质装备：{item_name}！", True)

                            # 添加掉落物品到玩家背包
                            if hasattr(self.player, "inventory"):
                                logger.info(f"尝试将装备 {item_name} (品质: {quality}) 添加到背包...") # 添加日志
                                self.player.inventory.append(processed_item)
                                logger.info(f"成功添加装备 {item_name} 到背包。") # 添加日志

                                # 显示掉落信息
                                vip_level = getattr(player, "vip_level", 0)
                                base_drop_chance = drop.get("rate", 0)
                                original_rate = base_drop_chance * 100
                                # 注意：这里的 boosted_rate 计算的是单件装备应用修正后的概率
                                boosted_rate = modified_drop_rate * 100

                                if vip_level > 0:
                                    self.add_log(f"通过VIP{vip_level}加成({original_rate:.2f}% → {boosted_rate:.2f}%)，获得了 {item_name}", True)
                                elif quality == "普通":
                                    self.add_log(f"获得了 {item_name}", True)
                                else:
                                    # 对于非普通品质，即使没有VIP也显示
                                    self.add_log(f"获得了 {item_name}", True)
                            else:
                                logger.warning(f"玩家对象没有背包属性，无法添加装备 {item_name}")

                            logger.info(f"掉落装备处理完成: {item_name}, 品质: {quality}") # 添加日志
                        else:
                            logger.info(f"装备 {item.get('name', '未知')} 掉落判定失败。") # 添加日志
                else:
                    logger.info("该怪物没有配置装备类掉落。") # 添加日志

                # 3. 单独处理每个非装备掉落（技能书、消耗品等）
                logger.info("开始处理非装备掉落...") # 添加日志
                for drop, item in other_drops:
                    try:
                        item_name = item.get("name", drop.get("item", "未知物品"))
                        logger.info(f"处理非装备项: {item_name}") # 添加日志
                        # 应用VIP掉落率加成和全局调整因子，不再使用等级加成
                        base_drop_chance = drop.get("rate", 0)
                        drop_chance = base_drop_chance * vip_drop_bonus * global_drop_rate_factor
                        logger.info(f"  计算掉落概率: 基础={base_drop_chance:.6f}, VIP={vip_drop_bonus:.2f}, 全局={global_drop_rate_factor:.2f}, 修正后={drop_chance:.6f}")

                        # 随机判定
                        rand_roll = random.random()
                        logger.info(f"  非装备掉落判定: 随机数={rand_roll:.6f}, 阈值={drop_chance:.6f}")
                        if rand_roll < drop_chance:
                            logger.info(f"  非装备掉落判定成功: {item_name}")
                            item_type = item.get("type", "")

                            # 技能书处理逻辑
                            if "技能书" in item_type or "skillbook" in item_type.lower():
                                self.add_log(f"获得了技能书：{item_name}", True)

                            # 消耗品处理逻辑
                            elif "消耗品" in item_type or "药水" in item_type or "补给" in item_type:
                                self.add_log(f"获得了消耗品：{item_name}", True)

                            # 添加掉落物品到玩家背包
                            if hasattr(self.player, "inventory"):
                                logger.info(f"  尝试将非装备 {item_name} 添加到背包...") # 添加日志
                                # 尝试调用 add_item 方法处理叠加
                                success = self.player.add_item(item)
                                if success:
                                    logger.info(f"  成功添加非装备 {item_name} 到背包。") # 添加日志
                                else:
                                     logger.warning(f"  添加非装备 {item_name} 到背包失败 (可能背包已满或发生错误)。") # 添加日志

                                # 显示掉落信息，包括VIP加成
                                vip_level = getattr(player, "vip_level", 0)
                                original_rate = base_drop_chance * 100
                                boosted_rate = drop_chance * 100

                                if vip_level > 0:
                                    self.add_log(f"通过VIP{vip_level}加成({original_rate:.2f}% → {boosted_rate:.2f}%)，获得了 {item_name}", True)
                                # else: # 非装备掉落通常不需要额外提示，除非需要特别显示
                                #     self.add_log(f"获得了 {item_name}", True)
                            else:
                                logger.warning(f"玩家对象没有背包属性，无法添加物品 {item_name}")
                                self.add_log(f"获得了 {item_name}，但无法存储", True)
                        else:
                            logger.info(f"  非装备掉落判定失败: {item_name}") # 添加日志
                    except Exception as e:
                        logger.error(f"处理怪物非装备掉落物品时出错: {item_name}, 错误: {e}")
                        import traceback
                        logger.error(traceback.format_exc())
            else:
                logger.info(f"怪物 {monster_name} 没有配置任何掉落。") # 添加日志
        except Exception as e:
            logger.error(f"处理怪物死亡时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            logger.error(traceback.format_exc())

    def handle_player_death(self):
        """处理玩家死亡"""
        if not self.player.is_dead:  # 确保玩家确实死亡
            self.player.is_dead = True
            self.player.last_death_time = time.time()
            self.add_log("你死了！10秒后自动复活")
            self.in_battle = False
            self.current_enemy = None
            self.player.last_attack_time = time.time()  # 重置攻击时间

    def generate_equipment(self):
        """根据玩家等级和地图难度生成装备"""
        try:
            # 安全获取装备数据库
            equipment_db = GameConfig.EQUIPMENT.get("equipment_db", {})
            if not equipment_db:
                logger.warning("装备数据库为空，使用默认配置")
                equipment_db = GameConfig.EQUIPMENT["equipment_db"] = {
                    "新手装备": [{"name": "木剑", "type": "武器", "level": 1, "attack": [2,5]}]
                }

            # 根据地图难度选择装备等级段
            current_diff = GameConfig.MAPS_DATA[self.player.current_map]["difficulty"]
            tier_keys = sorted([k for k in equipment_db.keys() if '装备' in k],
                             key=lambda x: int(x.split('装备')[0].translate(str.maketrans('新手进阶顶级','0246'))))

            selected_tier = "新手装备"
            for tier_name in reversed(tier_keys):
                try:
                    tier_diff = int(tier_name.split('装备')[0].translate(str.maketrans('新手进阶顶级','0246')))
                    if current_diff >= tier_diff:
                        selected_tier = tier_name
                        break
                except (ValueError, IndexError):
                    continue

            # 安全获取装备池
            equip_pool = equipment_db.get(selected_tier, [])
            if not equip_pool:
                logger.warning(f"未找到{selected_tier}的装备，使用新手装备")
                equip_pool = equipment_db.get("新手装备", [])

            base_eq = random.choice(equip_pool).copy() if equip_pool else {"name": "木剑", "type": "武器"}

            # 增强品质系统
            tiers_config = GameConfig.EQUIPMENT.get("tiers", {})
            tiers = list(tiers_config.keys()) or ["普通", "精良", "稀有", "史诗", "传说"]
            default_weights = [80, 15, 10, 4, 1]  # 普通, 精良, 稀有, 史诗, 传说的默认权重

            tier = "普通"  # 默认品质
            quality_bonus_points = 0  # 默认加成点数

            try:
                # 计算实际权重
                weights = [tiers_config.get(t, {}).get("weight", d) for t, d in zip(tiers, default_weights)]

                # 输出详细日志
                logger.info(f"装备品质权重: {dict(zip(tiers, weights))}")
                logger.info(f"权重总和: {sum(weights)}")

                # 使用权重随机选择品质 - 生成一个1-100的随机数进行权重验证
                random_value = random.random() * 100
                logger.info(f"品质随机值: {random_value}")

                # 使用权重随机选择品质
                tier = random.choices(tiers, weights=weights, k=1)[0]

                logger.info(f"选择的装备品质: {tier}")

                # 品质对应的加成点数
                quality_bonus_points = {
                    "普通": 0,
                    "精良": 1,  # 固定1点
                    "稀有": 2,   # 固定2点
                    "史诗": 3,   # 固定3点
                    "传说": 4    # 固定4点
                }.get(tier, 1)
            except (KeyError, ValueError, IndexError) as e:
                logger.error(f"品质系统配置错误: {str(e)}")
                tier = "普通"
                quality_bonus_points = 0

            # 可用属性列表
            available_attrs = []

            # 检查装备有哪些属性可以加成
            if "attack" in base_eq or "atk" in base_eq:
                available_attrs.append("attack")
            if "defense" in base_eq or "def" in base_eq:
                available_attrs.append("defense")
            if "magic_defense" in base_eq or "mdef" in base_eq:
                available_attrs.append("magic_defense")
            if "magic" in base_eq:
                available_attrs.append("magic")
            if "taoism" in base_eq:
                available_attrs.append("taoism")

            # 如果没有可加成属性，添加默认属性
            if not available_attrs:
                if base_eq.get("type", "").lower() in ["武器", "weapon"]:
                    available_attrs = ["attack"]
                elif base_eq.get("type", "").lower() in ["防具", "armor"]:
                    available_attrs = ["defense", "magic_defense"]
                else:
                    available_attrs = ["attack", "defense", "magic_defense", "magic", "taoism"]

            # 随机分配加成点数
            bonus_distribution = {}
            for _ in range(quality_bonus_points):
                attr = random.choice(available_attrs)
                bonus_distribution[attr] = bonus_distribution.get(attr, 0) + 1

            # 应用加成
            for attr, bonus in bonus_distribution.items():
                if attr == "attack":
                    if "attack" in base_eq:
                        if isinstance(base_eq["attack"], list) and len(base_eq["attack"]) >= 2:
                            orig_val = base_eq["attack"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["attack"] = [base_eq["attack"][0] + bonus, base_eq["attack"][1]]
                                logger.info(f"攻击加成(仅下限): {orig_val} -> {base_eq['attack']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["attack"] = [base_eq["attack"][0], base_eq["attack"][1] + bonus]
                                logger.info(f"攻击加成(仅上限): {orig_val} -> {base_eq['attack']}")
                        else:
                            orig_val = base_eq["attack"]
                            base_eq["attack"] = base_eq["attack"] + bonus
                            logger.info(f"攻击加成: {orig_val} -> {base_eq['attack']}")
                    elif "atk" in base_eq:
                        if isinstance(base_eq["atk"], tuple) and len(base_eq["atk"]) >= 2:
                            orig_val = base_eq["atk"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["atk"] = (base_eq["atk"][0] + bonus, base_eq["atk"][1])
                                logger.info(f"攻击加成(仅下限): {orig_val} -> {base_eq['atk']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["atk"] = (base_eq["atk"][0], base_eq["atk"][1] + bonus)
                                logger.info(f"攻击加成(仅上限): {orig_val} -> {base_eq['atk']}")
                        else:
                            orig_val = base_eq["atk"]
                            base_eq["atk"] = base_eq["atk"] + bonus
                            logger.info(f"攻击加成: {orig_val} -> {base_eq['atk']}")
                    else:
                        base_eq["attack"] = [bonus, bonus]
                        logger.info(f"新增攻击属性: {base_eq['attack']}")

                elif attr == "defense":
                    if "defense" in base_eq:
                        if isinstance(base_eq["defense"], list) and len(base_eq["defense"]) >= 2:
                            orig_val = base_eq["defense"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["defense"] = [base_eq["defense"][0] + bonus, base_eq["defense"][1]]
                                logger.info(f"防御加成(仅下限): {orig_val} -> {base_eq['defense']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["defense"] = [base_eq["defense"][0], base_eq["defense"][1] + bonus]
                                logger.info(f"防御加成(仅上限): {orig_val} -> {base_eq['defense']}")
                        else:
                            orig_val = base_eq["defense"]
                            base_eq["defense"] = base_eq["defense"] + bonus
                            logger.info(f"防御加成: {orig_val} -> {base_eq['defense']}")
                    elif "def" in base_eq:
                        if isinstance(base_eq["def"], tuple) and len(base_eq["def"]) >= 2:
                            orig_val = base_eq["def"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["def"] = (base_eq["def"][0] + bonus, base_eq["def"][1])
                                logger.info(f"防御加成(仅下限): {orig_val} -> {base_eq['def']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["def"] = (base_eq["def"][0], base_eq["def"][1] + bonus)
                                logger.info(f"防御加成(仅上限): {orig_val} -> {base_eq['def']}")
                        else:
                            orig_val = base_eq["def"]
                            base_eq["def"] = base_eq["def"] + bonus
                            logger.info(f"防御加成: {orig_val} -> {base_eq['def']}")
                    else:
                        base_eq["defense"] = [bonus, bonus]
                        logger.info(f"新增防御属性: {base_eq['defense']}")

                elif attr == "magic_defense":
                    if "magic_defense" in base_eq:
                        if isinstance(base_eq["magic_defense"], list) and len(base_eq["magic_defense"]) >= 2:
                            orig_val = base_eq["magic_defense"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0] + bonus, base_eq["magic_defense"][1]]
                                logger.info(f"魔法防御加成(仅下限): {orig_val} -> {base_eq['magic_defense']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0], base_eq["magic_defense"][1] + bonus]
                                logger.info(f"魔法防御加成(仅上限): {orig_val} -> {base_eq['magic_defense']}")
                        else:
                            orig_val = base_eq["magic_defense"]
                            base_eq["magic_defense"] = base_eq["magic_defense"] + bonus
                            logger.info(f"魔法防御加成: {orig_val} -> {base_eq['magic_defense']}")
                    elif "mdef" in base_eq:
                        if isinstance(base_eq["mdef"], tuple) and len(base_eq["mdef"]) >= 2:
                            orig_val = base_eq["mdef"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["mdef"] = (base_eq["mdef"][0] + bonus, base_eq["mdef"][1])
                                logger.info(f"魔法防御加成(仅下限): {orig_val} -> {base_eq['mdef']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["mdef"] = (base_eq["mdef"][0], base_eq["mdef"][1] + bonus)
                                logger.info(f"魔法防御加成(仅上限): {orig_val} -> {base_eq['mdef']}")
                        else:
                            orig_val = base_eq["mdef"]
                            base_eq["mdef"] = base_eq["mdef"] + bonus
                            logger.info(f"魔法防御加成: {orig_val} -> {base_eq['mdef']}")
                    else:
                        base_eq["magic_defense"] = [bonus, bonus]

                elif attr == "magic":
                    if "magic" in base_eq:
                        if isinstance(base_eq["magic"], list) and len(base_eq["magic"]) >= 2:
                            orig_val = base_eq["magic"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic"] = [base_eq["magic"][0] + bonus, base_eq["magic"][1]]
                                logger.info(f"魔法加成(仅下限): {orig_val} -> {base_eq['magic']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic"] = [base_eq["magic"][0], base_eq["magic"][1] + bonus]
                                logger.info(f"魔法加成(仅上限): {orig_val} -> {base_eq['magic']}")
                        else:
                            orig_val = base_eq["magic"]
                            base_eq["magic"] = base_eq["magic"] + bonus
                            logger.info(f"魔法加成: {orig_val} -> {base_eq['magic']}")
                    else:
                        base_eq["magic"] = [bonus, bonus]

                elif attr == "taoism":
                    if "taoism" in base_eq:
                        if isinstance(base_eq["taoism"], list) and len(base_eq["taoism"]) >= 2:
                            orig_val = base_eq["taoism"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["taoism"] = [base_eq["taoism"][0] + bonus, base_eq["taoism"][1]]
                                logger.info(f"道术加成(仅下限): {orig_val} -> {base_eq['taoism']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["taoism"] = [base_eq["taoism"][0], base_eq["taoism"][1] + bonus]
                                logger.info(f"道术加成(仅上限): {orig_val} -> {base_eq['taoism']}")
                        else:
                            orig_val = base_eq["taoism"]
                            base_eq["taoism"] = base_eq["taoism"] + bonus
                            logger.info(f"道术加成: {orig_val} -> {base_eq['taoism']}")
                    else:
                        base_eq["taoism"] = [bonus, bonus]

            # 保存品质加成分布信息，用于物品描述
            base_eq["quality_bonus"] = bonus_distribution

            try:
                # 添加品质信息
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": random.randint(*tiers_config.get(tier, {}).get("price", [50, 100]))
                })
            except Exception as e:
                logger.error(f"更新品质信息时出错: {e}")
                # 使用默认值
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": 50
                })

            # 添加特殊属性（仅史诗和传说品质）
            if tier in ["史诗", "传说"]:
                specials = ["暴击", "攻速"]
                if tier == "传说":
                    specials.extend(["生命偷取", "魔法穿透"])

                base_eq["special"] = random.choice(specials)

                if base_eq["special"] == "暴击":
                    base_eq["crit_damage"] = 0.3 if tier == "史诗" else 0.5
                elif base_eq["special"] == "攻速":
                    base_eq["attack_speed"] = 0.2 if tier == "史诗" else 0.3
                elif base_eq["special"] == "生命偷取":
                    base_eq["life_steal"] = 0.1
                elif base_eq["special"] == "魔法穿透":
                    base_eq["magic_penetration"] = 0.2

            # 为武器类装备添加额外的吸血属性几率（任何品质都有低概率获得）
            elif base_eq.get("type") == "武器" and random.random() < 0.05:  # 5%几率
                # 随机决定是百分比吸血还是固定值吸血
                lifesteal_type = random.choice(["百分比", "固定值"])

                if lifesteal_type == "百分比":
                    base_eq["special"] = "吸血"
                    # 根据品质决定吸血值
                    lifesteal_values = {
                        "普通": [1, 3],
                        "精良": [2, 4],
                        "稀有": [3, 5],
                        "史诗": [4, 6],
                        "传说": [5, 8]
                    }
                    min_val, max_val = lifesteal_values.get(tier, [1, 2])
                    base_eq["lifesteal"] = random.randint(min_val, max_val)
                    logger.info(f"生成了带有{base_eq['lifesteal']}%吸血属性的武器: {base_eq['name']}")
                else:
                    base_eq["special"] = "固定吸血"
                    # 根据品质决定固定吸血值
                    flat_lifesteal_values = {
                        "普通": [1, 2],
                        "精良": [2, 3],
                        "稀有": [3, 5],
                        "史诗": [4, 7],
                        "传说": [5, 10]
                    }
                    min_val, max_val = flat_lifesteal_values.get(tier, [1, 2])
                    base_eq["flat_lifesteal"] = random.randint(min_val, max_val)
                    logger.info(f"生成了带有{base_eq['flat_lifesteal']}点固定吸血属性的武器: {base_eq['name']}")

            return base_eq

        except Exception as e:
            logger.error(f"生成装备品质时发生错误: {e}")
            # 确保在发生错误时也返回原装备
            base_eq["tier"] = "普通"
            return base_eq

    def test_quality_generation(self, count=1000):
        """测试装备品质生成系统

        Args:
            count: 测试次数

        Returns:
            dict: 品质分布统计
        """
        logger.info(f"开始品质生成系统测试，样本数: {count}")

        # 创建一个测试装备模板
        test_equipment = {
            "name": "测试装备",
            "type": "武器",
            "level": 10,
            "attack": [10, 15],
            "defense": 5
        }

        quality_counts = {"普通": 0, "精良": 0, "稀有": 0, "史诗": 0, "传说": 0}

        for i in range(count):
            result = self._generate_equipment_quality(test_equipment)
            tier = result.get("tier", "普通")
            quality_counts[tier] += 1

            # 每100次输出一次统计
            if (i+1) % 100 == 0 or i == 0:
                logger.info(f"测试进度: {i+1}/{count}")
                for quality, count in quality_counts.items():
                    percentage = (count / (i+1)) * 100
                    logger.info(f"  {quality}: {count} ({percentage:.2f}%)")

        # 最终统计
        logger.info("品质生成测试结果:")
        for quality, count in quality_counts.items():
            percentage = (count / count) * 100
            logger.info(f"  {quality}: {count} ({percentage:.2f}%)")

        # 计算总体偏差
        expected = {"普通": 0.7273, "精良": 0.1364, "稀有": 0.0909, "史诗": 0.0364, "传说": 0.0091}
        total_diff = 0

        for quality, expected_prob in expected.items():
            actual_prob = quality_counts[quality] / count
            diff = abs(actual_prob - expected_prob)
            total_diff += diff
            logger.info(f"  {quality} 偏差: {diff:.4f}")

        avg_diff = total_diff / len(expected)
        logger.info(f"平均偏差: {avg_diff:.4f}")

        if avg_diff < 0.05:
            logger.info("品质生成系统工作正常")
        else:
            logger.warning("品质生成系统可能存在偏差")

        return quality_counts

    def get_save_game_dir(self) -> Path:
        """获取并确保角色存档目录存在"""
        try:
            # 使用用户主目录下的隐藏文件夹来存储存档
            home_dir = Path.home()
            app_data_dir = home_dir / ".萝卜放置传奇" # 应用数据根目录
            save_dir = app_data_dir / "saves" # 角色存档子目录

            # 创建目录 (如果不存在)
            save_dir.mkdir(parents=True, exist_ok=True)
            return save_dir
        except Exception as e:
            logger.error(f"创建或获取角色存档目录失败: {e}")
            # 如果失败，回退到当前工作目录下的临时目录
            fallback_dir = Path("data") / "saves_fallback"
            fallback_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"角色存档将保存到回退目录: {fallback_dir.resolve()}")
            return fallback_dir

    def save_game(self, filename: str = None) -> bool:
        """保存游戏存档"""
        try:
            logger.info("开始执行存档过程")

            # 检查player对象是否有效
            if not hasattr(self, 'player') or self.player is None:
                logger.error("保存游戏失败：玩家对象为空")
                return False

            # 获取玩家数据
            logger.info("获取玩家数据")
            try:
                player_data = self.player.save_data()
                if not player_data:
                    logger.error("无法获取玩家数据，player.save_data()返回空")
                    return False
            except Exception as e:
                logger.error(f"获取玩家数据时出错: {e}")
                return False

            # 导入版本管理模块
            from core.version import GAME_VERSION

            # 准备存档数据
            data = {
                "version": GAME_VERSION,   # 使用全局游戏版本号
                "player": player_data,
                "current_map": self.current_map,
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),  # 添加时间戳
                "game_state": {
                    "in_battle": getattr(self, "in_battle", False),
                    "battle_enabled": getattr(self, "battle_enabled", True),
                    "auto_battle": getattr(self, "auto_battle", False)
                },
                # 添加战斗统计数据
                "battle_stats": self.battle_stats
            }

            logger.info(f"成功准备存档数据，包含字段: {', '.join(data.keys())}")

            # 获取存档目录
            save_dir = self.get_save_game_dir()

            # 确定文件名
            if filename is None:
                # 使用玩家名称作为默认文件名
                player_name = self.player.name.strip() if self.player.name else "unnamed"
                # 清理文件名，移除无效字符
                safe_player_name = ''.join(c for c in player_name if c.isalnum() or c in ' -_').rstrip()
                if not safe_player_name: safe_player_name = "character"
                filename = f"{safe_player_name}_save.json"

            # 构建完整路径
            save_path = save_dir / filename

            logger.info(f"准备保存存档到: {save_path}")

            # 准备存档数据
            data = self.save_data() # 调用内部方法获取数据字典
            if data is None:
                 logger.error("无法生成存档数据")
                 return False

            # 导入 SafeFileHandler
            from utils.safe_file import SafeFileHandler

            # 使用 SafeFileHandler 进行安全保存
            success = SafeFileHandler.safe_save(str(save_path), data)
            if success:
                logger.info(f"游戏存档成功保存: {save_path}")
                self.last_save = time.time()
                return True
            else:
                logger.error("使用安全保存机制保存存档失败")
                return False

        except Exception as e:
            logger.error(f"保存游戏过程中出现异常: {e}")
            import traceback
            logger.error(f"保存错误详情: {traceback.format_exc()}")
            return False

    def load_game(self, filename=None):
        """加载游戏存档"""
        try:
            # 获取存档目录
            save_dir = self.get_save_game_dir()
            logger.info(f"尝试从目录加载游戏: {save_dir}")

            # 如果没有提供文件名，则加载最新的存档
            if not filename:
                save_files = list(save_dir.glob('*.json')) + list(save_dir.glob('*.save')) # 查找json和save文件
                save_files = [f for f in save_files if f.is_file() and not f.name.endswith('.bak')] # 过滤掉备份文件和目录

                if not save_files:
                    logger.warning("没有找到存档文件")
                    return False

                # 按修改时间排序，最新的在前面
                save_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                load_path = save_files[0]
                logger.info(f"未指定文件名，使用最新存档: {load_path}")
            else:
                # 如果提供了文件名，确保它是 Path 对象，并位于存档目录中
                load_path = Path(filename)
                # 如果 filename 只是基础名称，将其与 save_dir 结合
                if not load_path.is_absolute() and load_path.parent == Path('.'):
                    load_path = save_dir / filename
                # 标准化路径
                load_path = load_path.resolve()
                # 再次检查路径是否在预期的存档目录下 (可选，增加安全性)
                if not str(load_path).startswith(str(save_dir.resolve())):
                     logger.error(f"尝试加载非存档目录中的文件: {load_path}")
                     return False

            # 确保文件存在
            if not load_path.exists() or not load_path.is_file():
                logger.error(f"存档文件不存在或不是文件: {load_path}")
                return False

            logger.info(f"尝试加载存档: {load_path}")

            # 读取文件内容并尝试多种编码解析
            data = None
            try:
                # 首先读取文件原始内容
                file_content = load_path.read_bytes()

                if not file_content:
                    logger.error("存档文件内容为空")
                    return False

                # 尝试使用多种编码加载
                for encoding in ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']:
                    try:
                        text = file_content.decode(encoding)
                        data = json.loads(text)
                        logger.info(f"成功使用{encoding}编码解析存档文件")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用{encoding}编码解析失败")
                        continue
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败，编码:{encoding}, 错误: {e}")
                        continue
                    except Exception as e:
                        logger.warning(f"解析数据时出现其他错误，编码:{encoding}, 错误: {e}")
                        continue

                if not data:
                    logger.error("无法解析存档数据，所有编码尝试都失败")
                    return False

            except Exception as e:
                logger.error(f"读取存档文件时出错: {e}")
                return False

            # 检查数据完整性
            if not isinstance(data, dict):
                logger.error(f"存档数据格式错误：不是字典类型 - {type(data)}")
                return False

            logger.info(f"存档数据包含键: {', '.join(data.keys())}")

            # 检查并修复数据结构 (旧格式转换)
            if 'player' not in data and any(key in data for key in ['character_class', 'level', 'name']):
                 logger.info("检测到旧格式存档数据，转换为新格式")
                 # 初始化player_data字典
                 player_data = {}

                 # 提取玩家相关字段
                 player_keys = ['character_class', 'level', 'name', 'gender', 'hp', 'max_hp',
                               'mp', 'max_mp', 'exp', 'gold', 'strength', 'agility', 'intelligence',
                               'vitality', 'equipments', 'inventory', 'skills']

                 # 将玩家相关字段移至player_data
                 for key in player_keys:
                     if key in data:
                         player_data[key] = data.pop(key)

                 data['player'] = player_data

                 # 写入转换后的数据
                 try:
                     load_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding='utf-8')
                     logger.info("已将旧格式存档转换为新格式并保存")
                 except Exception as e:
                     logger.warning(f"保存修复后的存档失败: {e}")

            # ... (检查必要字段) ...

            # 调用_load_from_data执行实际加载逻辑
            try:
                return self._load_from_data(data)
            except Exception as e:
                logger.error(f"加载数据时出错: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                return False

        except Exception as e:
            logger.error(f"加载游戏时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _load_from_data(self, data):
        """从数据字典加载游戏状态

        参数:
            data: 包含游戏状态的字典

        返回:
            bool: 是否成功加载
        """
        try:
            # 检查数据版本
            save_version = data.get("version", "1.0")
            logger.info(f"加载存档版本: {save_version}")

            # 导入版本管理模块
            from core.version import GAME_VERSION, is_compatible

            # 检查存档版本是否兼容
            if not is_compatible(save_version, GAME_VERSION):
                logger.error(f"存档版本({save_version})高于当前游戏版本({GAME_VERSION})，无法加载")
                # 更新游戏状态，显示错误信息
                self.ui_message = {
                    "title": "版本不兼容",
                    "message": f"存档版本({save_version})高于当前游戏版本({GAME_VERSION})，无法加载。\n请升级游戏或使用较低版本的存档。"
                }
                return False

            # 如果存档版本低于当前版本，更新存档版本
            if save_version != GAME_VERSION:
                logger.info(f"存档版本({save_version})低于当前游戏版本({GAME_VERSION})，将更新存档版本")
                data["version"] = GAME_VERSION

                # 根据不同版本进行数据迁移（如有必要）
                # 例如: if save_version == "1.0": data["new_field"] = default_value

            # 加载玩家数据
            player_data = data.get("player", {})
            if not player_data:
                logger.error("存档中缺少玩家数据")
                return False

            # 创建新玩家 - 使用延迟导入避免循环导入
            from core.player import Player
            self.player = Player(
                character_class=player_data.get("character_class", "战士"),
                name=player_data.get("name", "未知"),
                gender=player_data.get("gender", "男")
            )
            self.player.game = self  # 设置对游戏实例的引用

            # 加载玩家数据
            self.player.load_from_dict(player_data)  # 使用load_from_dict方法

            # 加载当前地图
            loaded_map = data.get("current_map", "比奇省") # 先加载存档中的地图名
            logger.info(f"从存档加载的地图: {loaded_map}")

            # --- 添加：检查加载的地图是否为副本，如果是则强制重置 ---
            try:
                # --- 修改：使用 MAPS_DATA.get() ---
                map_info = GameConfig.MAPS_DATA.get(loaded_map)
                # --- 结束修改 ---
                if map_info and map_info.get("is_dungeon", False):
                    logger.warning(f"玩家存档位置在副本地图 ({loaded_map})，强制重置到 比奇省")
                    self.current_map = "比奇省" # 强制设置为主城
                else:
                    # 如果不是副本，或者找不到地图信息，或者没有is_dungeon标记，则正常使用加载的地图
                    self.current_map = loaded_map
            except Exception as e:
                 # 如果检查过程中出错，也默认设置为比奇省以保证安全
                 logger.error(f"检查地图 {loaded_map} 是否为副本时出错: {e}, 强制设置为 比奇省")
                 self.current_map = "比奇省"
            logger.info(f"加载后最终使用的地图: {self.current_map}")
            # --- 结束添加 ---

            # 加载游戏状态
            game_state = data.get("game_state", {})
            # 重要：先设置为False，稍后再根据需要启用
            self.in_battle = False
            self.battle_enabled = game_state.get("battle_enabled", True)
            # 从存档中获取自动战斗状态，但不立即应用
            saved_auto_battle = game_state.get("auto_battle", False)
            self.auto_battle = False  # 初始设置为False

            # 加载战斗统计
            self.battle_stats = data.get("battle_stats", {
                "exp_gained": 0,
                "gold_gained": 0,
                "monsters_killed": 0,
                "battles_count": 0,
                "battle_logs": [],
                "equipment_drops": {
                    "普通": 0,
                    "精良": 0,
                    "稀有": 0,
                    "史诗": 0,
                    "传说": 0
                },
                "auto_battle_time": 0,
                "battle_start_time": 0
            })

            # 初始化战斗系统
            if not hasattr(self, "battle_system"):
                from core.battle import BattleSystem
                self.battle_system = BattleSystem()  # 这里不需要传入self作为参数
                self._set_battle_callbacks()
            else:
                # 如果已有战斗系统，重置状态
                self.battle_system.battle_active = False
                self.battle_system.auto_battle = False
                self.battle_system.battle_logs = []
                self.current_enemy = None

            # 只有在成功初始化后，才考虑恢复自动战斗状态
            # 在游戏界面初始化时会根据这个状态来正确设置UI
            # 不直接设置auto_battle，而是通过loaded_auto_battle_state标记，让UI来恢复
            # 这样可以确保UI状态和游戏逻辑状态的一致性
            self.loaded_auto_battle_state = saved_auto_battle

            logger.info(f"游戏存档加载成功，自动战斗状态将在界面初始化后恢复：{saved_auto_battle}")
            return True

        except Exception as e:
            logger.error(f"加载游戏数据时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def load_game_from_data(self, data):
        """
        从数据字典加载游戏状态(兼容性方法)

        这是对 _load_from_data 方法的包装器，用于保持向后兼容性

        参数:
            data: 包含游戏状态的字典

        返回:
            bool: 是否成功加载
        """
        logger.info("使用 load_game_from_data 方法（兼容性包装器）")
        try:
            return self._load_from_data(data)
        except Exception as e:
            logger.error(f"使用兼容性方法加载游戏数据时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def reset_game_state(self):
        """重置游戏状态，用于加载存档之前"""
        logger.info("重置游戏状态")
        self.battle_enabled = True
        self.auto_battle = False
        self.in_battle = False
        self.current_enemy = None
        self.battle_logs = []  # 初始化正确的属性名称
        # 保留战斗统计数据和系统日志
        # 设置默认地图
        self.current_map = "比奇省"

        # 重置在线模式状态
        self.online_mode = False
        self.api = None
        self.token = None
        self.last_sync_time = time.time()

    def set_online_mode(self, token=None):
        """设置在线模式

        Args:
            token: 认证令牌，如果为None则关闭在线模式
        """
        if token:
            # 开启在线模式
            from network.api import GameAPI
            self.api = GameAPI("http://localhost:8000")
            self.api.http_client.set_token(token)
            self.token = token
            self.online_mode = True
            self.last_sync_time = time.time()
            logger.info("已开启在线模式")

            # 尝试从服务器获取玩家数据
            try:
                self.sync_with_server()
            except Exception as e:
                logger.error(f"从服务器同步数据失败: {e}")
        else:
            # 关闭在线模式
            self.online_mode = False
            self.api = None
            self.token = None
            logger.info("已关闭在线模式")

    def sync_with_server(self):
        """与服务器同步数据"""
        if not self.online_mode or not self.api:
            logger.warning("未开启在线模式，无法同步数据")
            return False

        try:
            # 获取服务器上的游戏状态
            game_state = self.api.get_game_state()
            logger.info(f"从服务器获取游戏状态: {game_state}")

            # 如果本地没有玩家数据，使用服务器数据
            if not self.player and "player" in game_state:
                # 使用延迟导入避免循环导入
                from core.player import Player
                player_data = game_state["player"]

                # 创建玩家对象
                self.player = Player(
                    character_class=player_data.get("character_class", "战士"),
                    name=player_data.get("name", "未知"),
                    gender=player_data.get("gender", "男")
                )
                self.player.game = self  # 设置对游戏实例的引用

                # 加载玩家数据
                self.player.load_from_dict(player_data)

                # 设置当前地图
                if "game_state" in game_state and "current_map" in game_state["game_state"]:
                    self.current_map = game_state["game_state"]["current_map"]
                else:
                    self.current_map = "比奇省"

                logger.info(f"从服务器加载玩家数据: {self.player.name}, 等级: {self.player.level}")
                return True

            # 如果本地有玩家数据，将本地数据上传到服务器
            elif self.player:
                # 准备玩家数据
                player_data = self.player.save_data()

                # 准备游戏状态数据
                game_state_data = {
                    "current_map": self.current_map,
                    "in_battle": self.in_battle,
                    "auto_battle": self.auto_battle
                }

                # 上传数据到服务器
                result = self.api.update_game_state(player_data, game_state_data)
                logger.info(f"上传数据到服务器: {result}")

                # 更新最后同步时间
                self.last_sync_time = time.time()
                return True

        except Exception as e:
            logger.error(f"与服务器同步数据失败: {e}")
            return False

    def set_auto_battle(self, state: bool) -> bool:
        """集中管理自动战斗状态变更

        Args:
            state: 新的自动战斗状态

        Returns:
            bool: 设置后的状态
        """
        try:
            logger.info(f"设置自动战斗状态: {state}")

            # 检查状态是否变化
            if self.auto_battle == state:
                return state  # 状态未变，直接返回

            # 更新主游戏状态
            self.auto_battle = state

            # 更新战斗系统状态
            if hasattr(self, "battle_system") and self.battle_system:
                self.battle_system.auto_battle = state
                logger.info(f"已同步战斗系统的自动战斗状态: {state}")

            # 更新战斗统计
            current_time = time.time()
            if state:
                # 自动战斗开始
                self.battle_stats["battle_start_time"] = current_time
                logger.info("自动战斗开始，记录开始时间")
            else:
                # 自动战斗结束，更新时长
                if self.battle_stats.get("battle_start_time", 0) > 0:
                    duration = current_time - self.battle_stats.get("battle_start_time", 0)
                    current_battle_time = self.battle_stats.get("auto_battle_time", 0)
                    self.battle_stats["auto_battle_time"] = current_battle_time + duration
                    self.battle_stats["battle_start_time"] = 0
                    logger.info(f"自动战斗结束，更新时长 +{duration:.1f}秒，总时长 {self.battle_stats['auto_battle_time']:.1f}秒")

            return state

        except Exception as e:
            logger.error(f"设置自动战斗状态时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def toggle_auto_battle(self) -> bool:
        """切换自动战斗状态"""
        self.auto_battle = not self.auto_battle

        # 记录自动战斗开始时间
        if self.auto_battle:
            self.battle_stats["battle_start_time"] = time.time()
            logger.info("开启自动战斗")
        else:
            # 自动战斗关闭时，累计战斗时间
            if self.battle_stats.get("battle_start_time", 0) > 0:
                elapsed = time.time() - self.battle_stats.get("battle_start_time", 0)
                # 安全获取auto_battle_time，如果不存在则默认为0
                current_time = self.battle_stats.get("auto_battle_time", 0)
                self.battle_stats["auto_battle_time"] = current_time + elapsed
                self.battle_stats["battle_start_time"] = 0
                logger.info(f"关闭自动战斗，累计战斗时间: {elapsed:.1f}秒")

        self.add_log(f"自动战斗: {'开启' if self.auto_battle else '关闭'}")
        self.battle_system.auto_battle = self.auto_battle

        return self.auto_battle

    def start_battle(self):
        """直接开始战斗，基于时间间隔触发，不再进行地图寻怪。"""
        try:
            logger.info(f"start_battle 被调用，当前游戏模式: {self.game_mode}, 战斗状态: {self.in_battle}")

            if not self.player:
                logger.error("无法开始战斗：玩家对象不存在。")
                return False

            if self.player.is_dead:
                logger.info("玩家已死亡，无法开始战斗。")
                return False

            # 检查是否已在战斗中，避免重复调用
            if self.in_battle or self.preparing_battle:
                logger.info("正在战斗中或准备战斗，无法同时开始新战斗。")
                return False

            # 生成怪物
            monster = self.generate_monster()
            if not monster:
                logger.error("生成怪物失败")
                self.add_log("当前区域无法生成怪物")
                return False

            # 设置当前敌人
            self.current_enemy = monster
            current_time = time.time()

            # 进入战斗准备阶段
            self.preparing_battle = True
            self.prepare_battle_start_time = current_time
            self.game_mode = 'in_battle'

            logger.info(f"遭遇 {self.current_enemy.name}! 进入1秒战斗准备阶段。")
            self.add_log(f"遭遇 {self.current_enemy.name}!")

            # 重置玩家和当前敌人的攻击计时器，确保战斗开始时可以立刻行动
            if self.player:
                self.player.last_attack_time = current_time - self.player.attack_interval - 0.1
            if self.current_enemy:
                self.current_enemy.last_attack_time = current_time - self.current_enemy.attack_interval - 0.1

            return True

        except Exception as e:
            logger.error(f"开始战斗时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 出错时尝试恢复到一个安全状态
            self.game_mode = 'idle'
            self.in_battle = False
            self.preparing_battle = False
            self.current_enemy = None
            return False

    def save_data(self):
        """准备游戏存档数据

        返回:
            dict: 游戏存档数据
        """
        try:
            if self.player is None:
                logger.warning("尝试保存数据，但玩家不存在")
                return None

            # 获取玩家数据（经过反作弊检查）
            player_data = self.player.to_dict()  # 使用to_dict方法

            # 导入版本管理模块
            from core.version import GAME_VERSION

            # 准备存档数据
            data = {
                "version": GAME_VERSION,   # 使用全局游戏版本号
                "player": player_data,
                "current_map": self.current_map,
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),  # 添加时间戳
                "game_state": {
                    "in_battle": self.in_battle,
                    "battle_enabled": self.battle_enabled,
                    "auto_battle": self.auto_battle,
                    "auto_save": self.auto_save  # 添加自动保存设置
                },
                # 添加战斗统计数据
                "battle_stats": self.battle_stats
            }

            logger.info(f"成功准备存档数据，包含字段: {', '.join(data.keys())}")
            return data

        except Exception as e:
            logger.error(f"准备存档数据时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def toggle_fullscreen(self, fullscreen):
        """设置全屏模式

        参数:
            fullscreen: 是否开启全屏
        """
        # 该方法调用UI系统的_toggle_fullscreen方法
        logger.info(f"游戏设置全屏模式: {fullscreen}")
        # 实际切换全屏在GameApp中处理，这里只记录设置

    def set_auto_save(self, auto_save):
        """设置自动保存

        参数:
            auto_save: 是否开启自动保存
        """
        self.auto_save = auto_save
        logger.info(f"自动保存设置为: {'开启' if auto_save else '关闭'}")

    def get_auto_save(self):
        """获取自动保存设置

        返回:
            bool: 当前自动保存设置
        """
        return self.auto_save

    def set_monster_stun(self, duration: float):
        """设置怪物眩晕状态

        参数:
            duration: 眩晕持续时间（秒）
        """
        if not self.current_enemy:
            return

        self.monster_stun_end_time = time.time() + duration
        logger.info(f"怪物被眩晕，持续{duration}秒")

    def is_monster_stunned(self) -> bool:
        """检查怪物是否处于眩晕状态"""
        return time.time() < self.monster_stun_end_time

    def add_combat_effect(self, effect_type, x, y):
        """
        添加战斗效果

        参数:
            effect_type: 效果类型（hit, miss, critical, heal, summon等）
            x: x坐标
            y: y坐标
        """
        try:
            # 记录效果
            logger.debug(f"添加战斗效果: {effect_type} 在位置 ({x}, {y})")

            # 检查效果类型是否支持
            if effect_type not in ["hit", "miss", "critical", "heal", "summon"]:
                logger.warning(f"未知的技能效果类型: {effect_type}")
                return

            # 实际效果显示逻辑应该在这里实现
            # 由于这是UI相关功能，我们只记录日志，不实际显示效果

        except Exception as e:
            logger.error(f"添加战斗效果时发生错误: {e}")

    def add_damage_number(self, damage, x, y, is_critical=False):
        """
        添加伤害数字效果

        参数:
            damage: 伤害值
            x: x坐标
            y: y坐标
            is_critical: 是否暴击
        """
        try:
            # 简单记录效果
            logger.debug(f"添加伤害数字: {damage} {'(暴击)' if is_critical else ''} 在位置 ({x}, {y})")

            # 实际效果显示逻辑应该在这里实现
            # 由于这是UI相关功能，我们只记录日志，不实际显示效果

        except Exception as e:
            logger.error(f"添加伤害数字时发生错误: {e}")

    def _on_player_death_callback(self, player, monster):
        """玩家死亡时的回调函数"""
        logger.info(f"玩家 {player.name} 被 {monster.name} 击杀！")

        # 副本系统已移除

        # 获取复活位置
        revive_location = "比奇省"  # 默认复活地点

        logger.info(f"玩家在地图 {self.current_map} 死亡，将在原地复活")
        revive_location = self.current_map # 普通地图原地复活

        # 记录死亡时间
        player.last_death_time = time.time()
        player.is_dead = True

        # 结束战斗
        # 注意：end_battle现在由battle_system内部调用
        # self.end_battle(False) # 不要在这里调用

        # 添加日志
        self.add_log(f"你被 {monster.name} 击败了!")

        # 副本系统已移除

        # 触发UI更新（例如显示死亡面板）
        if hasattr(self, 'ui_manager') and hasattr(self.ui_manager, 'update_death_panel'):
            self.ui_manager.update_death_panel(player.name, monster.name, revive_location, False)
        else:
            logger.warning("UI管理器不可用，无法更新死亡面板")

        # 可以在这里添加死亡惩罚，比如掉落经验或金币
        # 示例：掉落当前等级经验的10%
        # exp_loss = int(player.exp_required_for_level(player.level) * 0.1)
        # player.exp = max(0, player.exp - exp_loss)
        # self.add_log(f"死亡惩罚：损失了 {exp_loss} 点经验!")

    def _on_monster_death_callback(self, player, monster):
        """怪物死亡回调函数"""
        try:
            if not player or not monster:
                logger.error("怪物死亡回调缺少必要对象")
                return

            # 确保当前怪物对象存在
            if not self.current_enemy:
                self.current_enemy = monster

            # 调用完整的怪物死亡处理逻辑（包括掉落处理）
            self.handle_monster_death()

            # 重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.game_mode = 'idle'

            # 如果是自动战斗模式，设置下次战斗时间
            if self.auto_battle:
                import random
                from core.config import GameConfig

                # 计算下次战斗间隔
                base_interval = random.uniform(GameConfig.BATTLE_INTERVAL_MIN, GameConfig.BATTLE_INTERVAL_MAX)
                interval = base_interval * GameConfig.BATTLE_SPEED_MODIFIER

                self.next_monster_time = time.time() + interval
                logger.info(f"战斗结束，下次战斗将在 {interval:.1f} 秒后开始")

        except Exception as e:
            logger.error(f"处理怪物死亡回调时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 确保在发生错误时也重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.game_mode = 'idle'

    # 修改add_item方法，增加掉落统计功能
    def add_item(self, item, amount=1):
        """向玩家背包添加物品

        参数:
            item: 物品数据
            amount: 数量
        """
        if not item:
            logger.error("尝试添加空物品")
            return False

        if not self.player:
            logger.error("玩家对象为空，无法添加物品")
            return False

        # 添加物品到玩家背包
        success = self.player.add_item(item, amount)

        # 更新战斗统计的装备掉落数据
        if success and item.get("type") in ["武器", "防具", "饰品"]:
            self.update_equipment_drop_stats(item)

        return success

    def add_money(self, amount):
        """增加玩家金钱

        Args:
            amount: 金钱数量
        """
        if self.player:
            # 更新金钱统计
            self.update_gold_stats(amount)
            # 添加到玩家金钱
            self.player.gold += amount
            # 记录日志
            self.add_log(f"获得了 {amount} 金币!")
        return True

    def update(self, dt):
        """更新游戏状态

        Args:
            dt: 时间增量
        """
        # 获取当前时间
        current_time = time.time()

        # 更新防作弊系统
        self.anti_cheat.update_frame_time(dt)

        # 在线模式下，定期与服务器同步数据
        if self.online_mode and current_time - self.last_sync_time > self.sync_interval:
            try:
                self.sync_with_server()
            except Exception as e:
                logger.error(f"自动同步数据失败: {e}")

        # 处理战斗准备阶段
        if self.preparing_battle and self.current_enemy:
            # 检查是否已经过了准备时间（1秒）
            if current_time - self.prepare_battle_start_time >= 1.0:
                logger.info("战斗准备阶段结束，开始正式战斗。")
                self.preparing_battle = False
                self.in_battle = True
                # 初始化战斗系统
                if hasattr(self, 'battle_system') and self.battle_system:
                    self.battle_system.start_battle(self.player, self.current_enemy)
                    logger.info(f"战斗系统初始化完成，开始与 {self.current_enemy.name} 的战斗。")

        # 处理战斗中的逻辑
        elif self.game_mode == 'in_battle' or self.in_battle:
            self.update_battle(current_time)

        # 处理自动战斗逻辑
        elif self.auto_battle and not self.in_battle and not self.preparing_battle:
            if current_time >= self.next_monster_time:
                logger.info("自动战斗触发，开始新战斗。")
                self.start_battle()

        # --- 通用更新逻辑 (无论何种模式都可能需要) ---
        if self.player:
            if not (self.game_mode == 'in_battle' or self.in_battle or self.preparing_battle): # 不在战斗或准备中才恢复
                self.player.update_regeneration(current_time)

            if self.player.is_dead:
                if current_time - self.player.last_death_time >= 10: # 10秒复活
                    self.player.revive()
                    self.add_log("你已复活！")
                    self.game_mode = 'idle' # 复活后回到idle状态
                    self.in_battle = False

            self._update_player_buffs(current_time)
            if self.auto_potion:
                self.auto_potion.update(current_time)
            self.player.update_cooldowns()
            self._update_charmed_monsters()

        if self.auto_save and current_time - self.last_save > 60:  # 每60秒自动保存一次
            self.save_game()
            self.last_save = current_time

        # 更新伤害数字
        self.damage_numbers = [(num, pos, t - dt, color) for num, pos, t, color in self.damage_numbers if t > 0]

    def exit_game(self):
        """退出游戏，保存当前状态"""
        logger.info("退出游戏，准备保存数据...")
        try:
            # 保存游戏数据
            if self.player:
                # 如果在在线模式下，同步数据到服务器
                if self.online_mode:
                    try:
                        self.sync_with_server()
                        logger.info("退出前成功同步数据到服务器")
                    except Exception as e:
                        logger.error(f"退出前同步数据到服务器失败: {e}")

                # 本地保存
                self.save_game()
                logger.info("退出前成功保存游戏数据")
            else:
                logger.warning("退出游戏时玩家数据不存在，无法保存")

            # 重置游戏状态
            self.battle_enabled = False
            self.auto_battle = False
            self.in_battle = False
            self.current_enemy = None

            logger.info("游戏状态已重置，准备退出")

        except Exception as e:
            logger.error(f"退出游戏时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def reset_battle_stats(self):
        """重置战斗统计数据"""
        self.battle_stats = {
            "exp_gained": 0,
            "gold_gained": 0,
            "monsters_killed": 0,
            "battles_count": 0,
            "battle_logs": [],
            "equipment_drops": {
                "普通": 0,
                "精良": 0,
                "稀有": 0,
                "史诗": 0,
                "传说": 0
            },
            "auto_battle_time": 0,
            "battle_start_time": 0
        }
        logger.info("已重置战斗统计数据")

    def update_equipment_drop_stats(self, item):
        """更新装备掉落统计

        Args:
            item: 掉落的装备物品
        """
        if not item or not isinstance(item, dict):
            return

        quality = item.get("tier", "普通")
        if quality in self.battle_stats["equipment_drops"]:
            self.battle_stats["equipment_drops"][quality] += 1
            logger.debug(f"更新装备掉落统计: {quality} +1")

    def update_gold_stats(self, amount):
        """更新金币统计

        Args:
            amount: 获得的金币数量
        """
        self.battle_stats["gold_gained"] += amount
        logger.debug(f"更新金币统计: +{amount}")

    def _generate_equipment_quality(self, base_eq):
        """为装备生成随机品质和属性加成

        Args:
            base_eq: 基础装备数据

        Returns:
            dict: 带有品质和加成的装备数据
        """
        try:
            # 复制装备数据，避免修改原始数据
            base_eq = base_eq.copy()

            # 获取品质配置
            tiers_config = GameConfig.EQUIPMENT.get("tiers", {})
            tiers = list(tiers_config.keys()) or ["普通", "精良", "稀有", "史诗", "传说"]
            default_weights = [160, 34, 20, 5, 1]  # 普通, 精良, 稀有, 史诗, 传说的默认权重

            tier = "普通"  # 默认品质
            quality_bonus_points = 0  # 默认加成点数

            logger.info(f"开始为装备 {base_eq.get('name', '未知')} 生成品质")
            logger.info(f"可用品质配置: {tiers}")
            logger.info(f"默认权重配置: {default_weights}")

            try:
                weights = [tiers_config.get(t, {}).get("weight", d) for t, d in zip(tiers, default_weights)]
                logger.info(f"实际使用的权重: {weights}")

                # 计算和记录每个品质的理论概率
                total_weight = sum(weights)
                theoretical_probs = [w/total_weight for w in weights]
                logger.info(f"品质理论概率: {[f'{t}:{p:.4f}' for t, p in zip(tiers, theoretical_probs)]}")

                # 使用随机选择前记录随机种子状态，便于调试
                import random as rd
                random_value = rd.random()  # 生成[0,1)之间的随机数
                logger.info(f"品质随机值: {random_value:.6f}")

                # 计算累积概率分布
                cumulative_probs = []
                cum_sum = 0
                for w in weights:
                    cum_sum += w
                    cumulative_probs.append(cum_sum/total_weight)
                logger.info(f"累积概率分布: {[f'{t}:{p:.4f}' for t, p in zip(tiers, cumulative_probs)]}")

                # 手动实现概率选择，以便更好地记录过程
                selected_index = 0
                for i, prob in enumerate(cumulative_probs):
                    if random_value < prob:
                        selected_index = i
                        break
                tier = tiers[selected_index]
                logger.info(f"随机选择的品质: {tier}, 索引: {selected_index}")

                # 品质对应的加成点数
                quality_bonus_points = {
                    "普通": 0,
                    "精良": 1,  # 固定1点
                    "稀有": 2,   # 固定2点
                    "史诗": 3,   # 固定3点
                    "传说": 4    # 固定4点
                }.get(tier, 1)
                logger.info(f"品质 {tier} 对应的加成点数: {quality_bonus_points}")
            except (KeyError, ValueError, IndexError) as e:
                logger.error(f"品质系统配置错误: {str(e)}")
                tier = "普通"
                quality_bonus_points = 0

            # 可用属性列表
            available_attrs = []

            # 检查装备有哪些属性可以加成
            if "attack" in base_eq or "atk" in base_eq:
                available_attrs.append("attack")
            if "defense" in base_eq or "def" in base_eq:
                available_attrs.append("defense")
            if "magic_defense" in base_eq or "mdef" in base_eq:
                available_attrs.append("magic_defense")
            if "magic" in base_eq:
                available_attrs.append("magic")
            if "taoism" in base_eq:
                available_attrs.append("taoism")

            # 如果没有可加成属性，添加默认属性
            if not available_attrs:
                if base_eq.get("type", "").lower() in ["武器", "weapon"]:
                    available_attrs = ["attack"]
                elif base_eq.get("type", "").lower() in ["防具", "armor"]:
                    available_attrs = ["defense", "magic_defense"]
                else:
                    available_attrs = ["attack", "defense", "magic_defense", "magic", "taoism"]

            logger.info(f"可用加成属性: {available_attrs}")

            # 随机分配加成点数
            bonus_distribution = {}
            for i in range(quality_bonus_points):
                attr = random.choice(available_attrs)
                bonus_distribution[attr] = bonus_distribution.get(attr, 0) + 1
                logger.info(f"第{i+1}点加成分配给: {attr}")

            logger.info(f"最终加成分布: {bonus_distribution}")

            # 应用加成
            for attr, bonus in bonus_distribution.items():
                if attr == "attack":
                    if "attack" in base_eq:
                        if isinstance(base_eq["attack"], list) and len(base_eq["attack"]) >= 2:
                            orig_val = base_eq["attack"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["attack"] = [base_eq["attack"][0] + bonus, base_eq["attack"][1]]
                                logger.info(f"攻击加成(仅下限): {orig_val} -> {base_eq['attack']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["attack"] = [base_eq["attack"][0], base_eq["attack"][1] + bonus]
                                logger.info(f"攻击加成(仅上限): {orig_val} -> {base_eq['attack']}")
                        else:
                            orig_val = base_eq["attack"]
                            base_eq["attack"] = base_eq["attack"] + bonus
                            logger.info(f"攻击加成: {orig_val} -> {base_eq['attack']}")
                    elif "atk" in base_eq:
                        if isinstance(base_eq["atk"], tuple) and len(base_eq["atk"]) >= 2:
                            orig_val = base_eq["atk"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["atk"] = (base_eq["atk"][0] + bonus, base_eq["atk"][1])
                                logger.info(f"攻击加成(仅下限): {orig_val} -> {base_eq['atk']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["atk"] = (base_eq["atk"][0], base_eq["atk"][1] + bonus)
                                logger.info(f"攻击加成(仅上限): {orig_val} -> {base_eq['atk']}")
                        else:
                            orig_val = base_eq["atk"]
                            base_eq["atk"] = base_eq["atk"] + bonus
                            logger.info(f"攻击加成: {orig_val} -> {base_eq['atk']}")
                    else:
                        base_eq["attack"] = [bonus, bonus]
                        logger.info(f"新增攻击属性: {base_eq['attack']}")

                elif attr == "defense":
                    if "defense" in base_eq:
                        if isinstance(base_eq["defense"], list) and len(base_eq["defense"]) >= 2:
                            orig_val = base_eq["defense"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["defense"] = [base_eq["defense"][0] + bonus, base_eq["defense"][1]]
                                logger.info(f"防御加成(仅下限): {orig_val} -> {base_eq['defense']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["defense"] = [base_eq["defense"][0], base_eq["defense"][1] + bonus]
                                logger.info(f"防御加成(仅上限): {orig_val} -> {base_eq['defense']}")
                        else:
                            orig_val = base_eq["defense"]
                            base_eq["defense"] = base_eq["defense"] + bonus
                            logger.info(f"防御加成: {orig_val} -> {base_eq['defense']}")
                    elif "def" in base_eq:
                        if isinstance(base_eq["def"], tuple) and len(base_eq["def"]) >= 2:
                            orig_val = base_eq["def"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["def"] = (base_eq["def"][0] + bonus, base_eq["def"][1])
                                logger.info(f"防御加成(仅下限): {orig_val} -> {base_eq['def']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["def"] = (base_eq["def"][0], base_eq["def"][1] + bonus)
                                logger.info(f"防御加成(仅上限): {orig_val} -> {base_eq['def']}")
                        else:
                            orig_val = base_eq["def"]
                            base_eq["def"] = base_eq["def"] + bonus
                            logger.info(f"防御加成: {orig_val} -> {base_eq['def']}")
                    else:
                        base_eq["defense"] = [bonus, bonus]
                        logger.info(f"新增防御属性: {base_eq['defense']}")

                elif attr == "magic_defense":
                    if "magic_defense" in base_eq:
                        if isinstance(base_eq["magic_defense"], list) and len(base_eq["magic_defense"]) >= 2:
                            orig_val = base_eq["magic_defense"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0] + bonus, base_eq["magic_defense"][1]]
                                logger.info(f"魔法防御加成(仅下限): {orig_val} -> {base_eq['magic_defense']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic_defense"] = [base_eq["magic_defense"][0], base_eq["magic_defense"][1] + bonus]
                                logger.info(f"魔法防御加成(仅上限): {orig_val} -> {base_eq['magic_defense']}")
                        else:
                            orig_val = base_eq["magic_defense"]
                            base_eq["magic_defense"] = base_eq["magic_defense"] + bonus
                            logger.info(f"魔法防御加成: {orig_val} -> {base_eq['magic_defense']}")
                    elif "mdef" in base_eq:
                        if isinstance(base_eq["mdef"], tuple) and len(base_eq["mdef"]) >= 2:
                            orig_val = base_eq["mdef"]
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["mdef"] = (base_eq["mdef"][0] + bonus, base_eq["mdef"][1])
                                logger.info(f"魔法防御加成(仅下限): {orig_val} -> {base_eq['mdef']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["mdef"] = (base_eq["mdef"][0], base_eq["mdef"][1] + bonus)
                                logger.info(f"魔法防御加成(仅上限): {orig_val} -> {base_eq['mdef']}")
                        else:
                            orig_val = base_eq["mdef"]
                            base_eq["mdef"] = base_eq["mdef"] + bonus
                            logger.info(f"魔法防御加成: {orig_val} -> {base_eq['mdef']}")
                    else:
                        base_eq["magic_defense"] = [bonus, bonus]

                elif attr == "magic":
                    if "magic" in base_eq:
                        if isinstance(base_eq["magic"], list) and len(base_eq["magic"]) >= 2:
                            orig_val = base_eq["magic"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["magic"] = [base_eq["magic"][0] + bonus, base_eq["magic"][1]]
                                logger.info(f"魔法加成(仅下限): {orig_val} -> {base_eq['magic']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["magic"] = [base_eq["magic"][0], base_eq["magic"][1] + bonus]
                                logger.info(f"魔法加成(仅上限): {orig_val} -> {base_eq['magic']}")
                        else:
                            orig_val = base_eq["magic"]
                            base_eq["magic"] = base_eq["magic"] + bonus
                            logger.info(f"魔法加成: {orig_val} -> {base_eq['magic']}")
                    else:
                        base_eq["magic"] = [bonus, bonus]

                elif attr == "taoism":
                    if "taoism" in base_eq:
                        if isinstance(base_eq["taoism"], list) and len(base_eq["taoism"]) >= 2:
                            orig_val = base_eq["taoism"].copy()
                            # 根据品质决定增加上限或下限
                            if tier in ["普通", "精良"]:  # 普通和精良品质增加下限
                                base_eq["taoism"] = [base_eq["taoism"][0] + bonus, base_eq["taoism"][1]]
                                logger.info(f"道术加成(仅下限): {orig_val} -> {base_eq['taoism']}")
                            elif tier in ["稀有", "史诗", "传说"]:  # 稀有、史诗和传说品质增加上限
                                base_eq["taoism"] = [base_eq["taoism"][0], base_eq["taoism"][1] + bonus]
                                logger.info(f"道术加成(仅上限): {orig_val} -> {base_eq['taoism']}")
                        else:
                            orig_val = base_eq["taoism"]
                            base_eq["taoism"] = base_eq["taoism"] + bonus
                            logger.info(f"道术加成: {orig_val} -> {base_eq['taoism']}")
                    else:
                        base_eq["taoism"] = [bonus, bonus]

            # 保存品质加成分布信息，用于物品描述
            base_eq["quality_bonus"] = bonus_distribution

            try:
                # 添加品质信息
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": random.randint(*tiers_config.get(tier, {}).get("price", [50, 100]))
                })
            except Exception as e:
                logger.error(f"更新品质信息时出错: {e}")
                # 使用默认值
                base_eq.update({
                    "tier": tier,
                    "quality": tier,  # 同时设置quality属性，兼容界面显示
                    "equipped": False,
                    "price": 50
                })

            # 添加特殊属性（仅史诗和传说品质）
            if tier in ["史诗", "传说"]:
                specials = ["暴击", "攻速"]
                if tier == "传说":
                    specials.extend(["生命偷取", "魔法穿透"])

                base_eq["special"] = random.choice(specials)

                if base_eq["special"] == "暴击":
                    base_eq["crit_damage"] = 0.3 if tier == "史诗" else 0.5
                elif base_eq["special"] == "攻速":
                    base_eq["attack_speed"] = 0.2 if tier == "史诗" else 0.3
                elif base_eq["special"] == "生命偷取":
                    base_eq["life_steal"] = 0.1
                elif base_eq["special"] == "魔法穿透":
                    base_eq["magic_penetration"] = 0.2

            # 为武器类装备添加额外的吸血属性几率（任何品质都有低概率获得）
            elif base_eq.get("type") == "武器" and random.random() < 0.05:  # 5%几率
                # 随机决定是百分比吸血还是固定值吸血
                lifesteal_type = random.choice(["百分比", "固定值"])

                if lifesteal_type == "百分比":
                    base_eq["special"] = "吸血"
                    # 根据品质决定吸血值
                    lifesteal_values = {
                        "普通": [1, 3],
                        "精良": [2, 4],
                        "稀有": [3, 5],
                        "史诗": [4, 6],
                        "传说": [5, 8]
                    }
                    min_val, max_val = lifesteal_values.get(tier, [1, 2])
                    base_eq["lifesteal"] = random.randint(min_val, max_val)
                    logger.info(f"生成了带有{base_eq['lifesteal']}%吸血属性的武器: {base_eq['name']}")
                else:
                    base_eq["special"] = "固定吸血"
                    # 根据品质决定固定吸血值
                    flat_lifesteal_values = {
                        "普通": [1, 2],
                        "精良": [2, 3],
                        "稀有": [3, 5],
                        "史诗": [4, 7],
                        "传说": [5, 10]
                    }
                    min_val, max_val = flat_lifesteal_values.get(tier, [1, 2])
                    base_eq["flat_lifesteal"] = random.randint(min_val, max_val)
                    logger.info(f"生成了带有{base_eq['flat_lifesteal']}点固定吸血属性的武器: {base_eq['name']}")

            return base_eq

        except Exception as e:
            logger.error(f"生成装备品质时发生错误: {e}")
            # 确保在发生错误时也返回原装备
            base_eq["tier"] = "普通"
            return base_eq

    def _infer_item_type(self, item_name: str) -> str:
        """根据物品名称推断物品类型

        Args:
            item_name: 物品名称

        Returns:
            str: 推断出的物品类型，如果无法推断则返回空字符串
        """
        type_patterns = {
            "武器": ["剑", "刀", "杖", "斧", "弓", "匕首", "锤", "枪"],
            "防具": ["盔甲", "战衣", "布衣", "袍", "护甲", "铠甲"],
            "项链": ["项链", "明珠", "珠子", "护符", "符"],
            "戒指": ["戒指", "指环"],
            "手镯": ["手镯", "护腕", "手套", "臂环"],
            "头盔": ["头盔", "帽子", "头饰", "冠"],
            "技能书": ["技能书", "秘籍", "法术书", "咒语书"],
            "消耗品": ["药水", "药", "丹", "符", "卷轴", "强化石"]
        }

        for item_type, patterns in type_patterns.items():
            if any(pattern in item_name for pattern in patterns):
                return item_type

        return ""

    def end_battle(self, player_victory=False):
        """结束战斗，重置战斗状态

        Args:
            player_victory (bool): 玩家是否获胜

        Returns:
            bool: 操作是否成功
        """
        try:
            logger.info(f"结束战斗: 玩家{' 胜利' if player_victory else ' 失败'}")

            # 如果是自动战斗模式，需要更新累计时长
            if self.auto_battle and self.battle_stats.get("battle_start_time", 0) > 0:
                elapsed = time.time() - self.battle_stats.get("battle_start_time", 0)
                current_time = self.battle_stats.get("auto_battle_time", 0)
                self.battle_stats["auto_battle_time"] = current_time + elapsed
                # 重置开始时间为当前时间，以便继续计时
                self.battle_stats["battle_start_time"] = time.time()
                logger.info(f"战斗结束，更新累计战斗时长 +{elapsed:.1f}秒")

            # 调用战斗系统结束战斗
            if hasattr(self, 'battle_system') and self.battle_system:
                self.battle_system.end_battle(player_victory)

            # 重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.game_mode = 'idle'

            # 如果是自动战斗模式且玩家获胜，设置下次战斗时间
            if self.auto_battle and player_victory:
                import random
                from core.config import GameConfig

                # 计算下次战斗间隔
                base_interval = random.uniform(GameConfig.BATTLE_INTERVAL_MIN, GameConfig.BATTLE_INTERVAL_MAX)
                interval = base_interval * GameConfig.BATTLE_SPEED_MODIFIER

                self.next_monster_time = time.time() + interval
                logger.info(f"战斗结束，下次战斗将在 {interval:.1f} 秒后开始")

            # 重置玩家攻击时间
            if self.player:
                self.player.last_attack_time = time.time()

            return True

        except Exception as e:
            logger.error(f"结束战斗时发生错误: {e}")
            # 确保即使出错也重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.game_mode = 'idle'
            return False

    def get_dungeon_remaining_time(self) -> Tuple[bool, int]:
        """获取当前副本剩余时间

        副本系统已移除，此方法始终返回(False, 0)

        返回:
            Tuple[bool, int]: (是否在副本中, 剩余时间(秒))
        """
        # 副本系统已移除，始终返回不在副本中
        return False, 0

    # is_monster_aware_of_player 方法已移除，因为新的战斗系统不再需要感知检测

    # move_player_on_map 和 move_monsters_on_map 方法已移除，因为新的战斗系统不再需要地图移动

    def _update_charmed_monsters(self):
        """更新被诱惑的怪物状态"""
        if not self.player:
            return

        # 确保player有summons属性
        if not hasattr(self.player, 'summons'):
            logger.info("玩家没有summons属性，初始化为空列表")
            self.player.summons = []

        # 确保player有charmed_monsters属性
        if not hasattr(self.player, 'charmed_monsters'):
            logger.info("玩家没有charmed_monsters属性，初始化为空列表")
            self.player.charmed_monsters = []
            return

        if not self.player.charmed_monsters:
            logger.debug("玩家没有被魅惑的怪物")
            return

        logger.info(f"更新魅惑怪物状态，当前有 {len(self.player.charmed_monsters)} 个")

        active_charmed = []

        for monster in self.player.charmed_monsters[:]:
            # 检查怪物是否死亡或血量为0
            if getattr(monster, 'is_dead', False) or monster.hp <= 0:
                logger.info(f"移除死亡的魅惑怪物: {monster.name}")
                continue

            # 检查魅惑时间是否已过期
            if hasattr(monster, 'charmed_time') and hasattr(monster, 'charmed_duration'):
                # 持续时间为-1表示永久生效，不检查过期
                if monster.charmed_duration == -1:
                    logger.debug(f"魅惑怪物 {monster.name} 持续时间为-1，永久有效")
                    active_charmed.append(monster)
                    continue

            # 未过期的怪物保留
            active_charmed.append(monster)

        # 更新列表
        if len(active_charmed) != len(self.player.charmed_monsters):
            self.player.charmed_monsters = active_charmed
            logger.info(f"更新玩家的被魅惑怪物列表，当前有 {len(active_charmed)} 个")

    def _update_charmed_monsters_battle(self, current_time):
        """更新被诱惑怪物的战斗逻辑"""
        # 检查玩家是否有被诱惑的怪物
        player = self.player
        if not player or not hasattr(player, 'charmed_monsters') or not player.charmed_monsters:
            return

        # 检查是否有目标怪物
        monster = self.current_enemy
        if not monster or monster.hp <= 0:
            return

        # 让每个被诱惑的怪物攻击
        for charmed in player.charmed_monsters[:]:
            # 初始化攻击间隔属性（如果没有）
            if not hasattr(charmed, 'last_attack_time'):
                charmed.last_attack_time = current_time
                continue

            # 检查攻击间隔
            attack_interval = getattr(charmed, 'attack_interval', 2.0)
            if current_time - charmed.last_attack_time < attack_interval:
                        continue

            # 执行攻击
            self._charmed_monster_attack(charmed, monster)
            charmed.last_attack_time = current_time

    def _charmed_monster_attack(self, charmed, enemy):
        """被诱惑的怪物攻击敌人"""
        try:
            # 计算攻击伤害
            damage = charmed.calculate_damage()

            # 应用伤害到敌人
            actual_damage = enemy.take_damage(damage)

            # 添加战斗日志
            self.add_log(f"被魅惑的 {charmed.name} 对 {enemy.name} 造成 {actual_damage} 点伤害！", True)

            # 检查敌人是否死亡
            if enemy.hp <= 0:
                self.handle_monster_death()
        except Exception as e:
            logger.error(f"被魅惑怪物攻击时出错: {e}")



    # replenish_map_monsters 方法已移除，因为新的战斗系统不再需要地图怪物管理

    # check_player_monster_collisions 方法已移除，因为新的战斗系统不再需要碰撞检测

    def set_online_mode(self, token):
        """设置在线模式"""
        self.online_mode = True
        self.token = token

        # 初始化API
        if not self.api:
            from network.api import GameAPI
            self.api = GameAPI("http://localhost:8000")
            self.api.http_client.set_token(token)

        logger.info("已启用在线模式")

    def load_player_from_server(self, player_data):
        """从服务器加载玩家数据"""
        logger.info("从服务器加载玩家数据")
        try:
            # 创建新玩家
            self.load_player_from_data(player_data)
            logger.info(f"成功从服务器加载玩家: {player_data.get('name', '未知')}")
            return True
        except Exception as e:
            logger.error(f"从服务器加载玩家数据失败: {e}")
            return False

    def load_player_from_data(self, player_data):
        """从数据加载玩家

        Args:
            player_data: 玩家数据字典

        Returns:
            bool: 是否成功加载玩家
        """
        logger.info("从数据加载玩家")
        try:
            # 创建新玩家 - 使用延迟导入避免循环导入
            from core.player import Player

            # 提取基本信息
            name = player_data.get("name", "未知玩家")
            job = player_data.get("job", "战士")
            gender = player_data.get("gender", "男")

            # 创建玩家对象
            self.player = Player(job, name, gender)
            self.player.game = self  # 设置对游戏实例的引用

            # 设置玩家属性
            if "level" in player_data:
                self.player.level = player_data["level"]
            if "exp" in player_data:
                self.player.exp = player_data["exp"]
            if "hp" in player_data:
                self.player.hp = player_data["hp"]
            if "max_hp" in player_data:
                self.player.max_hp = player_data["max_hp"]
            if "mp" in player_data:
                self.player.mp = player_data["mp"]
            if "max_mp" in player_data:
                self.player.max_mp = player_data["max_mp"]
            if "attack" in player_data:
                self.player.attack = player_data["attack"]
            if "defense" in player_data:
                self.player.defense = player_data["defense"]
            if "hit" in player_data:
                self.player.hit = player_data["hit"]
            if "dodge" in player_data:
                self.player.dodge = player_data["dodge"]
            if "crit" in player_data:
                self.player.crit = player_data["crit"]

            # 添加初始装备
            self.player.add_initial_equipment()

            logger.info(f"成功加载玩家: {name}, 职业: {job}, 等级: {self.player.level}")
            return True
        except Exception as e:
            logger.error(f"加载玩家数据失败: {e}")
            return False
