[2025-05-22 20:56:35] [WARNING] [main.py:106] 游戏启动
[2025-05-22 20:56:35] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 20:56:35] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 20:56:38] [WARNING] [player.py:1734] 加载技能槽位时发现无效的键: initial
[2025-05-22 21:07:18] [WARNING] [game_screen.py:2736] 技能槽位 1 未绑定技能或不存在
[2025-05-22 21:07:19] [WARNING] [game_screen.py:3960] 使用火球术失败: 没有目标
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:19] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:19] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:20] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:20] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:21] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:21] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:22] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:22] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [WARNING] [game_screen.py:3960] 使用火球术失败: 没有目标
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:24] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:24] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:07:25] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:07:25] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

