[2025-05-22 13:52:35] [WARNING] [main.py:105] 游戏启动
[2025-05-22 13:52:35] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 13:52:35] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 13:53:10] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:53:10] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:53:10] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:53:28] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:53:28] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:53:28] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:53:51] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:53:51] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:53:51] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:54:20] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:54:20] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:54:20] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:54:48] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:54:48] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:54:48] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:55:13] [WARNING] [battle.py:920] 战斗日志: 获得了 6 经验值
[2025-05-22 13:55:13] [WARNING] [battle.py:920] 战斗日志: 获得了 104 金币
[2025-05-22 13:55:13] [WARNING] [battle.py:920] 战斗日志: 掉落装备: [普通]钢手镯
[2025-05-22 13:55:13] [WARNING] [battle.py:920] 战斗日志: 掉落装备: [精良]金项链
[2025-05-22 13:55:19] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:55:19] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:55:19] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:55:43] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:55:43] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:55:43] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:56:18] [WARNING] [battle.py:920] 战斗日志: 获得了 6 经验值
[2025-05-22 13:56:18] [WARNING] [battle.py:920] 战斗日志: 获得了 114 金币
[2025-05-22 13:56:18] [WARNING] [battle.py:920] 战斗日志: 掉落装备: [精良]小手镯
[2025-05-22 13:56:18] [WARNING] [battle.py:920] 战斗日志: 掉落装备: [普通]布衣(女)
[2025-05-22 13:56:23] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:56:23] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:56:23] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:56:47] [ERROR] [battle.py:905] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:56:47] [ERROR] [battle.py:655] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:56:47] [ERROR] [battle.py:657] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 912, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

