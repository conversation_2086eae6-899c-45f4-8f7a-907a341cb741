[2025-05-22 21:14:51] [WARNING] [main.py:106] 游戏启动
[2025-05-22 21:14:51] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 21:14:51] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 21:15:07] [WARNING] [game_screen.py:3960] 使用火球术失败: 没有目标
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:07] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:07] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:08] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:08] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:09] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:09] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 21:15:10] [ERROR] [game_screen.py:3429] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 21:15:10] [ERROR] [game_screen.py:3431] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3317, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

