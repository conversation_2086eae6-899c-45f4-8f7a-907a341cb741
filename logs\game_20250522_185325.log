[2025-05-22 18:53:25] [WARNING] [main.py:106] 游戏启动
[2025-05-22 18:53:26] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 18:53:26] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 18:53:38] [ERROR] [game_screen.py:4478] 刷新 GameScreen UI 状态时出错: 'Game' object has no attribute 'dungeon_time_limit'
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 4468, in _refresh_ui_state
    self._update_dungeon_timer()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 840, in _update_dungeon_timer
    in_dungeon, remaining_time = self.game_manager.get_dungeon_remaining_time()
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\game.py", line 3351, in get_dungeon_remaining_time
    if self.dungeon_time_limit <= 0 or self.dungeon_start_time <= 0:
       ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Game' object has no attribute 'dungeon_time_limit'
[2025-05-22 18:53:38] [ERROR] [main.py:601] 游戏发生未处理的异常: 'Game' object has no attribute 'dungeon_time_limit'
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 597, in main
    game_instance.run() # run() 现在包含清理逻辑
    ~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 348, in run
    self._update(dt)
    ~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 418, in _update
    self.ui_manager.update(dt)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\ui_manager.py", line 385, in update
    self.screens[self.active_screen].update(dt)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 2172, in update
    self._update_dungeon_timer()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 840, in _update_dungeon_timer
    in_dungeon, remaining_time = self.game_manager.get_dungeon_remaining_time()
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\game.py", line 3351, in get_dungeon_remaining_time
    if self.dungeon_time_limit <= 0 or self.dungeon_start_time <= 0:
       ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Game' object has no attribute 'dungeon_time_limit'
