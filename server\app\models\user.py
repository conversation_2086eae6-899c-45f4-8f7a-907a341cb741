from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, List
from datetime import datetime
import re

class UserBase(BaseModel):
    """用户基本信息"""
    username: str
    email: str  # 使用普通字符串而不是EmailStr

    @validator('email')
    def validate_email(cls, v):
        # 简单的邮箱格式验证
        if not re.match(r"[^@]+@[^@]+\.[^@]+", v):
            raise ValueError("Invalid email format")
        return v

class UserCreate(UserBase):
    """创建用户请求模型"""
    password: str

class UserInDB(UserBase):
    """数据库中的用户模型"""
    id: str = Field(alias="_id")
    hashed_password: str
    created_at: datetime = datetime.now()
    last_login: Optional[datetime] = None
    is_active: bool = True
    is_admin: bool = False

class User(UserBase):
    """API响应中的用户模型"""
    id: str
    created_at: datetime
    last_login: Optional[datetime] = None
    is_admin: bool = False

    class Config:
        from_attributes = True

class Token(BaseModel):
    """令牌模型"""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """令牌数据模型"""
    username: Optional[str] = None
