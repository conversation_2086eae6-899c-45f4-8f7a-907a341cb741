import os
import time
import random
import pygame
import json
import traceback  # 添加traceback模块引入
import shutil  # 添加shutil模块引入
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from utils.logger import logger
from utils.resource_manager import resources
from utils.security import DataSecurity
from core.config import GameConfig
from core.player import Player
from core.monster import Monster
from core.battle import BattleSystem

# 添加防作弊系统类
class AntiCheatSystem:
    """简单的防作弊系统"""
    
    def __init__(self, game):
        """初始化防作弊系统
        
        Args:
            game: 游戏实例
        """
        self.game = game
        self.last_check_time = time.time()
        self.last_exp = 0
        self.last_gold = 0
        self.check_interval = 60  # 检查间隔（秒）
        
    def run_checks(self) -> bool:
        """运行所有检查
        
        Returns:
            bool: 是否所有检查通过
        """
        current_time = time.time()
        
        # 每分钟执行一次检查
        if current_time - self.last_check_time < self.check_interval:
            return True
        
        self.last_check_time = current_time
        
        # 运行所有检查
        checks = [
            self.check_time_acceleration,
            self.check_stats_anomaly
        ]
        
        for check in checks:
            try:
                if not check():
                    return False
            except Exception as e:
                logger.error(f"防作弊检查失败: {e}")
        
        return True
    
    def check_time_acceleration(self) -> bool:
        """检查时间加速（游戏速度异常）
        
        Returns:
            bool: 检查是否通过
        """
        # 实际实现中可以检查游戏帧率、时间间隔等
        return True
    
    def check_stats_anomaly(self) -> bool:
        """检查属性异常
        
        Returns:
            bool: 检查是否通过
        """
        if not self.game.player:
            return True
        
        # 获取当前状态
        current_exp = self.game.player.exp
        current_gold = self.game.player.gold
        current_level = self.game.player.level
        
        # 检查经验值突增
        if self.last_exp > 0:
            exp_gain = current_exp - self.last_exp
            if exp_gain > 10000000:  # 1000万经验值增长
                logger.warning(f"检测到异常经验值增长: +{exp_gain}")
                return False
        
        # 检查金币突增
        if self.last_gold > 0:
            gold_gain = current_gold - self.last_gold
            if gold_gain > 1000000:  # 100万金币增长
                logger.warning(f"检测到异常金币增长: +{gold_gain}")
                return False
        
        # 更新上次状态
        self.last_exp = current_exp
        self.last_gold = current_gold
        
        return True

class Game:
    def __init__(self):
        """初始化游戏对象"""
        self.player = None  # 初始化为None，在创建新角色或加载存档时设置
        self.auto_battle = False
        self.battle_enabled = True
        self.last_battle_time = time.time()
        self.last_save = time.time()
        self.running = True
        self.current_page = "status"
        self.battle_log = []
        self.system_log = []
        self.max_log_lines = 6
        self.max_log_history = 100
        self.in_battle = False
        self.current_enemy = None
        self.show_equipment = False
        self.show_map_select = False
        self.ui = None
        # 使用新的地图系统
        self.available_maps = list(GameConfig.MAPS_DATA.keys())
        self.current_map = "比奇省"
        self.maps = GameConfig.MAPS_DATA
        self.inventory_scroll = 0  # 添加背包滚动位置属性
        self.anti_cheat = AntiCheatSystem(self)
        
        # 战斗计时器
        self.last_player_attack = time.time()
        self.last_enemy_attack = time.time()
        
        # 战斗系统初始化
        self.battle_system = BattleSystem()
        
        # 注册战斗系统回调
        self.battle_system.register_callback("on_battle_log", lambda msg: self.add_log(msg, True))
        self.battle_system.register_callback("on_monster_death", self._on_monster_death_callback)
        self.battle_system.register_callback("on_player_death", self._on_player_death_callback)

    def create_new_character(self, character_class="战士", name=""):
        """创建新角色
        
        Args:
            character_class: 角色职业，默认为战士
            name: 玩家名称，默认为空字符串
            
        Returns:
            bool: 是否成功创建角色
        """
        try:
            # 检查名称是否为空
            if not name:
                logger.warning("创建角色时未提供名称")
                name = f"{character_class}玩家"  # 提供默认名称
            
            # 创建新玩家
            self.player = Player(character_class, name)
            self.player.game = self
            
            # 添加初始装备
            self.player.add_initial_equipment()
            
            # 记录创建成功
            logger.info(f"成功创建名为 {name} 的{character_class}角色")
            self.add_log(f"创建了名为 {name} 的{character_class}角色")
            
            return True
        except Exception as e:
            logger.error(f"创建角色失败: {e}")
            self.add_log("创建角色失败")
            return False

    def can_enter_map(self, map_name: str) -> bool:
        """检查玩家是否可以进入指定地图
        
        Args:
            map_name: 地图名称
            
        Returns:
            bool: 是否可以进入该地图
        """
        # 检查地图是否存在
        if map_name not in self.maps:
            return False
            
        # 获取地图信息
        map_info = self.maps[map_name]
        
        # 如果玩家不存在，只允许进入初始地图
        if self.player is None:
            # 默认只允许进入等级要求为1的地图
            required_level = map_info.get("level_required", 1)
            return required_level <= 1
            
        # 检查玩家等级是否满足要求
        required_level = map_info.get("level_required", 1)
        if self.player.level < required_level:
            return False
            
        return True
        
    def handle_events(self):
        """处理游戏事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
                return
                
            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # 左键点击
                    mouse_pos = pygame.mouse.get_pos()
                    # 将点击事件传递给UI处理
        if self.ui:
                        self.ui.handle_click(mouse_pos)

    def get_map_info(self, map_name):
        """获取地图信息"""
        return GameConfig.MAPS_DATA.get(map_name, {})
        
    def get_map_monsters(self, map_name: str) -> List[Dict[str, Union[str, int]]]:
        """获取指定地图的怪物列表"""
        try:
            if map_name not in GameConfig.MAPS_DATA:
                logger.error(f"地图 {map_name} 不存在")
                return []
                
            map_data = GameConfig.MAPS_DATA[map_name]
            if "monsters" not in map_data:
                logger.error(f"地图 {map_name} 没有配置怪物")
                return []
                
            monsters = map_data["monsters"]
            if not isinstance(monsters, list):
                logger.error(f"地图 {map_name} 的怪物配置格式错误")
                return []
                
            # 验证每个怪物配置
            valid_monsters = []
            for monster in monsters:
                if not isinstance(monster, dict):
                    continue
                    
                if "name" not in monster:
                    continue
                    
                if monster["name"] not in GameConfig.MONSTER_DATA:
                    logger.warning(f"怪物 {monster['name']} 的数据不存在")
                    continue
                    
                valid_monsters.append(monster)
                
            if not valid_monsters:
                logger.warning(f"地图 {map_name} 没有有效的怪物配置")
                
            return valid_monsters
            
        except Exception as e:
            logger.error(f"获取地图 {map_name} 的怪物列表时发生错误: {e}")
            return []

    def validate_monster_config(self, monster_config: Dict[str, Union[str, int]]) -> Optional[str]:
        """验证怪物配置
        
        Args:
            monster_config: 怪物配置字典
            
        Returns:
            str: 错误信息，如果配置有效则返回None
        """
        if not isinstance(monster_config, dict):
            return "怪物配置必须是字典类型"
            
        monster_name = monster_config.get("name")
        if not monster_name or not isinstance(monster_name, str):
            return "怪物配置缺少有效的name字段"
            
        if monster_name not in GameConfig.MONSTER_DATA:
            return f"未找到怪物 {monster_name} 的数据"
            
        monster_data = GameConfig.MONSTER_DATA[monster_name]
        if not isinstance(monster_data, dict):
            return f"怪物 {monster_name} 的数据格式错误"
            
        return None

    def create_monster(self, monster_name: str, stats: Tuple[Union[int, float], ...]) -> Optional[Monster]:
        """创建怪物实例
        
        Args:
            monster_name: 怪物名称
            stats: 怪物属性元组
            
        Returns:
            Optional[Monster]: 创建的怪物实例，如果创建失败则返回None
        """
        try:
            # 优先使用GameConfig.MONSTER_DATA中的数据
            if monster_name in GameConfig.MONSTER_DATA:
                monster_data = GameConfig.MONSTER_DATA[monster_name]
                monster = Monster(
                    name=monster_name,
                    level=monster_data["level"],
                    hp=monster_data["hp"],
                    defense=monster_data["defense"],
                    magic_defense=monster_data["magic_defense"],
                    attack_range=monster_data["attack_range"],
                    exp=monster_data.get("exp", monster_data["level"] * 10),
                    attack_speed=monster_data["attack_speed"]
                )
            else:
                # 兼容旧代码，使用传入的stats
                monster = Monster(
                    name=monster_name,
                    level=int(stats[0]),
                    hp=int(stats[1]),
                    defense=int(stats[2]),
                    magic_defense=int(stats[3]),
                    attack_range=stats[4],
                    exp=int(stats[7]) if len(stats) > 7 else int(stats[0]) * 10,  # 使用与加载代码一致的经验值位置
                    attack_speed=float(stats[6])
                )
            monster.last_attack_time = time.time()
            return monster
        except (ValueError, TypeError) as e:
            logger.error(f"创建怪物 {monster_name} 失败: {e}")
            return None
        
    def generate_monster(self):
        """生成怪物"""
        try:
            # 检查当前地图
            if not self.current_map:
                logger.error("当前地图未设置")
                return None
                
            # 获取当前地图的怪物列表
            monsters = self.get_map_monsters(self.current_map)
            if not monsters:
                logger.error(f"地图 {self.current_map} 没有配置怪物")
                return None
            
            # 根据玩家等级选择怪物
            player_level = self.player.level if self.player else 1
            available_monsters = [m for m in monsters if m.get("level", 1) <= player_level + 2]
            
            if not available_monsters:
                logger.warning(f"当前地图没有适合玩家等级 {player_level} 的怪物")
                return None
                
            # 随机选择一个怪物
            monster_config = random.choice(available_monsters)
            monster_name = monster_config["name"]
            
            # 检查怪物数据
            if monster_name not in GameConfig.MONSTER_DATA:
                logger.error(f"怪物 {monster_name} 的数据不存在")
                return None
                
            # 创建怪物实例
            monster_data = GameConfig.MONSTER_DATA[monster_name]
            exp_value = monster_data.get("exp", monster_data["level"] * 10)
            monster = Monster(
                name=monster_name,
                level=monster_data["level"],
                hp=monster_data["hp"],
                defense=monster_data["defense"],
                magic_defense=monster_data["magic_defense"],
                attack_range=monster_data["attack_range"],
                exp=exp_value,  # 优先使用配置中的经验值
                attack_speed=monster_data["attack_speed"]
            )
            
            logger.info(f"生成怪物: {monster_name}, 等级: {monster.level}, 经验值: {monster.exp}")
            return monster
        except Exception as e:
            logger.error(f"生成怪物时发生错误: {e}")
            return None

    def change_map(self, map_name):
        """切换地图"""
        if map_name in GameConfig.MAPS_DATA and self.can_enter_map(map_name):
            self.current_map = map_name
            self.add_log(f"进入了{map_name}")
            return True
        else:
            self.add_log(f"无法进入{map_name}")
            return False

    def add_log(self, message, is_battle=False):
        """添加日志"""
        if is_battle:
            self.battle_log.append(message)
            if len(self.battle_log) > 100:  # 限制日志数量
                self.battle_log.pop(0)
        else:
            self.system_log.append(message)
            if len(self.system_log) > 100:  # 限制日志数量
                self.system_log.pop(0)

    def update(self, dt: float):
        """更新游戏状态"""
        try:
            current_time = time.time()
            
            # 处理玩家自动恢复HP和MP
            if self.player and not self.player.is_dead:
                self.player.update_regeneration(current_time)
            
            # 处理玩家复活
            if self.player and self.player.is_dead:
                death_time = float(self.player.last_death_time)
                if current_time - death_time >= 10.0:  # 10秒后复活
                    logger.info("玩家复活时间已到，开始复活处理")
                    self.player.is_dead = False
                    self.player.hp = self.player.max_hp
                    self.add_log("你已经复活！")
                    self.in_battle = False  # 复活时结束战斗状态
                    self.current_enemy = None
                    self.player.last_attack_time = current_time  # 重置攻击时间
                    logger.info(f"玩家已复活，HP: {self.player.hp}/{self.player.max_hp}")
            
            # 自动保存
            if self.player and current_time - self.last_save >= 300:  # 每5分钟自动保存
                self.save_game()
                self.last_save = current_time
            
            # 更新战斗系统
            if self.player and self.battle_system:
                # 如果在战斗中，更新战斗系统
                if self.in_battle and self.current_enemy:
                    # 确保battle_system的玩家和怪物对象是正确的
                    if not self.battle_system.battle_active:
                        self.battle_system.start_battle(self.player, self.current_enemy)
                    
                    # 更新战斗系统
                    self.battle_system.update(current_time)
                    
                # 使用传统战斗逻辑作为备份
                if self.in_battle and self.current_enemy:
                    self.update_battle(current_time)

        except Exception as e:
            logger.error(f"更新游戏状态时发生错误: {e}")
            logger.error(traceback.format_exc())

    def _run_anti_cheat_checks(self):
        """运行所有反作弊检查"""
        checks = [
            self.anti_cheat.check_time_acceleration,
            self.anti_cheat.check_stats_anomaly
        ]
        
        for check in checks:
            try:
                if not check():
                    return False
            except Exception as e:
                logger.error(f"反作弊检查出错: {e}")
                
        return True

    def update_battle(self, current_time):
        """更新战斗状态"""
        try:
            # 确保 current_time 是有效的数值
            current_time = float(current_time)
            logger.debug(f"更新战斗状态 - 当前时间: {current_time}")
            
            # 玩家死亡检查
            if self.player.is_dead:
                # 检查是否到达复活时间 - 不在这里处理复活，只记录日志
                death_time = float(self.player.last_death_time)
                time_until_revival = 10.0 - (current_time - death_time)
                if time_until_revival <= 0:
                    logger.debug("玩家复活时间已到，将在主循环中处理复活")
                else:
                    logger.debug(f"玩家处于死亡状态，还有 {time_until_revival:.1f} 秒复活")
                return  # 如果玩家处于死亡状态，直接返回不执行后续战斗逻辑

            # 如果不在战斗中或敌人不存在，直接返回
            if not self.in_battle or self.current_enemy is None:
                logger.debug("不在战斗中或敌人不存在，跳过战斗更新")
                return

            # 怪物死亡检查
            if self.current_enemy.is_dead:
                self.handle_monster_death()
                return
                    
            # 玩家攻击
            player_attack_time = float(self.player.last_attack_time)
            time_since_last_attack = current_time - player_attack_time
            attack_interval = float(self.player.attack_interval)
            
            logger.debug(f"玩家攻击检查 - 上次攻击: {player_attack_time}, 间隔: {time_since_last_attack}, 需要间隔: {attack_interval}")
            
            if time_since_last_attack >= attack_interval:
                # 执行玩家攻击
                self.player_attack()
                # 更新最后攻击时间
                self.player.last_attack_time = current_time
                logger.debug("玩家执行攻击")
                
            # 再次检查敌人是否存在和是否死亡（可能在玩家攻击后敌人已死亡或被设为None）
            if self.current_enemy is None:
                logger.debug("敌人已不存在，跳过敌人攻击")
                return
                
            # 怪物攻击
            if not self.current_enemy.is_dead:
                enemy_attack_time = float(self.current_enemy.last_attack_time)
                enemy_time_since_last_attack = current_time - enemy_attack_time
                enemy_attack_interval = float(self.current_enemy.attack_interval)
                
                if enemy_time_since_last_attack >= enemy_attack_interval:
                    self.enemy_attack()
                    # 再次检查敌人是否存在（可能在enemy_attack方法中被设为None）
                    if self.current_enemy is not None:
                        self.current_enemy.last_attack_time = current_time
                        logger.debug("怪物执行攻击")
            
        except (ValueError, TypeError) as e:
            logger.error(f"战斗更新时发生错误: {e}")
            # 发生错误时重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.player.last_attack_time = time.time()
        except Exception as e:
            logger.error(f"战斗更新时发生未知错误: {e}\n{traceback.format_exc()}")
            # 发生错误时重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            self.player.last_attack_time = time.time()

    def player_attack(self):
        """玩家攻击"""
        if not hasattr(self, "player") or not self.player or not hasattr(self, "current_enemy") or not self.current_enemy:
            return False
            
        player = self.player
        monster = self.current_enemy
        
        # 检查是否可以攻击（基于攻击间隔）
        current_time = time.time()
        time_since_last_attack = current_time - player.last_attack_time
        
        # 时间未到，不能攻击
        if time_since_last_attack < player.attack_interval:
            return False
            
        # 更新上次攻击时间
        player.last_attack_time = current_time
        
        # 检查是否命中
        hit_success = self.check_hit(player, monster)
        
        if not hit_success:
            # 未命中
            self.add_log("攻击未命中！")
            self.add_combat_effect("miss", monster.rect.centerx, monster.rect.centery - 30)
            return True  # 攻击进行了，但未命中
            
        # 计算伤害
        damage, is_critical = self.calculate_damage(player, monster)
        
        # 施加伤害
        if hasattr(monster, "hp") and hasattr(monster, "take_damage"):
            if is_critical:
                # 暴击效果
                self.add_combat_effect("critical", monster.rect.centerx, monster.rect.centery - 30)
                self.add_log(f"暴击！对 {monster.name} 造成 {damage} 点伤害")
            else:
                self.add_log(f"对 {monster.name} 造成 {damage} 点伤害")
                
            # 实际伤害可能会被怪物的防御降低
            actual_damage = monster.take_damage(damage)
            self.add_combat_effect("hit", monster.rect.centerx, monster.rect.centery)
            
            # 创建伤害数字效果
            self.add_damage_number(actual_damage, monster.rect.centerx, monster.rect.centery - 20, is_critical)
        
        # 检查怪物是否死亡
        if monster.hp <= 0:
            self.add_log(f"击败了 {monster.name}！")
            self.add_log(f"获得 {monster.exp} 点经验")
            
            # 添加经验
            old_level = player.level
            player.exp += monster.exp
            
            # 检查是否升级
            if player.exp >= player.required_exp:
                player.level_up()
                new_level = player.level
                
                if new_level > old_level:
                    self.add_log(f"升级了！当前等级：{new_level}")
                    
            # 怪物掉落物品处理
            monster_name = monster.name
            if monster_name in GameConfig.DROP_RATES["monsters"]:
                # 获取VIP掉落率加成
                vip_drop_bonus = player.get_vip_drop_rate_bonus()
                
                drops = GameConfig.DROP_RATES["monsters"][monster_name].get("drops", [])
                for drop in drops:
                    # 应用VIP掉落率加成
                    drop_chance = drop.get("rate", 0) * vip_drop_bonus
                    
                    if random.random() < drop_chance:
                        item_name = drop.get("item")
                        if item_name:
                            # 获取物品完整信息
                            item = self.player.get_item_info(item_name)
                            if item:
                                # 确保物品有类别信息
                                if "type" not in item or not item["type"]:
                                    # 尝试从装备数据库中查找物品类别
                                    equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})
                                    item_type = None
                                    
                                    # 根据物品名称特征推断类型
                                    if "剑" in item_name or "刀" in item_name or "杖" in item_name or "斧" in item_name:
                                        item_type = "武器"
                                    elif "盔甲" in item_name or "战衣" in item_name or "布衣" in item_name or "袍" in item_name:
                                        item_type = "防具"
                                    elif "项链" in item_name or "明珠" in item_name or "珠子" in item_name:
                                        item_type = "项链"
                                    elif "戒指" in item_name:
                                        item_type = "戒指"
                                    elif "手镯" in item_name or "护腕" in item_name or "手套" in item_name:
                                        item_type = "手镯"
                                    elif "头盔" in item_name or "帽子" in item_name:
                                        item_type = "头盔"
                                        
                                    if item_type:
                                        item["type"] = item_type
                                
                                # 计算原始掉落几率，用于显示VIP加成信息
                                original_rate = drop.get("rate", 0) * 100
                                boosted_rate = drop_chance * 100
                                
                                # 添加掉落物品到玩家背包
                                self.player.inventory.append(item)
                                
                                # 显示掉落信息，包括VIP加成
                                if player.vip_level > 0:
                                    self.add_log(f"通过VIP{player.vip_level}加成({original_rate:.2f}% → {boosted_rate:.2f}%)，获得了 {item_name}")
                                else:
                                    self.add_log(f"获得了 {item_name}")
                                    
                            else:
                                self.add_log(f"找不到物品信息: {item_name}")
                                
            # 移除死亡的怪物
            self.current_enemy = None
        except Exception as e:
            logger.error(f"玩家攻击时发生错误: {e}\n{traceback.format_exc()}")
            # 发生错误时不重置敌人，让上层函数处理

    def enemy_attack(self):
        """敌人攻击"""
        if self.current_enemy is None or self.current_enemy.is_dead:
            logger.debug("敌人不存在或已死亡，跳过攻击")
            return
            
        try:
            # 获取敌人准确性和玩家敏捷性
            enemy_accuracy = self.current_enemy.accuracy
            player_agility = self.player.agility
            
            # 按照公式计算命中率: 命中率=准确/敏捷
            hit_chance = min(95, max(30, (enemy_accuracy / (player_agility if player_agility > 0 else 1)) * 100))
            
            # 随机判定是否命中
            roll = random.randint(1, 100)
            logger.debug(f"敌人攻击命中判定 - 敌人准确性: {enemy_accuracy}, 玩家敏捷性: {player_agility}")
            logger.debug(f"命中率计算: {enemy_accuracy}/{player_agility}×100% = {hit_chance:.2f}%")
            logger.debug(f"命中判定: 需要 <= {hit_chance:.2f}，实际骰子 = {roll}")
            
            # 再次检查敌人是否存在
            if self.current_enemy is None:
                logger.warning("命中判定后敌人已不存在")
                return
                
            # 判断是否命中
            if roll > hit_chance:
                self.add_log(f"{self.current_enemy.name}的攻击未命中！", True)
                return
            
            # 计算伤害
            damage_result = self.current_enemy.calculate_damage()
            
            # 检查是否是元组（包含暴击信息）
            if isinstance(damage_result, tuple):
                damage = damage_result[0]  # 只取伤害值
            else:
                damage = damage_result
            
            # 应用伤害到玩家
            actual_damage = self.player.take_damage(damage)
            self.add_log(f"{self.current_enemy.name} 对你造成 {actual_damage} 点伤害", True)
            
            # 检查玩家是否死亡
            if self.player.is_dead:
                self.handle_player_death()
                
        except Exception as e:
            logger.error(f"敌人攻击时发生错误: {e}\n{traceback.format_exc()}")
            # 发生错误时不重置战斗状态，让上层函数处理

    def handle_monster_death(self):
        """处理怪物死亡"""
        if self.current_enemy is None:
            logger.warning("尝试处理怪物死亡，但当前没有敌人")
            self.in_battle = False
            return
            
        try:
            exp_gain = self.current_enemy.exp
            gold_gain = random.randint(1, self.current_enemy.level * 10)
            monster_name = self.current_enemy.name  # 保存敌人名称以便在敌人置为None后仍可使用
            
            # 添加日志记录怪物经验值
            logger.info(f"怪物经验值：{monster_name}，经验：{exp_gain}，等级：{self.current_enemy.level}")
            
            self.player.gain_exp(exp_gain)
            self.player.gold += gold_gain
            
            self.add_log(f"击败 {monster_name}，获得 {exp_gain} 点经验值")
            self.add_log(f"获得 {gold_gain} 金币")
            
            # 掉落物品处理
            if monster_name in GameConfig.DROP_RATES.get("monsters", {}):
                drops = GameConfig.DROP_RATES["monsters"][monster_name].get("drops", [])
                for drop in drops:
                    if random.random() < drop.get("rate", 0):
                        item_name = drop.get("item")
                        if item_name:
                            # 获取物品完整信息
                            item = self.player.get_item_info(item_name)
                            if item:
                                # 确保物品有类别信息
                                if "type" not in item or not item["type"]:
                                    # 尝试从装备数据库中查找物品类别
                                    equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})
                                    item_type = None
                                    
                                    # 根据物品名称特征推断类型
                                    if "剑" in item_name or "刀" in item_name or "杖" in item_name or "斧" in item_name:
                                        item_type = "武器"
                                    elif "盔甲" in item_name or "战衣" in item_name or "布衣" in item_name or "袍" in item_name:
                                        item_type = "防具"
                                    elif "项链" in item_name or "明珠" in item_name or "珠子" in item_name:
                                        item_type = "项链"
                                    elif "戒指" in item_name:
                                        item_type = "戒指"
                                    elif "手镯" in item_name or "护腕" in item_name or "手套" in item_name:
                                        item_type = "手镯"
                                    elif "头盔" in item_name or "帽子" in item_name:
                                        item_type = "头盔"
                                    
                                    # 如果还没找到类型，从装备数据库全面搜索
                                    if not item_type:
                                        for category, items in equipment_db.items():
                                            for db_item in items:
                                                if db_item.get("name") == item_name:
                                                    item_type = db_item.get("type")
                                                    if item_type:
                                                        break
                                            if item_type:
                                                break
                                    
                                    # 如果还是没找到类型，设置为"其他"
                                    if not item_type:
                                        item_type = "其他"
                                        
                                    # 设置物品类型
                                    item["type"] = item_type
                                    logger.info(f"为物品 {item_name} 添加类型: {item_type}")
                                
                                # 将物品添加到玩家背包
                                self.player.inventory.append(item)
                                logger.info(f"将物品 {item_name} 添加到玩家背包，类型: {item.get('type', '未知')}")
                                self.add_log(f"获得物品: {item_name} ({item.get('type', '未知')})")
                            else:
                                logger.warning(f"无法获取物品信息: {item_name}")
                                # 创建一个基本物品信息
                                basic_item = {
                                    "name": item_name,
                                    "type": "未知",
                                    "level": 1,
                                    "quality": "普通"
                                }
                                self.player.inventory.append(basic_item)
                                logger.info(f"创建基本物品信息并添加到背包: {item_name}")
                                self.add_log(f"获得物品: {item_name} (未知类型)")
                        
            self.in_battle = False
            self.current_enemy = None
        except Exception as e:
            logger.error(f"处理怪物死亡时发生错误: {e}\n{traceback.format_exc()}")
            # 确保战斗状态被重置
            self.in_battle = False
            self.current_enemy = None

    def handle_player_death(self):
        """处理玩家死亡"""
        if not self.player.is_dead:  # 确保玩家确实死亡
            self.player.is_dead = True
            self.player.last_death_time = time.time()
            self.add_log("你死了！10秒后自动复活")
            self.in_battle = False
            self.current_enemy = None
            self.player.last_attack_time = time.time()  # 重置攻击时间

    def generate_equipment(self):
        """根据玩家等级和地图难度生成装备"""
        try:
            # 安全获取装备数据库
            equipment_db = GameConfig.EQUIPMENT.get("equipment_db", {})
            if not equipment_db:
                logger.warning("装备数据库为空，使用默认配置")
                equipment_db = GameConfig.EQUIPMENT["equipment_db"] = {
                    "新手装备": [{"name": "木剑", "type": "武器", "level": 1, "attack": [2,5]}]
                }

            # 根据地图难度选择装备等级段
            current_diff = GameConfig.MAPS_DATA[self.player.current_map]["difficulty"]
            tier_keys = sorted([k for k in equipment_db.keys() if '装备' in k],
                             key=lambda x: int(x.split('装备')[0].translate(str.maketrans('新手进阶顶级','0246'))))
            
            selected_tier = "新手装备"
            for tier_name in reversed(tier_keys):
                try:
                    tier_diff = int(tier_name.split('装备')[0].translate(str.maketrans('新手进阶顶级','0246')))
                    if current_diff >= tier_diff:
                        selected_tier = tier_name
                        break
                except (ValueError, IndexError):
                    continue
            
            # 安全获取装备池
            equip_pool = equipment_db.get(selected_tier, [])
            if not equip_pool:
                logger.warning(f"未找到{selected_tier}的装备，使用新手装备")
                equip_pool = equipment_db.get("新手装备", [])
            
            base_eq = random.choice(equip_pool).copy() if equip_pool else {"name": "木剑", "type": "武器"}
            
            # 增强品质系统
            tiers_config = GameConfig.EQUIPMENT.get("tiers", {})
            tiers = list(tiers_config.keys()) or ["普通", "稀有", "史诗"]
            default_weights = [70, 25, 5]  # 普通, 稀有, 史诗的默认权重
            
            try:
                weights = [tiers_config.get(t, {}).get("weight", d) for t, d in zip(tiers, default_weights)]
                tier = random.choices(tiers, weights=weights)[0]
                quality_multiplier = GameConfig.EQUIPMENT["tiers"][tier].get("quality_multiplier", 1.0)
            except (KeyError, ValueError) as e:
                logger.error(f"品质系统配置错误: {str(e)}")
                tier = "普通"
                quality_multiplier = 1.0
            
            # 强化基础属性
            for attr in ["atk", "def", "mdef"]:
                if attr in base_eq:
                    if isinstance(base_eq[attr], tuple):
                        base_eq[attr] = (
                            int(base_eq[attr][0] * quality_multiplier),
                            int(base_eq[attr][1] * quality_multiplier)
                        )
                    else:
                        base_eq[attr] = int(base_eq[attr] * quality_multiplier)
            
            # 添加品质信息
            base_eq.update({
                "tier": tier,
                "equipped": False,
                "price": random.randint(*GameConfig.EQUIPMENT["tiers"][tier].get("price", [50, 100]))
            })
            
            # 添加特殊属性（仅史诗品质）
            if tier == "史诗":
                base_eq["special"] = random.choice([ "暴击", "攻速"])
                if base_eq["special"] == "暴击":
                    base_eq["crit_damage"] = 0.3
                elif base_eq["special"] == "攻速":
                    base_eq["attack_speed"] = 0.2
                
            return base_eq

        except (KeyError, ValueError) as e:
            print(f"装备生成错误: {str(e)}，使用默认配置")
            return {
                "name": "木剑",
                "type": "武器",
                "level": 1,
                "atk": (3,5),
                "tier": "普通",
                "price": 10,
                "durability": 10
            }
        
    def save_game(self, filename: str = None) -> bool:
        """保存游戏存档
        
        Args:
            filename: 可选的存档文件名，如果为None则使用默认文件名
            
        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info("开始执行简化版存档过程")
            
            # 检查player对象是否有效
            if self.player is None:
                logger.error("保存游戏失败：玩家对象为空")
                return False
                
            # 获取玩家数据 - 这个方法已经被优化，总是返回字典
            logger.info("获取玩家数据")
            player_data = self.player.save_data()
            if player_data is None:
                logger.error("无法获取玩家数据，player.save_data()返回None")
                return False
                
            # 准备存档数据
            data = {
                "version": "1.2",   # 更新版本号
                "player": player_data,
                "current_map": self.current_map,
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),
                "game_state": {
                    "in_battle": getattr(self, "in_battle", False),
                    "battle_enabled": getattr(self, "battle_enabled", True),
                    "auto_battle": getattr(self, "auto_battle", False)
                }
            }
            
            # 确保存档目录存在
            save_dir = "data/saves"
            os.makedirs(save_dir, exist_ok=True)
            
            # 使用玩家名称作为文件名前缀
            player_name = self.player.name.strip() if self.player.name else "unnamed"
            player_name = ''.join(c if c.isalnum() or c in ' -_' else '_' for c in player_name)
            file_path = os.path.join(save_dir, f"{player_name}_save.json")
            
            # 直接写入文件 - 简化流程
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"成功保存游戏: {file_path}")
                return True
            except Exception as write_err:
                logger.error(f"写入存档文件失败: {write_err}")
                
                # 尝试使用备用文件名
                try:
                    backup_file_path = os.path.join(save_dir, f"backup_{int(time.time())}.json")
                    with open(backup_file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    logger.info(f"使用备用文件名保存成功: {backup_file_path}")
                    return True
                except Exception as backup_err:
                    logger.error(f"使用备用文件名保存也失败: {backup_err}")
                    return False
                    
        except Exception as e:
            logger.error(f"保存游戏过程中出现异常: {e}")
            import traceback
            logger.error(f"保存错误详情: {traceback.format_exc()}")
            return False

    def load_game(self, filename=None):
        """加载游戏存档
        
        参数:
            filename: 存档文件路径，如果为None则加载最新的存档
            
        返回:
            bool: 是否成功加载
        """
        # 构建存档目录路径
        save_dir = os.path.join("data", "saves")
        os.makedirs(save_dir, exist_ok=True)
        
        logger.info(f"尝试加载游戏，filename: {filename}")
        
        # 如果没有提供文件名，则加载最新的存档
        if not filename:
            save_files = []
            
            # 查找所有.save和.json文件
            for f in os.listdir(save_dir):
                f_path = os.path.join(save_dir, f)
                if (f.endswith(".save") or f.endswith(".json")) and os.path.isfile(f_path):
                    save_files.append(f_path)
            
            if not save_files:
                logger.warning("没有找到存档文件")
                return False
            
            # 按修改时间排序，最新的在前面
            save_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            filename = save_files[0]
            logger.info(f"未指定文件名，使用最新存档: {filename}")
        
        # 确保提供的filename是有效的
        filename = os.path.normpath(filename)
        logger.info(f"尝试加载存档: {filename}")
        
        # 检查文件是否存在
        if not os.path.exists(filename):
            logger.error(f"存档文件不存在: {filename}")
            return False
            
        # 重置游戏状态
        self.reset_game_state()
            
        # 读取存档文件
        data = None
        save_file_data = None
        
        try:
            with open(filename, "rb") as f:
                save_file_data = f.read()
        except Exception as e:
            logger.error(f"读取存档文件时出错: {e}")
            return False
            
        if not save_file_data:
            logger.error("存档文件内容为空")
            return False
            
        # 尝试多种编码读取
        for encoding in ["utf-8", "gbk", "gb2312", "big5", "latin1"]:
            try:
                text = save_file_data.decode(encoding)
                data = json.loads(text)
                logger.info(f"成功使用{encoding}编码读取存档文件")
                break
            except UnicodeDecodeError:
                logger.warning(f"使用{encoding}编码解析失败")
                continue
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，编码:{encoding}, 错误: {e}")
                continue
            except Exception as e:
                logger.warning(f"解析数据时出现其他错误，编码:{encoding}, 错误: {e}")
                continue
        
        # 如果所有编码尝试都失败
        if not data:
            logger.error("无法解析存档数据，所有编码尝试都失败")
            return False
        
        # 验证数据格式
        if not isinstance(data, dict):
            logger.error(f"数据不是字典类型: {type(data)}")
            return False
            
        logger.info(f"存档数据包含键: {', '.join(data.keys())}")
        
        # 检查并修复数据结构
        if 'player' not in data and any(key in data for key in ['character_class', 'level', 'name']):
            # 旧格式数据，转换为新格式
            logger.info("检测到旧格式存档数据，转换为新格式")
            player_data = {}
            for key in list(data.keys()):
                if key not in ['current_map', 'save_time', 'game_state']:
                    player_data[key] = data.pop(key)
            data['player'] = player_data
            
            # 将修复后的数据写回文件
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info("已将旧格式存档转换为新格式并保存")
            except Exception as e:
                logger.warning(f"保存修复后的存档失败: {e}")
                # 继续处理，不中断加载流程
        
        # 调用_load_from_data执行实际加载逻辑
        try:
            return self._load_from_data(data)
        except Exception as e:
            logger.error(f"加载数据时出错: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def reset_game_state(self):
        """重置游戏状态，用于加载存档之前"""
        logger.info("重置游戏状态")
        self.battle_enabled = True
        self.auto_battle = False
        self.in_battle = False
        self.current_enemy = None
        self.battle_log = []
        # 保留系统日志
        # 设置默认地图
        self.current_map = "比奇省"

    def start_battle(self):
        """开始战斗"""
        if self.in_battle or self.player.is_dead:
            return False
            
        try:
            # 检查玩家状态
            if not self.player:
                logger.error("玩家对象不存在")
                self.add_log("系统错误：玩家数据异常")
                return False
                
            # 生成怪物
            monster = self.generate_monster()
            if not monster:
                logger.error("生成怪物失败")
                self.add_log("当前区域无法生成怪物")
                return False
                
            # 设置战斗状态
            self.current_enemy = monster
            self.in_battle = True
            self.last_battle_time = time.time()
            
            # 初始化攻击时间
            current_time = time.time()
            self.player.last_attack_time = current_time
            self.current_enemy.last_attack_time = current_time
            self.last_player_attack = current_time
            self.last_enemy_attack = current_time
            
            # 初始化战斗系统
            success = self.battle_system.start_battle(self.player, self.current_enemy)
            if not success:
                logger.error("战斗系统初始化失败")
                self.add_log("战斗初始化失败")
                self.in_battle = False
                self.current_enemy = None
                return False
            
            # 记录战斗开始
            logger.info(f"开始战斗 - 玩家等级: {self.player.level}, 怪物: {self.current_enemy.name}")
            self.add_log(f"遭遇 {self.current_enemy.name}！")
            
            return True  # 明确返回True表示战斗成功启动
            
        except Exception as e:
            logger.error(f"开始战斗时发生错误: {e}")
            self.add_log(f"战斗系统错误: {e}")
            # 重置战斗状态
            self.in_battle = False
            self.current_enemy = None
            if self.player:
                self.player.last_attack_time = time.time()
            return False

    def save_data(self):
        """保存游戏数据"""
        try:
            # 检查player对象是否有效
            if self.player is None:
                logger.error("保存数据失败：玩家对象为空")
                return None
                
            # 获取玩家数据
            logger.info("开始获取玩家数据")
            player_data = self.player.save_data()
            if player_data is None:
                logger.error("保存游戏数据失败：player.save_data()返回None")
                return None
            
            # 确保数据的完整性
            if not isinstance(player_data, dict):
                logger.error(f"玩家数据格式错误: 类型为 {type(player_data)}")
                return None
            
            # 检查关键数据
            required_fields = ["level", "hp", "max_hp", "attack", "defense"]
            missing_fields = []
            for field in required_fields:
                if field not in player_data:
                    missing_fields.append(field)
            
            if missing_fields:
                logger.error(f"玩家数据缺少关键字段: {', '.join(missing_fields)}")
                logger.error(f"现有字段: {', '.join(player_data.keys())}")
                return None
            
            # 准备存档数据
            data = {
                "version": "1.1",   # 更新版本号
                "player": player_data,
                "current_map": self.current_map,
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),  # 添加时间戳
                "game_state": {
                    "in_battle": self.in_battle,
                    "battle_enabled": self.battle_enabled,
                    "auto_battle": self.auto_battle
                }
            }
            
            logger.info(f"成功准备存档数据，包含字段: {', '.join(data.keys())}")
            return data
            
        except Exception as e:
            logger.error(f"准备存档数据时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def toggle_auto_battle(self):
        """切换自动战斗状态"""
        if self.battle_system and self.in_battle:
            result = self.battle_system.toggle_auto_battle()
            logger.info(f"自动战斗模式: {'开启' if result else '关闭'}")
            return result
        return False

    def _on_monster_death_callback(self, player, monster):
        """怪物死亡回调函数"""
        # 记录战斗结果
        self.add_log(f"击败了 {monster.name}", True)
        
        # 结束战斗状态
        self.in_battle = False
        self.current_enemy = None
        
    def _on_player_death_callback(self, player, monster):
        """玩家死亡回调函数"""
        # 记录战斗结果
        self.add_log(f"被 {monster.name} 击败", True)
        
        # 设置玩家死亡状态
        if player:
            player.is_dead = True
            player.last_death_time = time.time()
        
        # 结束战斗状态
        self.in_battle = False
        self.current_enemy = None

    def _load_from_data(self, data):
        """直接从数据字典加载游戏状态
        
        Args:
            data: 包含游戏数据的字典
            
        Returns:
            bool: 是否成功加载
        """
        try:
            logger.info("使用_load_from_data方法直接加载数据字典")
            
            # 验证数据格式
            if not isinstance(data, dict):
                logger.error(f"数据不是字典类型: {type(data)}")
                return False
                
            logger.info(f"数据包含键: {', '.join(data.keys())}")
            
            # 重置游戏状态
            self.reset_game_state()
            
            # 处理不同版本的存档结构
            player_data = None
            if "player" in data:
                # 新版存档结构
                player_data = data["player"]
                logger.info("从player字段加载数据")
                
                # 加载游戏状态
                self.current_map = data.get("current_map", "比奇省")
                
                # 尝试加载游戏状态
                game_state = data.get("game_state", {})
                if isinstance(game_state, dict):
                    self.battle_enabled = game_state.get("battle_enabled", True)
                    self.auto_battle = game_state.get("auto_battle", False)
            else:
                # 旧版存档结构
                player_data = data
                logger.info("使用整个数据字典作为玩家数据")
            
            if not player_data or not isinstance(player_data, dict):
                logger.error(f"玩家数据无效: {type(player_data)}")
                return False
            
            # 创建玩家并加载数据
            character_class = player_data.get("character_class", "战士")
            player_name = player_data.get("name", "")
            
            logger.info(f"创建角色: {character_class}, 名称: {player_name}")
            self.create_new_character(character_class, player_name)
            
            # 加载玩家数据
            success = self.player.load_data(player_data)
            if not success:
                logger.error("加载玩家数据失败")
                return False
            
            # 确保玩家数据的一致性
            self.player.recalculate_stats()
            
            logger.info("成功直接加载数据")
            return True
            
        except Exception as e:
            logger.error(f"直接加载数据时出错: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def use_health_potion(self):
        """使用生命药水"""
        if not self.player or self.player.is_dead:
            return False
            
        # 从背包中查找生命药水
        potions = []
        for item in self.player.inventory:
            if item.get("type") != "消耗品":
                continue
                
            effect = item.get("effect", {})
            if effect.get("type") == "heal":
                potions.append(item)
                
        if not potions:
            logger.warning("背包中没有生命药水")
            self.add_log("你没有任何可用的生命药水！")
            return False
            
        # 按药水效果从强到弱排序
        potions.sort(key=lambda x: x.get("effect", {}).get("value", 0), reverse=True)
        
        # 使用药水
        potion = potions[0]  # 使用效果最强的药水
        
        # 应用效果
        old_hp = self.player.hp
        effect_value = potion.get("effect", {}).get("value", 0)
        self.player.hp = min(self.player.hp + effect_value, self.player.max_hp)
        healed = self.player.hp - old_hp
        
        self.add_log(f"使用了 {potion['name']}，回复了 {healed} 点生命值")
        logger.info(f"使用物品: {potion['name']}，回复生命值: {healed}")
        
        # 从背包中移除物品
        try:
            self.player.inventory.remove(potion)
            return True
        except ValueError:
            logger.error(f"移除消耗品失败: 背包中未找到物品 {potion['name']}")
            return False
            
    def use_mana_potion(self):
        """使用魔法药水"""
        if not self.player or self.player.is_dead:
            return False
            
        # 从背包中查找魔法药水
        potions = []
        for item in self.player.inventory:
            if item.get("type") != "消耗品":
                continue
                
            effect = item.get("effect", {})
            if effect.get("type") == "mana":
                potions.append(item)
                
        if not potions:
            logger.warning("背包中没有魔法药水")
            self.add_log("你没有任何可用的魔法药水！")
            return False
            
        # 按药水效果从强到弱排序
        potions.sort(key=lambda x: x.get("effect", {}).get("value", 0), reverse=True)
        
        # 使用药水
        potion = potions[0]  # 使用效果最强的药水
        
        # 应用效果
        old_mp = self.player.mp
        effect_value = potion.get("effect", {}).get("value", 0)
        self.player.mp = min(self.player.mp + effect_value, self.player.max_mp)
        restored = self.player.mp - old_mp
        
        self.add_log(f"使用了 {potion['name']}，回复了 {restored} 点魔法值")
        logger.info(f"使用物品: {potion['name']}，回复魔法值: {restored}")
        
        # 从背包中移除物品
        try:
            self.player.inventory.remove(potion)
            return True
        except ValueError:
            logger.error(f"移除消耗品失败: 背包中未找到物品 {potion['name']}")
            return False