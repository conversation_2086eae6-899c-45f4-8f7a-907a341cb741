[2025-05-22 20:19:25] [WARNING] [main.py:106] 游戏启动
[2025-05-22 20:19:25] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 20:19:25] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:35] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:35] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:36] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:36] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:37] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:37] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:38] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:38] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:39] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:39] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:40] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:40] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:41] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:41] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:42] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:42] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:43] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:43] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:44] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:44] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:45] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:45] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:46] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:46] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:47] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:47] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

