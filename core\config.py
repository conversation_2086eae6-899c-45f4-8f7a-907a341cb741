import os
import json
import random
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import traceback

from utils.logger import logger
from utils.resource_manager import resources
from core.class_stats import (
    CLASS_STATS, WARRIOR_STATS, MAGE_STATS, TAOIST_STATS,
    get_class_stats, get_level_stats
)

class GameConfig:
    """游戏配置管理类"""

    # 装备配置（从JSON加载）
    loaded_equipment = None
    config_paths = [
        'data/configs/equipment.json'  # 旧路径兼容
    ]

    @classmethod
    def get_path(cls, filename):
        """获取文件的绝对路径

        参数:
            filename: 文件名

        返回:
            Path: 文件的绝对路径
        """
        return Path.cwd() / filename

    # 职业基础属性配置 - 从class_stats.py导入
    CLASS_STATS = CLASS_STATS

    # 配置文件路径
    CONFIG_PATHS = {
        "equipment": ["data/configs/equipment.json"],
        "monsters": ["data/configs/monsters.json", "data/monsters.json"],
        "maps": ["data/configs/maps.json"],
        "drop_rates": ["data/configs/drop_rates.json"],
        "skills": ["data/configs/skills.json"],  # 技能配置路径
        "skillbooks": ["data/configs/skillbooks.json"],  # 技能书配置路径
        "consumables": ["data/configs/consumables.json"]  # 消耗品配置路径
    }

    # 初始化地图数据
    MAPS_DATA = {}

    # 初始化掉落率数据
    DROP_RATES = {}

    # 初始化技能数据
    SKILLS_DATA = {}

    # 初始化技能书数据
    SKILLBOOKS_DATA = {}

    # 初始化消耗品数据
    CONSUMABLES_DATA = {}

    # 战斗速度修正因子，1.0为正常速度，小于1.0则减慢战斗
    BATTLE_SPEED_MODIFIER = 1.0  #

    # 自动战斗间隔配置（秒）
    BATTLE_INTERVAL_MIN = 3.0  # 最小战斗间隔
    BATTLE_INTERVAL_MAX = 5.0  # 最大战斗间隔

    # 设置默认地图配置 - 将在load_maps失败时使用
    DEFAULT_MAPS_DATA = {
        "比奇省": {
            "difficulty": 1,
            "level_required": 1,
            "monsters": [
                {"name": "鸡", "weight": 30},
                {"name": "钉耙猫", "weight": 30}
            ],
            "description": "比奇城外的田野"
        }
    }
    # 副本系统已移除

    # 设置默认掉落率 - 将在load_drop_rates失败时使用
    DEFAULT_DROP_RATES = {
        "monsters": {
            "鸡": {
                "drops": [
                    {"item": "小红药", "rate": 0.1}
                ]
            }
        }
    }

    # --- 修改：移除类定义时直接加载怪物的冗余代码 ---
    # # 怪物配置
    # MONSTER_DATA = {}
    # loaded_count = 0
    # try:
    #     with open('data/configs/monsters.json', 'r', encoding='utf-8') as f:
    #         raw_data = json.load(f)
    #         if not isinstance(raw_data, dict):
    #             raise ValueError("怪物数据格式错误，应为字典类型")
    #
    #         for name, data in raw_data.items():
    #             try:
    #                 monster = {
    #                     "level": 1,
    #                     "hp": 10,
    #                     "defense": 0,
    #                     "magic_defense": 0,
    #                     "attack_range": {"min":1, "max":1},
    #                     "accuracy": 5,
    #                     "attack_speed": 1.0
    #                 }
    #
    #                 # 处理不同格式数据
    #                 if isinstance(data, dict):  # 新格式
    #                     valid_fields = {
    #                         "level": int(data.get("level", 1)),
    #                         "hp": int(data.get("hp", 10)),
    #                         "defense": int(data.get("defense", 0)),
    #                         "magic_defense": int(data.get("magic_defense", 0)),
    #                         "attack_range": data.get("attack_range", {"min":1, "max":1}),
    #                         "accuracy": int(data.get("accuracy", 5)),
    #                         "attack_speed": float(data.get("attack_speed", 1.0)),
    #                         "exp": int(data.get("exp", data.get("level", 1) * 10))  # 添加经验值字段
    #                     }
    #                     # 验证攻击范围
    #                     if isinstance(valid_fields["attack_range"], list):
    #                         valid_fields["attack_range"] = {
    #                             "min": valid_fields["attack_range"][0],
    #                             "max": valid_fields["attack_range"][1]
    #                         }
    #                     monster.update(valid_fields)
    #                 elif isinstance(data, list):  # 旧格式
    #                     if len(data) < 7:
    #                         raise ValueError("旧格式数据字段不足")
    #                     monster.update({
    #                         "level": int(data[0]),
    #                         "hp": int(data[1]),
    #                         "defense": int(data[2]),
    #                         "magic_defense": int(data[3]),
    #                         "attack_range": data[4] if isinstance(data[4], dict) else {"min": data[4][0], "max": data[4][1]},
    #                         "accuracy": int(data[5]),
    #                         "attack_speed": float(data[6]),
    #                         "exp": int(data[7]) if len(data) > 7 else int(data[0]) * 10  # 如果有经验值就使用，否则用等级*10
    #                     })
    #                 else:
    #                     raise ValueError("未知的数据格式类型")
    #
    #                 # 最终验证
    #                 if not isinstance(monster["attack_range"], dict):
    #                     raise ValueError("攻击范围格式错误")
    #                 if monster["attack_range"]["min"] > monster["attack_range"]["max"]:
    #                     raise ValueError("攻击范围最小值大于最大值")
    #
    #                 MONSTER_DATA[name] = monster
    #                 loaded_count += 1
    #                 logger.debug(f"成功加载怪物: {name} - 等级{monster['level']} HP{monster['hp']} 经验值{monster.get('exp', '未设置')}")
    #             except Exception as e:
    #                 logger.error(f"加载怪物[{name}]失败: {str(e)}\n原始数据: {data}")
    #                 continue
    #
    #
    #         logger.info(f"成功加载{loaded_count}个怪物配置，跳过{len(raw_data)-loaded_count}个无效条目")
    # except Exception as e:
    #     logger.error(f"加载怪物配置失败: {str(e)}")
    #     # 回退默认配置
    #     MONSTER_DATA = {
    #         "大刀": {
    #             "level": 99,
    #             "hp": 9999,
    #             "defense": 9999,
    #             "magic_defense": 9999,
    #             "attack_range": {"min": 9999, "max": 9999},
    #             "accuracy": 9999,
    #             "attack_speed": 9999
    #         }
    #     }
    # --- 结束修改 ---
    MONSTER_DATA = {} # 初始化为空字典，由 load_monsters 填充

    # 战士职业等级成长表 - 从class_stats.py导入
    WARRIOR_STATS = WARRIOR_STATS

    @classmethod
    def initialize(cls):
        """初始化游戏配置"""
        # 加载装备配置
        cls.load_equipment()

        # 加载怪物配置
        cls.load_monsters()

        # 加载地图数据
        cls.load_maps()

        # 加载掉落配置
        cls.load_drop_rates()

        # 加载技能配置
        cls.load_skills()

        # 加载技能书配置
        cls.load_skillbooks()

        # 加载消耗品配置
        cls.load_consumables()

    @classmethod
    def load_equipment(cls):
        """加载装备配置"""
        # 首先尝试使用 _load_config 方法加载
        cls.loaded_equipment = cls._load_config("equipment")

        # 如果加载失败，尝试使用直接文件路径加载
        if not cls.loaded_equipment:
            try:
                equipment_path = cls.get_path("equipment.json")
                if equipment_path.exists():
                    with open(equipment_path, 'r', encoding='utf-8') as f:
                        cls.loaded_equipment = json.load(f)
                    logger.warning(f"使用直接路径成功加载装备配置: {len(cls.loaded_equipment.get('equipment_db', {}))} 类装备")
                else:
                    logger.error("装备配置文件不存在")
            except Exception as e:
                logger.error(f"使用直接路径加载装备配置失败: {e}")

        # 添加EQUIPMENT属性，让它指向loaded_equipment
        cls.EQUIPMENT = cls.loaded_equipment

        # 确保首饰类物品有正确的类型属性
        if cls.loaded_equipment and "equipment_db" in cls.loaded_equipment:
            equipment_db = cls.loaded_equipment["equipment_db"]
            if "首饰" in equipment_db:
                for accessory_type, items in equipment_db["首饰"].items():
                    for item in items:
                        if isinstance(item, dict) and "type" not in item:
                            item["type"] = accessory_type
                            logger.debug(f"为首饰类物品 {item.get('name', '未知')} 设置类型: {accessory_type}")

        # 检查品质配置
        if cls.loaded_equipment:
            tiers_config = cls.loaded_equipment.get("tiers", {})
            if not tiers_config:
                logger.warning("装备品质配置为空，将使用默认品质配置")
                # 设置默认的品质配置
                cls.loaded_equipment["tiers"] = {
                    "普通": {"weight": 80, "price": [10, 50]},
                    "精良": {"weight": 15, "price": [50, 100]},
                    "稀有": {"weight": 10, "price": [100, 200]},
                    "史诗": {"weight": 4, "price": [200, 500]},
                    "传说": {"weight": 1, "price": [500, 1000]}
                }
                logger.warning("已添加默认品质配置")
            else:
                # 检查每个品质的权重配置
                for tier, config in tiers_config.items():
                    if "weight" not in config:
                        logger.warning(f"品质 {tier} 缺少权重配置，设置默认值 1")
                        config["weight"] = 1

    @classmethod
    def load_monsters(cls):
        """加载怪物配置"""
        logger.info("开始加载怪物配置")
        try:
            # --- 修改：使用 ResourceManager 加载 JSON ---
            from utils.resource_manager import resources # 确保导入
            cls.MONSTER_DATA = resources.load_json_data("data/configs/monsters.json", default={})
            # --- 结束修改 ---

            if not cls.MONSTER_DATA:
                logger.error("加载怪物配置失败或文件为空")
            else:
                 logger.info(f"成功加载 {len(cls.MONSTER_DATA.get('monsters', []))} 个怪物配置")
        except Exception as e:
            logger.error(f"加载怪物配置时发生异常: {e}", exc_info=True)

    @classmethod
    def load_maps(cls):
        """加载地图数据"""
        logger.info("开始加载地图数据")
        try:
            # 从配置路径中加载地图数据
            maps_data = cls._load_config("maps")

            # 记录加载的原始数据
            logger.info(f"加载的原始地图数据类型: {type(maps_data)}")
            if isinstance(maps_data, dict):
                logger.info(f"原始地图数据包含的键: {list(maps_data.keys())}")
                if "maps" in maps_data:
                    logger.info(f"maps键下的数据类型: {type(maps_data['maps'])}")
                    if isinstance(maps_data["maps"], dict):
                        logger.info(f"maps键下包含 {len(maps_data['maps'])} 个地图")

            # 检查地图数据是否为空
            if not maps_data:
                logger.warning("地图数据为空，使用默认地图配置")
                cls.MAPS_DATA = cls.DEFAULT_MAPS_DATA
                logger.info(f"已设置默认地图配置，包含 {len(cls.MAPS_DATA)} 个地图")
                return

            # 处理不同的格式情况
            if isinstance(maps_data, dict):
                if "maps" in maps_data and isinstance(maps_data["maps"], dict):
                    # 标准格式：{"maps": {...}}
                    cls.MAPS_DATA = maps_data["maps"]
                    logger.info(f"成功加载地图数据: {len(cls.MAPS_DATA)}个地图")
                    logger.info(f"地图列表: {list(cls.MAPS_DATA.keys())}")
                else:
                    # 直接是地图字典：{...}
                    cls.MAPS_DATA = maps_data
                    logger.info(f"成功加载地图数据(直接格式): {len(cls.MAPS_DATA)}个地图")
                    logger.info(f"地图列表: {list(cls.MAPS_DATA.keys())}")
            else:
                # 数据格式错误
                logger.error(f"地图数据格式错误，使用默认地图配置")
                cls.MAPS_DATA = cls.DEFAULT_MAPS_DATA
                logger.info(f"已设置默认地图配置，包含 {len(cls.MAPS_DATA)} 个地图")

            # 验证地图数据完整性
            if len(cls.MAPS_DATA) == 0:
                logger.warning("加载的地图数据为空，使用默认地图配置")
                cls.MAPS_DATA = cls.DEFAULT_MAPS_DATA
                logger.info(f"已设置默认地图配置，包含 {len(cls.MAPS_DATA)} 个地图")
                return

            # 检查每个地图的数据格式
            invalid_maps = []
            for map_name, map_data in cls.MAPS_DATA.items():
                if not isinstance(map_data, dict):
                    logger.warning(f"地图 {map_name} 的数据格式错误，应为字典，实际为 {type(map_data)}")
                    invalid_maps.append(map_name)
                    continue

                if "monsters" not in map_data:
                    logger.warning(f"地图 {map_name} 没有配置怪物")
                    continue

                if not isinstance(map_data["monsters"], list):
                    logger.warning(f"地图 {map_name} 的怪物配置格式错误，应为列表，实际为 {type(map_data['monsters'])}")
                    continue

                if len(map_data["monsters"]) == 0:
                    logger.warning(f"地图 {map_name} 的怪物配置为空列表")
                    continue

                logger.debug(f"地图 {map_name} 配置有效，包含 {len(map_data['monsters'])} 个怪物")

            # 移除无效的地图
            for map_name in invalid_maps:
                del cls.MAPS_DATA[map_name]
                logger.info(f"已移除无效地图: {map_name}")

            # 移除所有副本地图
            maps_to_remove = []
            for map_name, map_data in cls.MAPS_DATA.items():
                if map_data.get("is_dungeon", False):
                    maps_to_remove.append(map_name)

            # 从地图数据中删除副本地图
            for map_name in maps_to_remove:
                del cls.MAPS_DATA[map_name]
                logger.info(f"已移除副本地图: {map_name}")

            # 最终检查
            if len(cls.MAPS_DATA) == 0:
                logger.warning("移除无效地图后，地图数据为空，使用默认地图配置")
                cls.MAPS_DATA = cls.DEFAULT_MAPS_DATA
                logger.info(f"已设置默认地图配置，包含 {len(cls.MAPS_DATA)} 个地图")
            else:
                logger.info(f"最终加载了 {len(cls.MAPS_DATA)} 个有效地图: {list(cls.MAPS_DATA.keys())}")

        except Exception as e:
            logger.error(f"加载地图数据失败: {e}")
            logger.error(traceback.format_exc())
            # 使用默认地图配置
            cls.MAPS_DATA = cls.DEFAULT_MAPS_DATA
            logger.info(f"由于异常，已设置默认地图配置，包含 {len(cls.MAPS_DATA)} 个地图")

    @classmethod
    def load_drop_rates(cls):
        """加载掉落配置"""
        logger.info("开始加载掉落率数据")
        try:
            # 从配置路径中加载掉落率数据
            drop_data = cls._load_config("drop_rates")

            # 检查掉落率数据是否为空
            if not drop_data:
                logger.warning("掉落率数据为空，使用默认掉落率配置")
                cls.DROP_RATES = cls.DEFAULT_DROP_RATES
                return

            # 处理不同的格式情况
            if isinstance(drop_data, dict):
                if "drop_rates" in drop_data and isinstance(drop_data["drop_rates"], dict):
                    # 标准格式：{"drop_rates": {...}}
                    cls.DROP_RATES = drop_data["drop_rates"]
                    logger.info(f"成功加载掉落率数据")
                else:
                    # 直接是掉落率字典：{...}
                    cls.DROP_RATES = drop_data
                    logger.info(f"成功加载掉落率数据(直接格式)")
            else:
                # 数据格式错误
                logger.error(f"掉落率数据格式错误，使用默认掉落率配置")
                cls.DROP_RATES = cls.DEFAULT_DROP_RATES

            # 验证掉落率数据完整性
            if len(cls.DROP_RATES) == 0:
                logger.warning("加载的掉落率数据为空，使用默认掉落率配置")
                cls.DROP_RATES = cls.DEFAULT_DROP_RATES

        except Exception as e:
            logger.error(f"加载掉落率数据失败: {e}")
            logger.error(traceback.format_exc())
            # 使用默认掉落率配置
            cls.DROP_RATES = cls.DEFAULT_DROP_RATES

    @classmethod
    def load_skills(cls):
        """加载技能配置"""
        logger.info("开始加载技能配置")

        try:
            # 从配置路径中加载技能数据
            skills_data = cls._load_config("skills")

            if skills_data:
                cls.SKILLS_DATA = skills_data
                # 统计总技能数
                total_skills = sum(len(class_skills) for class_skills in skills_data.values())
                logger.info(f"成功加载技能配置: {len(skills_data)}个职业，{total_skills}个技能")
            else:
                logger.warning("技能配置为空，使用默认配置")
                cls.SKILLS_DATA = cls._get_default_skills()
        except Exception as e:
            logger.error(f"加载技能配置失败: {e}")
            cls.SKILLS_DATA = cls._get_default_skills()

    @classmethod
    def load_skillbooks(cls):
        """加载技能书配置"""
        logger.info("开始加载技能书配置")

        try:
            # 从配置路径中加载技能书数据
            skillbooks_data = cls._load_config("skillbooks")

            if skillbooks_data:
                cls.SKILLBOOKS_DATA = skillbooks_data
                # 统计总技能书数
                total_skillbooks = sum(len(class_skillbooks) for class_skillbooks in skillbooks_data.values())
                logger.info(f"成功加载技能书配置: {len(skillbooks_data)}个职业，{total_skillbooks}个技能书")
            else:
                logger.warning("技能书配置为空，使用默认配置")
                cls.SKILLBOOKS_DATA = cls._get_default_skillbooks()
        except Exception as e:
            logger.error(f"加载技能书配置失败: {e}")
            cls.SKILLBOOKS_DATA = cls._get_default_skillbooks()

    @classmethod
    def load_consumables(cls):
        """加载消耗品配置"""
        logger.info("开始加载消耗品配置")

        try:
            # 从配置路径中加载消耗品数据
            consumables_data = cls._load_config("consumables")

            if consumables_data:
                cls.CONSUMABLES_DATA = consumables_data
                # 统计总消耗品数
                total_consumables = sum(len(category_consumables) for category_consumables in consumables_data.values())
                logger.info(f"成功加载消耗品配置: {len(consumables_data)}个分类，{total_consumables}个消耗品")
            else:
                logger.warning("消耗品配置为空，使用默认配置")
                cls.CONSUMABLES_DATA = cls._get_default_consumables()
        except Exception as e:
            logger.error(f"加载消耗品配置失败: {e}")
            cls.CONSUMABLES_DATA = cls._get_default_consumables()

    @classmethod
    def _load_config(cls, config_name: str) -> Dict[str, Any]:
        """加载指定配置文件"""
        if config_name not in cls.CONFIG_PATHS:
            logger.error(f"未知的配置类型: {config_name}")
            return {}

        paths = cls.CONFIG_PATHS[config_name]

        for path in paths:
            try:
                data = resources.load_json_data(path)
                logger.info(f"成功加载{config_name}配置: {path}")
                return data
            except FileNotFoundError:
                logger.warning(f"配置文件不存在: {path}")
                continue
            except json.JSONDecodeError as e:
                logger.error(f"配置文件{path}格式错误: {e}")
                continue
            except Exception as e:
                logger.error(f"加载配置文件{path}失败: {e}")
                continue

        logger.warning(f"未能加载任何{config_name}配置文件，使用空字典")
        return {}

    @staticmethod
    def _get_default_monster() -> Dict[str, Dict[str, Any]]:
        """获取默认怪物配置（用于加载失败时）"""
        return {
            "默认怪物": {
                "level": 1,
                "hp": 10,
                "defense": 0,
                "magic_defense": 0,
                "attack_range": {"min": 1, "max": 1},
                "accuracy": 5,
                "attack_speed": 1.0,
                "exp": 10
            }
        }

    @classmethod
    def _parse_attack_range(cls, data):
        """统一处理攻击范围数据格式"""
        if isinstance(data, dict):
            return data
        try:
            return {"min": data["min"], "max": data["max"]}
        except (IndexError, TypeError):
            logger.warning(f"无效的攻击范围数据格式: {data}, 使用默认值")
            return {"min": 1, "max": 1}

    @staticmethod
    def get_warrior_stats(level: int) -> List[Union[int, float]]:
        """获取战士的升级属性（已被重定向到class_stats模块）"""
        return get_level_stats("战士", level)

    @classmethod
    def get_class_stats(cls, character_class: str) -> Dict[str, Any]:
        """获取职业属性（已被重定向到class_stats模块）"""
        return get_class_stats(character_class)

    @classmethod
    def get_equipment(cls, equipment_name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取装备信息"""
        if not cls.loaded_equipment:
            logger.warning("装备数据未加载")
            return None

        # 先查找新手装备
        equipment_db = cls.loaded_equipment.get("equipment_db", {})
        newbie_equipment = equipment_db.get("新手装备", [])

        # 根据名称查找装备
        for item in newbie_equipment:
            if item.get("name") == equipment_name:
                item_copy = item.copy()
                # 确保装备类型被明确设置
                if "type" not in item_copy:
                    item_copy["type"] = "新手装备"
                return item_copy

        # 如果在新手装备中找不到，查找所有装备
        for category, items in equipment_db.items():
            for item in items:
                if item.get("name") == equipment_name:
                    item_copy = item.copy()
                    # 确保装备类型被明确设置
                    if "type" not in item_copy or not item_copy["type"]:
                        # 直接使用类别作为类型
                        if category in ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "技能书", "消耗品"]:
                            item_copy["type"] = category
                            logger.info(f"为装备 {equipment_name} 设置类型: {category} (基于类别)")
                        else:
                            # 根据名称推断类型
                            inferred_type = cls._infer_item_type(equipment_name)
                            if inferred_type:
                                item_copy["type"] = inferred_type
                                logger.info(f"为装备 {equipment_name} 设置类型: {inferred_type} (基于名称推断)")
                            else:
                                # 默认类型
                                item_copy["type"] = "其他"
                                logger.warning(f"无法为装备 {equipment_name} 推断类型，设置为默认类型: 其他")
                    return item_copy

        # 如果仍然找不到，返回基础装备
        if equipment_name == "木剑":
            return {
                "name": "木剑",
                "type": "武器",
                "level": 1,
                "attack": [2, 5],
                "tier": "普通",
                "price": 10
            }
        elif equipment_name == "布衣":
            return {
                "name": "布衣",
                "type": "防具",
                "level": 1,
                "defense": [1, 3],
                "tier": "普通",
                "price": 10
            }

        logger.warning(f"找不到装备: {equipment_name}")
        return None

    @classmethod
    def get_item_info(cls, item_name: str) -> Optional[Dict[str, Any]]:
        """获取物品信息，包括装备、技能书和消耗品

        Args:
            item_name: 物品名称

        Returns:
            dict: 物品信息字典，如果找不到则返回None
        """
        # 查找装备
        equipment_db = cls.loaded_equipment.get("equipment_db", {})
        for category, items in equipment_db.items():
            for item in items:
                if item.get("name") == item_name:
                    item_copy = item.copy()
                    # 确保装备类型被明确设置
                    if "type" not in item_copy or not item_copy["type"]:
                        # 直接使用类别作为类型
                        # 特殊处理项链、戒指、手镯等类别
                        if category in ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "技能书", "消耗品"]:
                            item_copy["type"] = category
                            logger.info(f"为物品 {item_name} 设置类型: {category} (基于类别)")
                        else:
                            # 根据名称推断类型
                            inferred_type = cls._infer_item_type(item_name)
                            if inferred_type:
                                item_copy["type"] = inferred_type
                                logger.info(f"为物品 {item_name} 设置类型: {inferred_type} (基于名称推断)")
                            else:
                                # 默认类型
                                item_copy["type"] = "其他"
                                logger.warning(f"无法为物品 {item_name} 推断类型，设置为默认类型: 其他")
                    return item_copy

        # 查找技能书
        if hasattr(cls, "SKILLBOOKS_DATA") and cls.SKILLBOOKS_DATA:
            skill_books = cls.SKILLBOOKS_DATA.get("skill_books", {})
            for class_type, books in skill_books.items():
                for book in books:
                    if book.get("name") == item_name:
                        book_copy = book.copy()
                        # 确保技能书有类型
                        if "type" not in book_copy or not book_copy["type"]:
                            book_copy["type"] = "技能书"
                        return book_copy

        # 查找消耗品
        if hasattr(cls, "CONSUMABLES_DATA") and cls.CONSUMABLES_DATA:
            consumables = cls.CONSUMABLES_DATA.get("consumables", {})
            for category, items in consumables.items():
                for item in items:
                    if item.get("name") == item_name:
                        return item.copy()

        return None

    @classmethod
    def _infer_item_type(cls, item_name: str) -> str:
        """根据物品名称推断物品类型

        参数:
            item_name: 物品名称

        返回:
            str: 推断出的物品类型，如果无法推断则返回空字符串
        """
        type_patterns = {
            "武器": ["剑", "刀", "杖", "斧", "弓", "匕首", "锤", "枪"],
            "防具": ["盔甲", "战衣", "布衣", "袍", "护甲", "铠甲"],
            "项链": ["项链", "明珠", "珠子", "护符", "符"],
            "戒指": ["戒指", "指环"],
            "手镯": ["手镯", "护腕", "手套", "臂环"],
            "头盔": ["头盔", "帽子", "头饰", "冠"],
            "技能书": ["技能书", "秘籍", "法术书", "咒语书"],
            "消耗品": ["药水", "药", "丹", "符", "卷轴", "强化石", "祝福油", "战神油"]
        }

        for item_type, patterns in type_patterns.items():
            if any(pattern in item_name for pattern in patterns):
                return item_type

        # 特殊处理项链
        if "项链" in item_name:
            return "项链"

        return ""

    @staticmethod
    def _get_default_skills() -> Dict[str, Dict[str, Any]]:
        """获取默认技能配置"""
        return {
            "warrior": {
                "basic_sword": {
                    "name": "基本剑术",
                    "type": "passive",
                    "description": "精通基本剑术，提高准确性",
                    "max_level": 3,
                    "level_requirements": [7, 11, 16],
                    "effects": [
                        {"type": "accuracy", "value": 3},
                        {"type": "accuracy", "value": 6},
                        {"type": "accuracy", "value": 9}
                    ],
                    "mp_cost": 0,
                    "cooldown": 0
                }
            }
        }

    @classmethod
    def _get_default_skillbooks(cls) -> Dict[str, Dict[str, Any]]:
        """获取默认技能书配置"""
        return {
            "warrior": {
                "sword_master": {
                    "name": "剑术大师",
                    "type": "active",
                    "description": "提高剑术攻击力",
                    "max_level": 5,
                    "level_requirements": [10, 15, 20, 25, 30],
                    "effects": [
                        {"type": "attack", "value": 10},
                        {"type": "attack", "value": 20},
                        {"type": "attack", "value": 30},
                        {"type": "attack", "value": 40},
                        {"type": "attack", "value": 50}
                    ],
                    "mp_cost": 10,
                    "cooldown": 30
                }
            }
        }

    @classmethod
    def _get_default_consumables(cls) -> Dict[str, Dict[str, Any]]:
        """获取默认消耗品配置"""
        return {
            "health_potions": {
                "small_health_potion": {
                    "name": "小红药",
                    "type": "health",
                    "description": "恢复少量生命值",
                    "value": 10,
                    "price": 5
                },
                "medium_health_potion": {
                    "name": "中红药",
                    "type": "health",
                    "description": "恢复中等生命值",
                    "value": 50,
                    "price": 20
                },
                "large_health_potion": {
                    "name": "大红药",
                    "type": "health",
                    "description": "恢复大量生命值",
                    "value": 100,
                    "price": 50
                }
            }
        }

    @classmethod
    def get_skills_for_class(cls, character_class: str) -> Dict[str, Any]:
        """获取指定职业的所有技能"""
        if not cls.SKILLS_DATA:
            cls.load_skills()

        # 转换职业名称为英文，以匹配配置文件
        class_map = {
            "战士": "warrior",
            "法师": "mage",
            "道士": "taoist"
        }

        class_key = class_map.get(character_class, character_class.lower())
        return cls.SKILLS_DATA.get(class_key, {})

    @classmethod
    def get_skill(cls, skill_id: str, character_class: str = None) -> Optional[Dict[str, Any]]:
        """获取指定ID的技能配置

        参数:
            skill_id: 技能ID
            character_class: 可选的职业名称，用于限定查找范围

        返回:
            技能配置字典，如果未找到则返回None
        """
        if not cls.SKILLS_DATA:
            cls.load_skills()

        # 如果指定了职业，只在该职业技能中查找
        if character_class:
            class_map = {
                "战士": "warrior",
                "法师": "mage",
                "道士": "taoist"
            }
            class_key = class_map.get(character_class, character_class.lower())
            class_skills = cls.SKILLS_DATA.get(class_key, {})
            return class_skills.get(skill_id)

        # 否则在所有职业技能中查找
        for class_skills in cls.SKILLS_DATA.values():
            if skill_id in class_skills:
                return class_skills[skill_id]

        return None

# 初始化时调用此方法加载所有配置
# GameConfig.initialize()