# -*- coding: utf-8 -*-
"""
成就界面
显示玩家的成就进度和奖励
"""

import pygame
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from ui.ui_manager import Screen
from ui.components import Panel, Button, Text, ProgressBar
from utils.logger import logger

if TYPE_CHECKING:
    from core.game import Game

class AchievementsScreen(Screen):
    """成就界面类"""
    
    def __init__(self, ui_manager, game_manager: 'Game'):
        """
        初始化成就界面
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
        """
        super().__init__("achievements")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        
        # 成就数据
        self.achievements_data = self._load_achievements_data()
        
        # UI组件
        self.components_map = {}
        
        # 创建界面
        self._create_ui()
        
        logger.info("成就界面初始化完成")
    
    def _load_achievements_data(self) -> List[Dict[str, Any]]:
        """加载成就数据"""
        # 这里定义一些基础成就
        achievements = [
            {
                "id": "first_kill",
                "name": "初次击杀",
                "description": "击杀第一只怪物",
                "type": "kill",
                "target": 1,
                "reward_type": "exp",
                "reward_value": 100,
                "completed": False
            },
            {
                "id": "monster_hunter",
                "name": "怪物猎人",
                "description": "击杀100只怪物",
                "type": "kill",
                "target": 100,
                "reward_type": "gold",
                "reward_value": 1000,
                "completed": False
            },
            {
                "id": "level_up",
                "name": "等级提升",
                "description": "达到10级",
                "type": "level",
                "target": 10,
                "reward_type": "yuanbao",
                "reward_value": 10,
                "completed": False
            },
            {
                "id": "equipment_collector",
                "name": "装备收集者",
                "description": "获得10件装备",
                "type": "equipment",
                "target": 10,
                "reward_type": "exp",
                "reward_value": 500,
                "completed": False
            },
            {
                "id": "skill_master",
                "name": "技能大师",
                "description": "学会5个技能",
                "type": "skill",
                "target": 5,
                "reward_type": "gold",
                "reward_value": 2000,
                "completed": False
            }
        ]
        
        return achievements
    
    def _create_ui(self):
        """创建UI界面"""
        screen_size = pygame.display.get_surface().get_size()
        
        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            pygame.Rect(50, 50, screen_size[0] - 100, screen_size[1] - 100),
            color=(30, 30, 50),
            border_color=(80, 80, 120),
            border_width=2
        )
        self.add_component(main_panel)
        self.components_map["main_panel"] = main_panel
        
        # 创建标题
        title_rect = pygame.Rect(70, 70, 200, 40)
        title_text = self.ui_manager.create_text(
            title_rect,
            "成就系统",
            "chinese_title",
            (255, 255, 220),
            "left"
        )
        self.add_component(title_text)
        self.components_map["title"] = title_text
        
        # 创建成就列表
        self._create_achievements_list()
        
        # 创建返回按钮
        back_button_rect = pygame.Rect(screen_size[0] - 150, screen_size[1] - 80, 80, 40)
        back_button = self.ui_manager.create_button(
            back_button_rect,
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)
        self.components_map["back_button"] = back_button
    
    def _create_achievements_list(self):
        """创建成就列表"""
        screen_size = pygame.display.get_surface().get_size()
        
        # 成就列表区域
        list_start_y = 120
        achievement_height = 80
        achievement_spacing = 10
        
        for i, achievement in enumerate(self.achievements_data):
            y_pos = list_start_y + i * (achievement_height + achievement_spacing)
            
            # 成就面板
            achievement_rect = pygame.Rect(70, y_pos, screen_size[0] - 140, achievement_height)
            achievement_panel = self.ui_manager.create_panel(
                achievement_rect,
                color=(40, 40, 70) if not achievement["completed"] else (60, 80, 60),
                border_color=(60, 60, 100) if not achievement["completed"] else (80, 120, 80),
                border_width=1
            )
            self.add_component(achievement_panel)
            
            # 成就名称
            name_rect = pygame.Rect(achievement_rect.x + 10, achievement_rect.y + 5, 200, 25)
            name_text = self.ui_manager.create_text(
                name_rect,
                achievement["name"],
                "chinese_normal",
                (255, 255, 255) if not achievement["completed"] else (200, 255, 200),
                "left"
            )
            self.add_component(name_text)
            
            # 成就描述
            desc_rect = pygame.Rect(achievement_rect.x + 10, achievement_rect.y + 30, 300, 20)
            desc_text = self.ui_manager.create_text(
                desc_rect,
                achievement["description"],
                "chinese_small",
                (200, 200, 200),
                "left"
            )
            self.add_component(desc_text)
            
            # 进度显示
            progress = self._get_achievement_progress(achievement)
            progress_text = f"进度: {progress}/{achievement['target']}"
            
            progress_rect = pygame.Rect(achievement_rect.x + 10, achievement_rect.y + 50, 200, 20)
            progress_text_component = self.ui_manager.create_text(
                progress_rect,
                progress_text,
                "chinese_small",
                (180, 180, 180),
                "left"
            )
            self.add_component(progress_text_component)
            
            # 奖励信息
            reward_text = f"奖励: {self._format_reward(achievement)}"
            reward_rect = pygame.Rect(achievement_rect.right - 200, achievement_rect.y + 10, 180, 20)
            reward_text_component = self.ui_manager.create_text(
                reward_rect,
                reward_text,
                "chinese_small",
                (255, 215, 0),
                "right"
            )
            self.add_component(reward_text_component)
            
            # 状态显示
            status_text = "已完成" if achievement["completed"] else "未完成"
            status_color = (0, 255, 0) if achievement["completed"] else (255, 100, 100)
            status_rect = pygame.Rect(achievement_rect.right - 200, achievement_rect.y + 35, 180, 20)
            status_text_component = self.ui_manager.create_text(
                status_rect,
                status_text,
                "chinese_small",
                status_color,
                "right"
            )
            self.add_component(status_text_component)
    
    def _get_achievement_progress(self, achievement: Dict[str, Any]) -> int:
        """获取成就进度"""
        if not self.game_manager or not self.game_manager.player:
            return 0
        
        player = self.game_manager.player
        achievement_type = achievement["type"]
        
        try:
            if achievement_type == "kill":
                # 击杀数量成就
                return getattr(player, 'total_kills', 0)
            elif achievement_type == "level":
                # 等级成就
                return getattr(player, 'level', 1)
            elif achievement_type == "equipment":
                # 装备数量成就
                if hasattr(player, 'inventory') and hasattr(player.inventory, 'items'):
                    equipment_count = sum(1 for item in player.inventory.items 
                                        if item and getattr(item, 'type', '') in ['weapon', 'armor', 'accessory'])
                    return equipment_count
                return 0
            elif achievement_type == "skill":
                # 技能数量成就
                if hasattr(player, 'skills'):
                    return len([skill for skill, level in player.skills.items() if level > 0])
                return 0
            else:
                return 0
        except Exception as e:
            logger.warning(f"获取成就进度失败: {e}")
            return 0
    
    def _format_reward(self, achievement: Dict[str, Any]) -> str:
        """格式化奖励显示"""
        reward_type = achievement["reward_type"]
        reward_value = achievement["reward_value"]
        
        if reward_type == "exp":
            return f"{reward_value} 经验"
        elif reward_type == "gold":
            return f"{reward_value} 金币"
        elif reward_type == "yuanbao":
            return f"{reward_value} 元宝"
        else:
            return f"{reward_value} {reward_type}"
    
    def _check_achievements(self):
        """检查成就完成情况"""
        if not self.game_manager or not self.game_manager.player:
            return
        
        for achievement in self.achievements_data:
            if not achievement["completed"]:
                progress = self._get_achievement_progress(achievement)
                if progress >= achievement["target"]:
                    achievement["completed"] = True
                    self._grant_achievement_reward(achievement)
                    logger.info(f"成就完成: {achievement['name']}")
    
    def _grant_achievement_reward(self, achievement: Dict[str, Any]):
        """发放成就奖励"""
        if not self.game_manager or not self.game_manager.player:
            return
        
        player = self.game_manager.player
        reward_type = achievement["reward_type"]
        reward_value = achievement["reward_value"]
        
        try:
            if reward_type == "exp":
                player.gain_exp(reward_value)
            elif reward_type == "gold":
                player.gold += reward_value
            elif reward_type == "yuanbao":
                if hasattr(player, 'yuanbao'):
                    player.yuanbao += reward_value
            
            # 显示奖励消息
            if hasattr(self.game_manager, 'add_log'):
                reward_text = self._format_reward(achievement)
                self.game_manager.add_log(f"获得成就奖励: {reward_text}")
                
        except Exception as e:
            logger.error(f"发放成就奖励失败: {e}")
    
    def _on_back_click(self):
        """返回按钮点击事件"""
        self.ui_manager.show_screen("game")
    
    def update(self, dt: float):
        """更新界面"""
        # 定期检查成就完成情况
        self._check_achievements()
    
    def handle_event(self, event: pygame.event.Event):
        """处理事件"""
        # 处理ESC键返回
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self._on_back_click()
                return True
        
        return False
