import os
import logging
import traceback
import time
import datetime
from typing import Optional

# 创建全局日志对象
logger = logging.getLogger("game")

def setup_logger(level: int = logging.WARNING, log_file: Optional[str] = None) -> None:
    """
    设置日志系统
    
    参数:
        level: 日志级别，默认为WARNING（只记录警告和错误）
        log_file: 日志文件路径，默认为logs目录下的日期时间命名的文件
    """
    # 设置日志级别
    logger.setLevel(level)
    
    # 如果日志处理器已经存在，则不重复创建
    if logger.handlers:
        return
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    
    # 设置格式
    formatter = logging.Formatter(
        "[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(formatter)
    
    # 添加控制台处理器
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，则创建文件处理器
    if log_file is None:
        # 创建logs目录
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        # 使用当前日期时间创建日志文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(logs_dir, f"game_{timestamp}.log")
    
    try:
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        
        # 添加文件处理器
        logger.addHandler(file_handler)
        
        logger.info(f"日志记录到文件: {log_file}")
    except Exception as e:
        logger.warning(f"无法创建日志文件: {e}")
    
    # 防止日志传递到父处理器
    logger.propagate = False

def get_logger() -> logging.Logger:
    """
    获取日志对象
    
    返回:
        日志对象
    """
    if not logger.handlers:
        setup_logger()
    return logger

def _log_detailed_error_to_file(e: Exception):
    """将详细错误信息和堆栈跟踪追加记录到 logs/error_log.txt 文件"""
    try:
        # 确保 logs 目录存在
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        error_log_path = os.path.join(logs_dir, "error_log.txt")

        # 使用追加模式 'a'
        with open(error_log_path, "a", encoding='utf-8') as f:
            f.write(f"\n--- 错误发生于: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} ---\n")
            f.write(f"错误类型: {type(e).__name__}\n")
            f.write(f"错误信息: {str(e)}\n")
            f.write("详细堆栈:\n")
            traceback.print_exc(file=f) # 直接将堆栈写入文件
            f.write("-" * 40 + "\n")
    except Exception as log_error:
        # 如果记录错误日志本身也失败了，打印到控制台以防信息丢失
        print(f"!!! 紧急：记录详细错误日志到文件失败: {log_error}")
        print(f"!!! 原始错误类型: {type(e).__name__}, 信息: {str(e)}")
        print("!!! 原始错误堆栈:")
        traceback.print_exc()

def handle_error(e: Exception, message: str = "发生错误"):
    """统一的错误处理函数，记录错误到主日志并记录详细堆栈到错误文件"""
    # 确保 logger 已初始化 (以防万一在 setup 之前就发生错误)
    if not logger.handlers:
        try:
            # 尝试用默认设置初始化，避免因日志配置错误导致程序完全崩溃
            setup_logger(logging.WARNING) 
            logger.warning("Logger 在 handle_error 中被动初始化。")
        except Exception as setup_exc:
            print(f"!!! 紧急：在 handle_error 中初始化 logger 失败: {setup_exc}")
            print(f"!!! 原始错误信息: {message}: {e}")
            traceback.print_exc()
            # 即使日志设置失败，也尝试记录详细错误
            _log_detailed_error_to_file(e)
            return # 无法继续使用 logger，直接返回

    # 记录简要错误到主日志 (使用全局 logger)
    # exc_info=True 会让 logging 模块自动添加异常信息和堆栈跟踪（如果可用且级别允许）
    logger.error(f"{message}: {type(e).__name__} - {e}", exc_info=True)

    # 记录详细错误和堆栈到单独的文件
    _log_detailed_error_to_file(e) 