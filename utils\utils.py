#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pygame
import logging
from typing import Optional, Tuple, Any
import os # <-- Import os module

# 获取日志记录器
logger = logging.getLogger("utils")

# --- 对话框常量 ---
# Calculate absolute path for the font file based on this file's location
_UTILS_DIR = os.path.dirname(os.path.abspath(__file__))
_FONT_REL_PATH = os.path.join('..', 'assets', 'fonts', 'simhei.ttf') # Assumes assets/ is parallel to utils/
DEFAULT_FONT_PATH = os.path.abspath(os.path.join(_UTILS_DIR, _FONT_REL_PATH))
# DEFAULT_FONT_PATH = "assets/fonts/simhei.ttf" # Original line
DEFAULT_FONT_SIZE = 20
DIALOG_BG_COLOR = (50, 50, 70)
DIALOG_BORDER_COLOR = (70, 70, 90)
TEXT_COLOR = (220, 220, 220)
BUTTON_TEXT_COLOR = (255, 255, 255)
CONFIRM_BUTTON_COLOR = (60, 120, 60)
CANCEL_BUTTON_COLOR = (120, 60, 60)
OK_BUTTON_COLOR = (60, 100, 140)

DIALOG_WIDTH_RATIO = 0.8 # 对话框宽度占屏幕宽度的比例
DIALOG_MAX_WIDTH = 500
DIALOG_HEIGHT = 200
BUTTON_WIDTH = 100
BUTTON_HEIGHT = 40
BUTTON_MARGIN = 20
PADDING = 20
TEXT_LINE_SPACING = 30

class DialogBase:
    """对话框基类，处理通用绘制和布局。"""
    def __init__(self, message: str, screen_size: Tuple[int, int]):
        self.message = message
        self.screen_width, self.screen_height = screen_size
        self.font = self._load_font()
        self._active = True
        self._result: Optional[Any] = None

        # 计算对话框尺寸和位置
        self.dialog_width = min(DIALOG_MAX_WIDTH, int(self.screen_width * DIALOG_WIDTH_RATIO))
        self.dialog_height = DIALOG_HEIGHT # 可以根据文本内容动态调整，但为简化保持固定
        self.dialog_x = (self.screen_width - self.dialog_width) // 2
        self.dialog_y = (self.screen_height - self.dialog_height) // 2
        self.dialog_rect = pygame.Rect(self.dialog_x, self.dialog_y, self.dialog_width, self.dialog_height)

        # 创建对话框表面和渲染文本
        self.surface = self._create_surface()
        self._render_text()

    def _load_font(self) -> pygame.font.Font:
        """加载字体，如果失败则回退到默认字体。"""
        try:
            font = pygame.font.Font(DEFAULT_FONT_PATH, DEFAULT_FONT_SIZE)
            logger.info(f"成功加载字体: {DEFAULT_FONT_PATH}")
            return font
        except pygame.error as e:
            logger.warning(f"加载字体 {DEFAULT_FONT_PATH} 失败: {e}. 回退到默认字体。")
            return pygame.font.Font(None, DEFAULT_FONT_SIZE + 4) # 默认字体可能稍大

    def _create_surface(self) -> pygame.Surface:
        """创建对话框的基础表面。"""
        dialog_surface = pygame.Surface((self.dialog_width, self.dialog_height))
        dialog_surface.fill(DIALOG_BG_COLOR)
        pygame.draw.rect(dialog_surface, DIALOG_BORDER_COLOR, dialog_surface.get_rect(), 2)
        return dialog_surface

    def _render_text(self):
        """将消息文本渲染到对话框表面上。"""
        text_lines = []
        words = self.message.split(' ')
        current_line = ""
        max_width = self.dialog_width - 2 * PADDING

        for word in words:
            test_line = f"{current_line} {word}" if current_line else word
            try:
                text_width, _ = self.font.size(test_line)
                if text_width <= max_width:
                    current_line = test_line
                else:
                    text_lines.append(current_line)
                    current_line = word
            except Exception as e:
                 logger.error(f"字体渲染错误: {e} - 文本: '{test_line}'")
                 text_lines.append(current_line if current_line else word) # 尝试添加当前行或单词
                 current_line = "" # 重置以防万一


        if current_line:
            text_lines.append(current_line)

        text_y = PADDING + 10 # 初始Y位置
        for line in text_lines:
            try:
                text_surface = self.font.render(line, True, TEXT_COLOR)
                text_rect = text_surface.get_rect(centerx=self.dialog_width // 2, top=text_y)
                # 防止文本超出对话框底部（不包括按钮区域）
                if text_y + text_rect.height < self.dialog_height - BUTTON_HEIGHT - PADDING * 2:
                    self.surface.blit(text_surface, text_rect)
                    text_y += TEXT_LINE_SPACING
                else:
                    logger.warning("文本内容过多，无法完全显示在对话框中。")
                    break # 不再渲染更多行
            except Exception as e:
                logger.error(f"渲染文本行时出错: {e} - 文本: '{line}'")


    @property
    def is_active(self) -> bool:
        """对话框是否处于活动状态。"""
        return self._active

    def get_result(self) -> Optional[Any]:
        """获取对话框的结果（如果已关闭）。"""
        return self._result

    def handle_event(self, event: pygame.event.Event):
        """处理输入事件（由子类实现）。"""
        raise NotImplementedError

    def draw(self, screen: pygame.Surface):
        """将对话框绘制到屏幕上。"""
        if self._active:
            screen.blit(self.surface, (self.dialog_x, self.dialog_y))

    def _create_button(self, text: str, color: Tuple[int, int, int]) -> pygame.Surface:
        """创建按钮表面。"""
        button_surface = pygame.Surface((BUTTON_WIDTH, BUTTON_HEIGHT))
        button_surface.fill(color)
        text_surf = self.font.render(text, True, BUTTON_TEXT_COLOR)
        text_rect = text_surf.get_rect(center=(BUTTON_WIDTH // 2, BUTTON_HEIGHT // 2))
        button_surface.blit(text_surf, text_rect)
        return button_surface

class ConfirmDialog(DialogBase):
    """确认对话框（是/否）。"""
    def __init__(self, message: str, screen_size: Tuple[int, int]):
        super().__init__(message, screen_size)

        # 计算按钮位置
        buttons_y = self.dialog_height - BUTTON_HEIGHT - PADDING
        total_button_width = BUTTON_WIDTH * 2 + BUTTON_MARGIN
        start_x = (self.dialog_width - total_button_width) // 2

        self.yes_button_rel_x = start_x
        self.no_button_rel_x = start_x + BUTTON_WIDTH + BUTTON_MARGIN
        self.buttons_rel_y = buttons_y

        # 创建按钮表面
        self.yes_button_surf = self._create_button("确认", CONFIRM_BUTTON_COLOR)
        self.no_button_surf = self._create_button("取消", CANCEL_BUTTON_COLOR)

        # 计算按钮在屏幕上的绝对位置矩形
        self.yes_button_rect = pygame.Rect(
            self.dialog_x + self.yes_button_rel_x,
            self.dialog_y + self.buttons_rel_y,
            BUTTON_WIDTH, BUTTON_HEIGHT
        )
        self.no_button_rect = pygame.Rect(
            self.dialog_x + self.no_button_rel_x,
            self.dialog_y + self.buttons_rel_y,
            BUTTON_WIDTH, BUTTON_HEIGHT
        )

    def handle_event(self, event: pygame.event.Event):
        if not self._active:
            return

        result = None
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.yes_button_rect.collidepoint(event.pos):
                result = True
            elif self.no_button_rect.collidepoint(event.pos):
                result = False
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN or event.key == pygame.K_y:
                result = True
            elif event.key == pygame.K_ESCAPE or event.key == pygame.K_n:
                result = False

        if result is not None:
            self._result = result
            self._active = False
            logger.info(f"确认对话框关闭，结果: {self._result}")

    def draw(self, screen: pygame.Surface):
        if self._active:
            # 先绘制基础对话框（包括文本）
            super().draw(screen)
            # 再绘制按钮
            self.surface.blit(self.yes_button_surf, (self.yes_button_rel_x, self.buttons_rel_y))
            self.surface.blit(self.no_button_surf, (self.no_button_rel_x, self.buttons_rel_y))
            # 重新将包含按钮的surface绘制到屏幕
            screen.blit(self.surface, (self.dialog_x, self.dialog_y))


class MessageDialog(DialogBase):
    """消息提示框（确定）。"""
    def __init__(self, message: str, screen_size: Tuple[int, int]):
        super().__init__(message, screen_size)

        # 计算按钮位置
        button_x = (self.dialog_width - BUTTON_WIDTH) // 2
        button_y = self.dialog_height - BUTTON_HEIGHT - PADDING

        self.ok_button_rel_x = button_x
        self.ok_button_rel_y = button_y

        # 创建按钮表面
        self.ok_button_surf = self._create_button("确定", OK_BUTTON_COLOR)

        # 计算按钮在屏幕上的绝对位置矩形
        self.ok_button_rect = pygame.Rect(
            self.dialog_x + self.ok_button_rel_x,
            self.dialog_y + self.ok_button_rel_y,
            BUTTON_WIDTH, BUTTON_HEIGHT
        )

    def handle_event(self, event: pygame.event.Event):
        if not self._active:
            return

        closed = False
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.ok_button_rect.collidepoint(event.pos):
                closed = True
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN or event.key == pygame.K_ESCAPE:
                closed = True

        if closed:
            self._result = None # Message dialog doesn't really have a "result"
            self._active = False
            logger.info("消息对话框关闭")

    def draw(self, screen: pygame.Surface):
         if self._active:
            # 先绘制基础对话框（包括文本）
            super().draw(screen)
            # 再绘制按钮
            self.surface.blit(self.ok_button_surf, (self.ok_button_rel_x, self.ok_button_rel_y))
            # 重新将包含按钮的surface绘制到屏幕
            screen.blit(self.surface, (self.dialog_x, self.dialog_y))


# --- 示例用法 (注释掉，实际使用时应在主循环中处理) ---
"""
# 在你的Pygame主循环初始化后:
# screen = pygame.display.set_mode(...)
# screen_size = screen.get_size()
# current_dialog = None

# 在需要显示对话框的地方:
# if show_confirmation:
#    current_dialog = ConfirmDialog("确定要退出吗？", screen_size)
#    show_confirmation = False # 防止重复创建
# elif show_info:
#    current_dialog = MessageDialog("操作成功！", screen_size)
#    show_info = False

# 在主事件循环中:
# for event in pygame.event.get():
#     if event.type == pygame.QUIT:
#         running = False
#     # 将事件传递给活动对话框
#     if current_dialog and current_dialog.is_active:
#         current_dialog.handle_event(event)
#     else:
#         # 处理其他游戏事件...
#         pass

# 在主绘图循环中:
# screen.fill((0, 0, 0)) # 绘制游戏背景
# # ... 绘制其他游戏元素 ...

# # 如果有活动对话框，绘制它
# if current_dialog:
#     current_dialog.draw(screen)
#     # 检查对话框是否刚刚关闭
#     if not current_dialog.is_active:
#         result = current_dialog.get_result()
#         print(f"Dialog closed with result: {result}")
#         # 根据结果执行操作 (例如，如果确认退出，则 running = False)
#         if isinstance(current_dialog, ConfirmDialog) and result is True:
#              # Do something on confirm
#              pass
#         current_dialog = None # 清除对话框引用

# pygame.display.flip()
"""

# 移除旧的函数定义
# def show_confirm_dialog(message: str) -> bool: ...
# def show_message(message: str) -> None: ...

# (旧函数的代码已被完全移除)

# (旧函数的代码已被完全移除) 