[2025-05-24 13:29:50] [WARNING] [main.py:173] 游戏启动
[2025-05-24 13:29:50] [WARNING] [main.py:313] 开始创建游戏界面
[2025-05-24 13:29:52] [WARNING] [main.py:419] 游戏界面创建完成
[2025-05-24 13:31:30] [ERROR] [battle.py:965] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:31:30] [WARNING] [battle.py:977] 玩家对象为None，无法设置死亡状态
[2025-05-24 13:32:48] [ERROR] [battle.py:965] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:32:48] [WARNING] [battle.py:977] 玩家对象为None，无法设置死亡状态
[2025-05-24 13:33:14] [ERROR] [battle.py:965] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:33:14] [WARNING] [battle.py:977] 玩家对象为None，无法设置死亡状态
[2025-05-24 13:33:39] [ERROR] [battle.py:965] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:33:39] [WARNING] [battle.py:977] 玩家对象为None，无法设置死亡状态
[2025-05-24 13:34:22] [ERROR] [battle.py:965] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:34:22] [WARNING] [battle.py:977] 玩家对象为None，无法设置死亡状态
