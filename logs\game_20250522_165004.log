[2025-05-22 16:50:04] [WARNING] [main.py:105] 游戏启动
[2025-05-22 16:50:04] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 16:50:04] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 17:41:30] [ERROR] [battle.py:904] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 17:41:30] [ERROR] [battle.py:656] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead' and no __dict__ for setting new attributes
[2025-05-22 17:41:30] [ERROR] [battle.py:658] Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 648, in monster_attack
    self.handle_player_death()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 911, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead' and no __dict__ for setting new attributes

