# -*- coding: utf-8 -*-
"""
怪物面板
显示当前目标怪物的信息、生命值、属性等
"""

import pygame
from typing import Dict, Any, Optional, TYPE_CHECKING
from ui.screens.game_ui_base import GameUIPanel, GameUIConstants, UIComponentFactory
from utils.logger import logger
from utils.resource_manager import resources

if TYPE_CHECKING:
    from core.game import Game

class MonsterPanel(GameUIPanel):
    """怪物面板类"""
    
    def __init__(self, ui_manager, game_manager: 'Game', panel_rect: pygame.Rect):
        """
        初始化怪物面板
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
            panel_rect: 面板区域
        """
        super().__init__(ui_manager, game_manager, "monster_panel")
        self.panel_rect = panel_rect
        self.monster_components = {}
        self.update_interval = GameUIConstants.UPDATE_INTERVALS["monster_info"]
        self.monster_image_cache = {}
        
        self.create_components()
    
    def create_components(self):
        """创建怪物面板组件"""
        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            self.panel_rect,
            color=GameUIConstants.COLORS["panel_bg"],
            border_color=GameUIConstants.COLORS["panel_border"],
            border_width=GameUIConstants.SIZES["border_width"]
        )
        self.add_component(main_panel)
        self.components_map["main_panel"] = main_panel
        
        # 创建面板标题
        title_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 10,
            self.panel_rect.width - 40,
            30
        )
        title_text = self.ui_manager.create_text(
            title_rect,
            "目标信息",
            GameUIConstants.FONTS["normal"],
            GameUIConstants.COLORS["text_primary"],
            "center"
        )
        self.add_component(title_text)
        self.components_map["title"] = title_text
        
        # 创建怪物名称
        self._create_monster_name()
        
        # 创建怪物图片区域
        self._create_monster_image()
        
        # 创建怪物生命值
        self._create_monster_hp()
        
        # 创建怪物属性
        self._create_monster_stats()
        
        # 创建怪物状态
        self._create_monster_status()
        
        # 创建召唤物列表
        self._create_summon_list()
        
        logger.debug("怪物面板组件创建完成")
    
    def _create_monster_name(self):
        """创建怪物名称显示"""
        name_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 50,
            self.panel_rect.width - 40,
            30
        )
        name_text = self.ui_manager.create_text(
            name_rect,
            "无目标",
            GameUIConstants.FONTS["large"],
            GameUIConstants.COLORS["text_primary"],
            "center"
        )
        self.add_component(name_text)
        self.monster_components["name"] = name_text
    
    def _create_monster_image(self):
        """创建怪物图片显示区域"""
        # 图片区域
        image_rect = pygame.Rect(
            self.panel_rect.x + (self.panel_rect.width - 100) // 2,
            self.panel_rect.y + 90,
            100,
            100
        )
        
        # 创建图片背景面板
        image_panel = self.ui_manager.create_panel(
            image_rect,
            color=(20, 20, 30),
            border_color=GameUIConstants.COLORS["panel_border"],
            border_width=1
        )
        self.add_component(image_panel)
        self.monster_components["image_panel"] = image_panel
        
        # 图片将在update_data中动态绘制
        self.monster_components["image_rect"] = image_rect
    
    def _create_monster_hp(self):
        """创建怪物生命值显示"""
        # 生命值条
        hp_bar_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 210,
            self.panel_rect.width - 40,
            GameUIConstants.SIZES["bar_height"]
        )
        hp_bar = UIComponentFactory.create_progress_bar(
            self.ui_manager, hp_bar_rect, 0, 100, "hp"
        )
        self.add_component(hp_bar)
        self.monster_components["hp_bar"] = hp_bar
        
        # 生命值文本
        hp_text_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 235,
            self.panel_rect.width - 40,
            20
        )
        hp_text = self.ui_manager.create_text(
            hp_text_rect,
            "HP: 0/0",
            GameUIConstants.FONTS["small"],
            GameUIConstants.COLORS["text_primary"],
            "center"
        )
        self.add_component(hp_text)
        self.monster_components["hp_text"] = hp_text
    
    def _create_monster_stats(self):
        """创建怪物属性显示"""
        # 属性标题
        stats_title_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 270,
            self.panel_rect.width - 40,
            25
        )
        stats_title = self.ui_manager.create_text(
            stats_title_rect,
            "属性信息",
            GameUIConstants.FONTS["normal"],
            GameUIConstants.COLORS["text_secondary"],
            "left"
        )
        self.add_component(stats_title)
        
        # 属性列表
        stats_info = [
            ("等级", "0"),
            ("攻击", "0"),
            ("防御", "0"),
            ("经验", "0")
        ]
        
        y_offset = 300
        for i, (stat_name, stat_value) in enumerate(stats_info):
            stat_rect = pygame.Rect(
                self.panel_rect.x + 20,
                self.panel_rect.y + y_offset + i * GameUIConstants.SIZES["text_height"],
                self.panel_rect.width - 40,
                20
            )
            
            label_text, value_text = UIComponentFactory.create_stat_display(
                self.ui_manager, stat_rect, stat_name, stat_value
            )
            
            self.add_component(label_text)
            self.add_component(value_text)
            self.monster_components[f"{stat_name}_text"] = value_text
    
    def _create_monster_status(self):
        """创建怪物状态显示"""
        status_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 400,
            self.panel_rect.width - 40,
            30
        )
        status_text = self.ui_manager.create_text(
            status_rect,
            "状态: 无",
            GameUIConstants.FONTS["small"],
            (255, 200, 200),  # 淡红色
            "left"
        )
        self.add_component(status_text)
        self.monster_components["status_text"] = status_text
    
    def _create_summon_list(self):
        """创建召唤物列表"""
        # 召唤物标题
        summon_title_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 440,
            self.panel_rect.width - 40,
            25
        )
        summon_title = self.ui_manager.create_text(
            summon_title_rect,
            "召唤物",
            GameUIConstants.FONTS["normal"],
            GameUIConstants.COLORS["text_secondary"],
            "left"
        )
        self.add_component(summon_title)
        self.monster_components["summon_title"] = summon_title
        
        # 召唤物列表（最多显示3个）
        self.monster_components["summon_list"] = []
        for i in range(3):
            summon_rect = pygame.Rect(
                self.panel_rect.x + 20,
                self.panel_rect.y + 470 + i * 25,
                self.panel_rect.width - 40,
                20
            )
            summon_text = self.ui_manager.create_text(
                summon_rect,
                "",
                GameUIConstants.FONTS["small"],
                GameUIConstants.COLORS["text_primary"],
                "left"
            )
            self.add_component(summon_text)
            self.monster_components["summon_list"].append(summon_text)
    
    def update_data(self):
        """更新怪物数据"""
        if not hasattr(self.game_manager, 'current_enemy'):
            self._clear_monster_info()
            return
        
        current_enemy = self.game_manager.current_enemy
        if not current_enemy:
            self._clear_monster_info()
            return
        
        try:
            # 更新怪物名称
            if "name" in self.monster_components:
                self.monster_components["name"].set_text(current_enemy.name)
            
            # 更新怪物生命值
            if "hp_bar" in self.monster_components:
                self.monster_components["hp_bar"].update_value(
                    current_enemy.current_hp, current_enemy.max_hp
                )
            if "hp_text" in self.monster_components:
                self.monster_components["hp_text"].set_text(
                    f"HP: {current_enemy.current_hp}/{current_enemy.max_hp}"
                )
            
            # 更新怪物属性
            self._update_monster_stats(current_enemy)
            
            # 更新怪物状态
            self._update_monster_status(current_enemy)
            
            # 更新怪物图片
            self._update_monster_image(current_enemy)
            
        except Exception as e:
            logger.error(f"更新怪物面板数据时出错: {e}")
    
    def _update_monster_stats(self, monster):
        """更新怪物属性"""
        stats_mapping = {
            "等级": getattr(monster, 'level', 0),
            "攻击": getattr(monster, 'attack', 0),
            "防御": getattr(monster, 'defense', 0),
            "经验": getattr(monster, 'exp_reward', 0)
        }
        
        for stat_name, stat_value in stats_mapping.items():
            component_key = f"{stat_name}_text"
            if component_key in self.monster_components:
                self.monster_components[component_key].set_text(str(stat_value))
    
    def _update_monster_status(self, monster):
        """更新怪物状态"""
        if "status_text" in self.monster_components:
            status_list = []
            
            # 检查各种状态
            if hasattr(monster, 'is_poisoned') and monster.is_poisoned:
                status_list.append("中毒")
            
            if hasattr(monster, 'is_stunned') and monster.is_stunned:
                status_list.append("眩晕")
            
            if hasattr(monster, 'is_charmed') and monster.is_charmed:
                status_list.append("魅惑")
            
            # 更新状态显示
            if status_list:
                status_text = "状态: " + ", ".join(status_list)
            else:
                status_text = "状态: 无"
            
            self.monster_components["status_text"].set_text(status_text)
    
    def _update_monster_image(self, monster):
        """更新怪物图片"""
        # 这里可以添加怪物图片加载和显示逻辑
        # 由于图片处理比较复杂，暂时保留原有的图片显示方式
        pass
    
    def _clear_monster_info(self):
        """清空怪物信息"""
        # 清空名称
        if "name" in self.monster_components:
            self.monster_components["name"].set_text("无目标")
        
        # 清空生命值
        if "hp_bar" in self.monster_components:
            self.monster_components["hp_bar"].update_value(0, 100)
        if "hp_text" in self.monster_components:
            self.monster_components["hp_text"].set_text("HP: 0/0")
        
        # 清空属性
        for stat_name in ["等级", "攻击", "防御", "经验"]:
            component_key = f"{stat_name}_text"
            if component_key in self.monster_components:
                self.monster_components[component_key].set_text("0")
        
        # 清空状态
        if "status_text" in self.monster_components:
            self.monster_components["status_text"].set_text("状态: 无")
        
        # 清空召唤物列表
        if "summon_list" in self.monster_components:
            for summon_text in self.monster_components["summon_list"]:
                summon_text.set_text("")
    
    def update_summon_list(self):
        """更新召唤物列表"""
        if not hasattr(self.game_manager, 'player') or not self.game_manager.player:
            return
        
        player = self.game_manager.player
        if not hasattr(player, 'summons'):
            return
        
        summon_list = self.monster_components.get("summon_list", [])
        
        # 清空所有召唤物显示
        for summon_text in summon_list:
            summon_text.set_text("")
        
        # 显示当前召唤物
        active_summons = [summon for summon in player.summons if summon.is_alive()]
        
        for i, summon in enumerate(active_summons[:3]):  # 最多显示3个
            if i < len(summon_list):
                summon_info = f"{summon.name} HP:{summon.current_hp}/{summon.max_hp}"
                summon_list[i].set_text(summon_info)
