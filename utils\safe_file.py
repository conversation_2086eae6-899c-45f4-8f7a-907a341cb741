import os
import json
import shutil
import time
import chardet  # 直接导入chardet库
from typing import Dict, Any, Optional, Tuple

from utils.logger import logger

class SafeFileHandler:
    """安全的文件操作处理类"""
    
    # 常见的中文乱码替换表
    REPLACEMENTS = {
        "鎴樺＋": "战士",
        "鐢?": "男",
        "澶寸洈": "头盔",
        "椤归摼": "项链",
        "宸︽墜闀?": "左手镯",
        "鍙虫墜闀?": "右手镯",
        "闃插叿": "防具",
        "姝﹀櫒": "武器",
        "鍕嬬珷": "勋章",
        "宸︽垝鎸?": "左戒指",
        "鍙虫垝鎸?": "右戒指",
        "甯冭。(鐢?": "布衣(男)",
        "鏈ㄥ墤": "木剑",
        "鏅€?": "普通",
        "姣斿鐪?": "比奇省"
    }
    
    @staticmethod
    def safe_save(file_path: str, data: Dict[str, Any]) -> bool:
        """
        安全地保存数据到文件
        
        Args:
            file_path: 目标文件路径
            data: 要保存的数据
            
        Returns:
            bool: 是否成功保存
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建临时文件路径
        temp_path = f"{file_path}.temp"
        backup_path = f"{file_path}.bak"
        
        try:
            # 先写入临时文件 - 严格使用UTF-8编码并禁用ensure_ascii
            with open(temp_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 验证是否成功写入
            try:
                with open(temp_path, "r", encoding="utf-8") as f:
                    test_data = json.load(f)
                logger.info("临时文件UTF-8编码验证成功")
            except Exception as e:
                logger.error(f"写入验证失败: {e}")
                return False
            
            # 如果原文件存在，创建备份
            if os.path.exists(file_path):
                try:
                    shutil.copy2(file_path, backup_path)
                except Exception as e:
                    logger.warning(f"创建备份文件失败: {e}")
            
            # 重命名临时文件为正式文件
            if os.path.exists(file_path):
                os.remove(file_path)
            os.rename(temp_path, file_path)
            
            logger.info(f"文件已安全保存: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            # 如果临时文件存在，清理它
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False
    
    @staticmethod
    def safe_load(file_path: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        安全地加载文件数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            (成功标志, 加载的数据, 错误信息)
        """
        if not os.path.exists(file_path):
            # 检查是否有备份文件
            backup_path = f"{file_path}.bak"
            if os.path.exists(backup_path):
                logger.info(f"主文件不存在，尝试从备份恢复: {backup_path}")
                file_path = backup_path
            else:
                return False, None, "文件不存在"
        
        try:
            # 首先尝试直接用UTF-8读取
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    data = json.loads(content)
                    logger.info(f"成功使用UTF-8编码加载文件")
                    return True, data, ""
            except UnicodeDecodeError:
                logger.warning("UTF-8解码失败，尝试自动修复编码")
                # 直接尝试修复文件编码
                success, fixed_data = SafeFileHandler._fix_file_encoding(file_path)
                if success:
                    return True, fixed_data, ""
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败: {e}")
                # 尝试修复JSON格式错误
                success, fixed_data = SafeFileHandler._fix_json_format(file_path)
                if success:
                    return True, fixed_data, ""
                return False, None, f"JSON格式错误: {str(e)}"
            
            return False, None, "无法解析文件内容，所有已知编码尝试均失败"
            
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False, None, str(e)
    
    @staticmethod
    def _fix_file_encoding(file_path: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        修复文件编码问题
        
        Args:
            file_path: 文件路径
            
        Returns:
            (成功标志, 修复后的数据)
        """
        try:
            # 读取文件二进制内容
            with open(file_path, "rb") as f:
                content = f.read()
            
            # 检测编码
            result = chardet.detect(content)
            detected_encoding = result["encoding"]
            confidence = result["confidence"]
            
            logger.info(f"检测到编码: {detected_encoding}，置信度: {confidence}")
            
            # 尝试使用检测到的编码读取
            if detected_encoding and detected_encoding.lower() != "utf-8":
                try:
                    text = content.decode(detected_encoding)
                    data = json.loads(text)
                    logger.info(f"成功使用{detected_encoding}编码加载文件")
                    
                    # 自动修复文件编码
                    logger.info(f"尝试修复文件编码为UTF-8")
                    backup_before_fix = f"{file_path}.{detected_encoding}.bak"
                    shutil.copy2(file_path, backup_before_fix)
                    
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    logger.info(f"成功修复文件编码为UTF-8，原文件备份为: {backup_before_fix}")
                    
                    return True, data
                except Exception as e:
                    logger.error(f"使用检测到的编码({detected_encoding})处理失败: {e}")
            
            # 尝试常见编码
            for encoding in ["gbk", "gb2312", "big5", "latin1"]:
                try:
                    text = content.decode(encoding)
                    data = json.loads(text)
                    logger.info(f"成功使用{encoding}编码加载文件")
                    
                    # 自动修复文件编码
                    backup_before_fix = f"{file_path}.{encoding}.bak"
                    shutil.copy2(file_path, backup_before_fix)
                    
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    logger.info(f"成功修复文件编码为UTF-8，原文件备份为: {backup_before_fix}")
                    
                    return True, data
                except UnicodeDecodeError:
                    continue
                except json.JSONDecodeError as e:
                    logger.warning(f"使用{encoding}编码时JSON解析失败: {e}")
                    continue
            
            # 尝试替换中文编码问题
            try:
                logger.info("尝试使用中文字符替换修复方法")
                text = content.decode('latin1')  # latin1按字节转换不会失败
                
                # 替换已知的中文编码问题
                for corrupt, fix in SafeFileHandler.REPLACEMENTS.items():
                    text = text.replace(corrupt, fix)
                
                # 尝试解析修复后的内容
                fixed_data = json.loads(text)
                # 如果成功，重新保存为UTF-8
                backup_binary_fix = f"{file_path}.binary_fixed.bak"
                shutil.copy2(file_path, backup_binary_fix)
                
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                logger.info(f"成功通过中文字符替换修复并保存文件，原文件备份为: {backup_binary_fix}")
                return True, fixed_data
            except Exception as binary_err:
                logger.error(f"中文字符替换修复尝试失败: {binary_err}")
            
            return False, None
        except Exception as e:
            logger.error(f"修复文件编码失败: {e}")
            return False, None
    
    @staticmethod
    def _fix_json_format(file_path: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        修复JSON格式问题
        
        Args:
            file_path: 文件路径
            
        Returns:
            (成功标志, 修复后的数据)
        """
        try:
            with open(file_path, "rb") as f:
                raw_content = f.read()
            
            # 尝试用latin1读取并修复JSON格式
            text = raw_content.decode('latin1')
            # 修复常见JSON错误
            text = text.replace(',"}"', '}')
            text = text.replace(',""]', '"]')
            
            # 尝试解析修复后的内容
            try:
                fixed_data = json.loads(text)
                # 如果成功，重新保存为UTF-8
                backup_corrupted = f"{file_path}.corrupted.bak"
                shutil.copy2(file_path, backup_corrupted)
                
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                logger.info(f"成功修复JSON格式并保存，原文件备份为: {backup_corrupted}")
                return True, fixed_data
            except json.JSONDecodeError:
                pass
                
            return False, None
        except Exception as e:
            logger.error(f"修复JSON格式失败: {e}")
            return False, None