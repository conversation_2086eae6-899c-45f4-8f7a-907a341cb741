# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_all, collect_submodules

block_cipher = None

# 收集pygame的所有数据文件和模块
pygame_data = collect_all('pygame')

# 收集pystray和PIL的所有数据文件和模块
try:
    pystray_data = collect_all('pystray')
    pil_data = collect_all('PIL')
except Exception as e:
    print(f"警告: 收集pystray或PIL数据时出错: {e}")
    pystray_data = ([], [], [])
    pil_data = ([], [], [])

# 添加额外隐藏导入
hidden_imports = collect_submodules('pygame') + [
    'core.game',
    'core.version',
    'utils.resource_manager',
    'utils.logger',
    'ui.ui_manager',
    'json',
    'logging',
    'datetime',
    'locale',
    'pystray',  # 添加系统托盘库
    'PIL',      # 添加PIL库
    'PIL.Image',
]

# 定义数据文件
datas = [
    ('data', 'data'),
    ('utils', 'utils'),
    ('core', 'core'),
    ('ui', 'ui'),
    ('assets', 'assets'),
    ('resources', 'resources'),
]

# 合并pygame数据
datas += pygame_data[0]
binaries = pygame_data[1]
hidden_imports += pygame_data[2]

# 合并pystray和PIL数据
datas += pystray_data[0] + pil_data[0]
binaries += pystray_data[1] + pil_data[1]
hidden_imports += pystray_data[2] + pil_data[2]

# 主程序分析
a = Analysis(
    ['main.py'],
    pathex=[os.path.abspath('.')],
    binaries=binaries,
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 打包成单个可执行文件
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 文件夹模式
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='萝卜放置传奇',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    icon='assets/images/icon.png',
)

# 收集所有文件到集合中
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='萝卜放置传奇',
)