import random
import time
import json
import os
from typing import Dict, Tuple, Any, Optional, Union

from utils.logger import logger


class Summon:
    """召唤物实体类，代表玩家召唤的骷髅、神兽等"""
    
    def __init__(self, name, summon_type, level, hp, attack_range, defense=0, magic_defense=0, attack_speed=1.0, duration=-1):
        self.name = name
        self.summon_type = summon_type  # 召唤物类型，例如"skeleton_1"、"beast_2"等
        self.level = level
        self.hp = hp
        self.max_hp = hp
        self.defense = defense
        self.magic_defense = magic_defense
        self.attack_range = attack_range
        self.attack_speed = attack_speed
        
        # 召唤物特有属性
        self.owner = None  # 召唤者（玩家）引用
        self.duration = duration  # 持续时间（秒），-1表示永久
        self.summon_time = time.time()  # 召唤时间
        self.expire_time = self.summon_time + duration if duration > 0 else float('inf')  # 过期时间，负数表示永不过期
        self.force_expire = False  # 强制过期标志，用于角色死亡或下线时
        
        # 导入全局战斗速度修正因子
        from core.config import GameConfig
        # 应用全局战斗速度修正
        modified_speed = attack_speed * GameConfig.BATTLE_SPEED_MODIFIER
        
        # 根据攻击速度计算攻击间隔（单位：秒）
        self.attack_interval = 1.0 / modified_speed if modified_speed > 0 else float('inf')
        self.last_attack_time = 0
        self.is_dead = False
        
        # 基础属性
        self.accuracy = max(10, level * 1.5)  # 准确度比普通怪物高
        self.agility = max(5, level * 1.0)    # 敏捷性随等级提升
        
        # 记录召唤物初始化属性
        logger.debug(f"召唤物初始化: {name} - 类型:{summon_type} 等级:{level} 准确:{self.accuracy} 敏捷:{self.agility} 持续:{'永久' if duration <= 0 else f'{duration}秒'}")
    
    def set_owner(self, player):
        """设置召唤物的主人"""
        self.owner = player
    
    def force_dismiss(self):
        """强制解散召唤物（当玩家死亡或下线时）"""
        self.force_expire = True
        logger.debug(f"召唤物 {self.name} 被强制解散")
        
    def is_expired(self):
        """检查召唤物是否已过期"""
        # 如果设置了强制过期标志，直接返回True
        if self.force_expire:
            return True
        # 否则检查时间是否到期（仅当duration > 0时）
        if self.duration > 0:
            return time.time() >= self.expire_time
        # 持续时间为永久
        return False
    
    def get_remaining_time(self):
        """获取剩余时间（秒）"""
        if self.duration <= 0:
            return -1  # 表示永久
        return max(0, self.expire_time - time.time())
    
    def take_damage(self, damage):
        """受到伤害"""
        # 处理damage是元组的情况（可能包含暴击信息）
        if isinstance(damage, tuple):
            # 如果是 (damage, is_crit) 形式的元组，只取实际伤害值
            if len(damage) >= 1:
                damage = damage[0]
            else:
                logger.warning(f"无效的召唤物伤害值元组: {damage}")
                return 0
        
        # 确保damage是一个有效的数值
        if not isinstance(damage, (int, float)) or damage < 0:
            logger.warning(f"无效的召唤物伤害值: {damage}")
            return 0
            
        # 计算实际伤害（考虑召唤物防御）
        actual_damage = max(1, damage - self.defense)
        
        # 减少生命值
        self.hp -= actual_damage
        if self.hp <= 0:
            self.hp = 0
            self.is_dead = True
            
        # 返回实际伤害
        return actual_damage
    
    def calculate_damage(self):
        """计算伤害"""
        if isinstance(self.attack_range, dict):
            min_attack = self.attack_range.get("min", 1)
            max_attack = self.attack_range.get("max", 2)
        else:
            # 处理攻击范围
            try:
                min_attack, max_attack = self.attack_range
            except:
                min_attack, max_attack = 1, 2  # 默认值
                
        # 确保最小和最大值正确
        if min_attack > max_attack:
            min_attack, max_attack = max_attack, min_attack
            
        # 计算基础伤害
        base_damage = random.randint(int(min_attack), int(max_attack))
        
        # 伤害波动 (±10%)
        final_damage = int(base_damage * random.uniform(0.9, 1.1))
        
        # 考虑暴击 (召唤物有10%几率暴击，造成150%伤害)
        is_critical = random.random() < 0.1
        if is_critical:
            final_damage = int(final_damage * 1.5)
        
        # 确保至少造成1点伤害
        return max(1, final_damage), is_critical
    
    def attack_monster(self, monster):
        """攻击怪物"""
        # 命中判定
        hit_chance = min(95, max(30, (self.accuracy / (monster.agility + 1)) * 100))
        if random.randint(1, 100) > hit_chance:
            return 0, False  # 未命中
            
        # 计算伤害
        damage, is_critical = self.calculate_damage()
        
        # 应用伤害到怪物
        actual_damage = monster.take_damage(damage)
        
        return actual_damage, is_critical
    
    def update(self, current_time):
        """更新召唤物状态"""
        # 检查是否过期
        if self.is_expired():
            self.is_dead = True
            logger.debug(f"召唤物 {self.name} {'被强制解散' if self.force_expire else '已过期消失'}")
            return False
        
        # 检查所有者状态
        if self.owner and hasattr(self.owner, 'is_dead') and self.owner.is_dead:
            self.force_dismiss()
            return False
        
        return True

    def get_attack_cooldown_info(self, current_time):
        """获取攻击冷却信息，用于调试"""
        time_since_last_attack = current_time - self.last_attack_time
        time_until_next_attack = max(0, self.attack_interval - time_since_last_attack)
        cooldown_percent = max(0, min(100, (time_since_last_attack / self.attack_interval) * 100))
        
        return {
            "last_attack_time": self.last_attack_time,
            "attack_interval": self.attack_interval,
            "time_since_last": time_since_last_attack,
            "time_until_next": time_until_next_attack,
            "cooldown_percent": cooldown_percent,
            "ready_to_attack": time_since_last_attack >= self.attack_interval
        }


class SummonFactory:
    """召唤物工厂类，用于创建不同类型的召唤物"""
    
    # 召唤物配置数据
    _summon_configs = {}
    
    @classmethod
    def _load_summon_configs(cls):
        """从配置文件加载召唤物数据"""
        if not cls._summon_configs:
            try:
                # 首先尝试标准路径
                config_path = os.path.join("data", "configs", "summons.json")
                if os.path.exists(config_path):
                    with open(config_path, "r", encoding="utf-8") as f:
                        cls._summon_configs = json.load(f)
                    logger.debug(f"成功从 {config_path} 加载召唤物配置，共 {len(cls._summon_configs)} 条")
                    return
                    
                # 如果标准路径不存在，尝试在打包环境中查找
                import sys
                base_path = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
                
                # 尝试几种可能的路径
                possible_paths = [
                    os.path.join(base_path, "data", "configs", "summons.json"),
                    os.path.join(base_path, "_internal", "data", "configs", "summons.json"),
                    os.path.join(base_path, "data", "configs", "summons.json")
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        logger.info(f"在打包环境中找到召唤物配置: {path}")
                        with open(path, "r", encoding="utf-8") as f:
                            cls._summon_configs = json.load(f)
                        logger.debug(f"成功从打包环境加载召唤物配置，共 {len(cls._summon_configs)} 条")
                        return
                        
                # 加载失败，使用默认配置
                logger.warning("无法找到召唤物配置文件，使用默认配置")
                cls._init_default_configs()
                
            except Exception as e:
                logger.error(f"加载召唤物配置失败: {str(e)}")
                # 如果加载失败，使用默认配置
                logger.warning("使用默认召唤物配置")
                cls._init_default_configs()
                
    @classmethod
    def _init_default_configs(cls):
        """初始化默认的召唤物配置"""
        # 默认配置提供基本的骷髅和神兽
        cls._summon_configs = {
            "skeleton_0": {
                "name": "骷髅战士",
                "hp": 60,
                "attack_min": 10,
                "attack_max": 25,
                "defense": 8,
                "magic_defense": 5,
                "attack_speed": 0.5,
                "level": 0
            },
            "beast_0": {
                "name": "神兽",
                "hp": 300,
                "attack_min": 20,
                "attack_max": 30,
                "defense": 15,
                "magic_defense": 25,
                "attack_speed": 0.8,
                "level": 0
            }
        }
        logger.info(f"已初始化默认召唤物配置，共 {len(cls._summon_configs)} 条")
    
    @classmethod
    def create_summon(cls, summon_type, player_level, duration=-1):
        """创建一个新的召唤物
        
        Args:
            summon_type: 召唤物类型 (如 "skeleton" 或 "beast")
            player_level: 玩家等级，用于选择合适的召唤物等级
            duration: 持续时间（秒），默认为-1表示永久持续
            
        Returns:
            Summon: 召唤物实例
        """
        # 确保配置已加载
        cls._load_summon_configs()
        
        # 根据player_level选择合适等级的召唤物
        summon_level = min(7, max(0, player_level // 5))  # 每5级玩家等级对应1级召唤物，最高7级
        
        # 确定实际召唤物类型
        if summon_type.startswith("skeleton"):
            actual_type = f"skeleton_{summon_level}"
        elif summon_type.startswith("beast"):
            actual_type = f"beast_{summon_level}"
        else:
            actual_type = summon_type
        
        # 检查召唤物类型是否存在
        if actual_type not in cls._summon_configs:
            logger.error(f"未知的召唤物类型: {actual_type}")
            return None
        
        config = cls._summon_configs[actual_type]
        
        # 获取召唤物属性
        name = config["name"]
        level = config["level"]  # 使用配置中定义的等级
        hp = config["hp"]
        attack_min = config["attack_min"]
        attack_max = config["attack_max"]
        attack_range = [attack_min, attack_max]
        
        # 创建召唤物实例
        summon = Summon(
            name=name,
            summon_type=actual_type,
            level=level,
            hp=hp,
            attack_range=attack_range,
            defense=config["defense"],
            magic_defense=config["magic_defense"],
            attack_speed=config["attack_speed"],
            duration=duration
        )
        
        return summon 