[2025-05-01 22:53:35] [WARNING] [main.py:101] 游戏启动
[2025-05-01 22:53:36] [WARNING] [main.py:217] 开始创建游戏界面
[2025-05-01 22:53:36] [WARNING] [main.py:309] 游戏界面创建完成
[2025-05-01 22:53:47] [ERROR] [skills_screen.py:571] 更新技能槽位时出错: 'UIManager' object has no attribute 'get_screen'
[2025-05-01 22:53:47] [ERROR] [skills_screen.py:573] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\skills_screen.py", line 566, in _on_toggle_click
    game_screen = self.ui_manager.get_screen("game")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'UIManager' object has no attribute 'get_screen'. Did you mean: 'create_screen'?

[2025-05-01 22:53:50] [ERROR] [skills_screen.py:571] 更新技能槽位时出错: 'UIManager' object has no attribute 'get_screen'
[2025-05-01 22:53:50] [ERROR] [skills_screen.py:573] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\skills_screen.py", line 566, in _on_toggle_click
    game_screen = self.ui_manager.get_screen("game")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'UIManager' object has no attribute 'get_screen'. Did you mean: 'create_screen'?

[2025-05-01 22:54:10] [WARNING] [battle.py:648] 战斗日志: 获得了 168 经验值
