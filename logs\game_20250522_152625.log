[2025-05-22 15:26:25] [WARNING] [main.py:105] 游戏启动
[2025-05-22 15:26:26] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 15:26:26] [ERROR] [main.py:291] 创建商店界面失败: UIManager.create_button() got an unexpected keyword argument 'normal_color'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 288, in _create_screens
    shop_screen = ShopScreen(self.ui_manager, self.game)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\shop_screen.py", line 46, in __init__
    self._create_ui_elements()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\shop_screen.py", line 479, in _create_ui_elements
    self.buy_button = self.ui_manager.create_button(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: UIManager.create_button() got an unexpected keyword argument 'normal_color'
[2025-05-22 15:26:26] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 15:26:30] [ERROR] [ui_manager.py:333] 尝试显示不存在的界面: shop
