[2025-07-20 19:52:55] [WARNING] [main.py:173] 游戏启动
[2025-07-20 19:52:55] [WARNING] [main.py:313] 开始创建游戏界面
[2025-07-20 19:52:55] [ERROR] [main.py:425] 创建游戏界面过程中发生错误: name 'central_panel_top' is not defined
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 332, in _create_screens
    game_screen = GameScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\game_screen.py", line 141, in __init__
    self._create_components()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\game_screen.py", line 162, in _create_components
    self._create_central_area()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\game_screen.py", line 408, in _create_central_area
    log_panel_y = central_panel_top + 20  # 中央区域顶部加上20像素边距
                  ^^^^^^^^^^^^^^^^^
NameError: name 'central_panel_top' is not defined. Did you mean: 'central_panel'?
