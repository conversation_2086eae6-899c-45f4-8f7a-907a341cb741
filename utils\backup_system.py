import os
import json
import time
import importlib
from typing import Dict, List, Any, Optional, Tuple

from utils.logger import logger
from utils.security import DataSecurity

class BackupSystem:
    """游戏数据备份系统"""
    
    def __init__(self, backup_dir: str = "data/backups"):
        """初始化备份系统"""
        self.backup_dir = backup_dir
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, player_data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """创建角色属性备份"""
        try:
            # 如果没有提供文件名，使用时间戳生成
            if filename is None:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"save_{timestamp}.json.bak"
            
            # 确保文件扩展名正确
            if not filename.endswith(".bak"):
                filename += ".bak"
            
            # 完整的备份文件路径
            backup_path = os.path.join(self.backup_dir, filename)
            
            # 添加备份元数据
            backup_data = player_data.copy()
            backup_data["_backup_time"] = time.time()
            backup_data["_backup_version"] = "1.0"
            
            # 直接将数据保存为JSON，不再使用加密
            with open(backup_path, "w", encoding="utf-8") as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功创建备份: {filename}")
            return filename
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return ""
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有可用的备份"""
        try:
            backups = []
            
            # 遍历备份目录中的所有文件
            for filename in os.listdir(self.backup_dir):
                if filename.endswith(".bak"):
                    full_path = os.path.join(self.backup_dir, filename)
                    
                    try:
                        # 获取文件属性
                        file_stat = os.stat(full_path)
                        
                        # 尝试解析备份信息
                        backup_info = self._get_backup_info(full_path)
                        
                        backups.append({
                            "filename": filename,
                            "path": full_path,
                            "size": file_stat.st_size,
                            "created_time": file_stat.st_ctime,
                            "character_class": backup_info.get("character_class", "未知"),
                            "level": backup_info.get("level", 0),
                            "valid": backup_info.get("valid", False)
                        })
                    except Exception as e:
                        logger.warning(f"读取备份文件'{filename}'信息失败: {e}")
                        backups.append({
                            "filename": filename,
                            "path": full_path,
                            "size": file_stat.st_size,
                            "created_time": file_stat.st_ctime,
                            "character_class": "无法读取",
                            "level": 0,
                            "valid": False
                        })
            
            # 按创建时间降序排序
            backups.sort(key=lambda x: x["created_time"], reverse=True)
            return backups
        except Exception as e:
            logger.error(f"列出备份失败: {e}")
            return []
    
    def _get_backup_info(self, backup_path: str) -> Dict[str, Any]:
        """获取备份文件的基本信息"""
        try:
            # 直接读取并解析JSON，不再使用解密
            with open(backup_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 提取基本信息
            info = {
                "valid": True,
                "character_class": data.get("character_class", "未知"),
                "level": data.get("level", 0),
                "backup_time": data.get("_backup_time", 0),
                "backup_version": data.get("_backup_version", "")
            }
            return info
        except Exception as e:
            logger.warning(f"读取备份信息失败: {e}")
            return {"valid": False}
    
    def restore_backup(self, backup_path: str) -> Tuple[bool, Dict[str, Any], str]:
        """从备份恢复角色属性"""
        try:
            # 如果提供的是文件名而不是完整路径
            if not os.path.isabs(backup_path):
                backup_path = os.path.join(self.backup_dir, backup_path)
            
            # 检查文件是否存在
            if not os.path.exists(backup_path):
                return False, {}, f"备份文件'{backup_path}'不存在"
            
            # 直接读取并解析JSON，不再使用解密
            try:
                with open(backup_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
            except json.JSONDecodeError as e:
                return False, {}, f"备份数据无效: {str(e)}"
            
            # 从备份数据中移除备份元数据
            data.pop("_backup_time", None)
            data.pop("_backup_version", None)
            
            logger.info(f"成功恢复备份: {os.path.basename(backup_path)}")
            return True, data, "恢复成功"
        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return False, {}, f"恢复出错: {str(e)}"
    
    def delete_backup(self, backup_path: str) -> bool:
        """删除备份文件"""
        try:
            # 如果提供的是文件名而不是完整路径
            if not os.path.isabs(backup_path):
                backup_path = os.path.join(self.backup_dir, backup_path)
            
            # 检查文件是否存在
            if not os.path.exists(backup_path):
                logger.warning(f"要删除的备份文件'{backup_path}'不存在")
                return False
            
            # 删除文件
            os.remove(backup_path)
            logger.info(f"成功删除备份: {os.path.basename(backup_path)}")
            return True
        except Exception as e:
            logger.error(f"删除备份失败: {e}")
            return False
    
    def get_class_modifiers(self, character_class: str) -> Dict[str, float]:
        """获取职业属性修正"""
        try:
            # 动态导入GameConfig模块
            spec = importlib.util.find_spec("core.config")
            if spec:
                config_module = importlib.import_module("core.config")
                GameConfig = getattr(config_module, "GameConfig", None)
            else:
                # 向后兼容尝试从main导入
                main_spec = importlib.util.find_spec("Untitled-2")
                if main_spec:
                    main_module = importlib.import_module("Untitled-2")
                    GameConfig = getattr(main_module, "GameConfig", None)
                else:
                    GameConfig = None
            
            if GameConfig and hasattr(GameConfig, "CLASS_STATS"):
                class_stats = getattr(GameConfig, "CLASS_STATS")
                if character_class in class_stats:
                    return class_stats[character_class]
            
            # 默认修正值
            return {
                "accuracy": 13,
                "agility": 15,
                "hp_modifier": 1.0,
                "mp_modifier": 1.0,
                "attack_modifier": 1.0,
                "defense_modifier": 1.0,
                "magic_defense": 0,
                "magic_dodge": 0
            }
        except Exception as e:
            logger.error(f"获取职业修正失败: {e}")
            # 返回默认值
            return {
                "accuracy": 13,
                "agility": 15,
                "hp_modifier": 1.0,
                "mp_modifier": 1.0,
                "attack_modifier": 1.0,
                "defense_modifier": 1.0,
                "magic_defense": 0,
                "magic_dodge": 0
            }