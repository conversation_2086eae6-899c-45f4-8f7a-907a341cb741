from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

class PlayerBase(BaseModel):
    """玩家基本信息"""
    name: str
    character_class: str
    gender: str

class PlayerCreate(PlayerBase):
    """创建玩家请求模型"""
    pass

class PlayerInDB(PlayerBase):
    """数据库中的玩家模型"""
    id: str = Field(alias="_id")
    user_id: str
    level: int = 1
    exp: int = 0
    gold: int = 0
    yuanbao: int = 5
    vip_level: int = 0
    vip_activated: List[int] = []
    hp: int = 0
    max_hp: int = 0
    mp: int = 0
    max_mp: int = 0
    attack: int = 0
    defense: int = 0
    attack_speed: float = 1.0
    crit_rate: int = 5
    base_accuracy: int = 70
    agility: int = 0
    luck: int = 0
    magic_defense: int = 0
    magic_dodge: int = 0
    equipment: Dict[str, Any] = {}
    inventory: List[Dict[str, Any]] = []
    storage: List[Dict[str, Any]] = []
    locked_items: List[str] = []
    skills: Dict[str, Any] = {}
    disabled_skills: List[str] = []
    skill_slots: Dict[str, Any] = {}
    skill_proficiencies: Dict[str, int] = {}
    created_at: datetime = datetime.now()
    last_updated: datetime = datetime.now()

class Player(PlayerBase):
    """API响应中的玩家模型"""
    id: str
    user_id: str
    level: int
    exp: int
    gold: int
    yuanbao: int
    vip_level: int
    hp: int
    max_hp: int
    mp: int
    max_mp: int
    attack: int
    defense: int
    equipment: Dict[str, Any]
    inventory: List[Dict[str, Any]]
    skills: Dict[str, Any]

    class Config:
        from_attributes = True

class GameState(BaseModel):
    """游戏状态模型"""
    id: Optional[str] = Field(None, alias="_id")
    player_id: str
    current_map: str = "比奇省"
    in_battle: bool = False
    auto_battle: bool = False
    battle_stats: Dict[str, Any] = {
        "exp_gained": 0,
        "gold_gained": 0,
        "monsters_killed": 0,
        "battles_count": 0
    }
    last_updated: datetime = datetime.now()

    class Config:
        from_attributes = True
