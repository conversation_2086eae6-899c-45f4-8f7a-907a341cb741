"""
职业成长属性模块

包含各职业的基础属性和等级成长表
"""

from typing import Dict, List, Union, Any
from utils.logger import logger

# 职业基础属性配置
# 战士：高准确(17)，中敏捷(15)
# 法师：低准确(5)，中敏捷(15)
# 道士：中准确(13)，高敏捷(18)
CLASS_STATS = {
    "战士": {
        "accuracy": 17,
        "agility": 15,
        "hp_modifier": 1,  # 生命值倍率
        "mp_modifier": 1,  # 魔法值倍率
        "attack_modifier": 1.2,  # 攻击力倍率
        "defense_modifier": 1.1,  # 防御力倍率
        "magic_attack_modifier": 0.6,  # 魔法攻击力倍率
        "tao_attack_modifier": 0.7,  # 道术攻击力倍率
        "magic_defense": 0,  # 魔法防御
        "magic_dodge": 0,     # 魔法闪避率
    },
    "法师": {
        "accuracy": 5,
        "agility": 15,
        "hp_modifier": 1,
        "mp_modifier": 1,
        "attack_modifier": 0.7,
        "defense_modifier": 1,
        "magic_attack_modifier": 1.3,  # 魔法攻击力倍率
        "tao_attack_modifier": 0.8,  # 道术攻击力倍率
        "magic_defense": 10,
        "magic_dodge": 10,
    },
    "道士": {
        "accuracy": 13,
        "agility": 18,
        "hp_modifier": 1.0,
        "mp_modifier": 1.0,
        "attack_modifier": 0.8,
        "defense_modifier": 1.0,
        "magic_attack_modifier": 0.8,  # 魔法攻击力倍率
        "tao_attack_modifier": 1.2,  # 道术攻击力倍率
        "magic_defense": 5,
        "magic_dodge": 10,
    }
}

# 战士职业等级成长表
# 等级: [HP, MP, 攻击下限, 攻击上限, 防御下限, 防御上限, 魔法攻击下限, 魔法攻击上限, 道术攻击下限, 道术攻击上限, 魔抗, 攻速]
WARRIOR_STATS = {
    1: [19, 15, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1.0],
    5: [44, 18, 1, 2, 0, 0, 0, 0, 0, 0, 0, 1.0],
    10: [89, 46, 1, 2, 0, 1, 0, 0, 0, 0, 0, 1.0],
    15: [149, 63, 2, 3, 0, 2, 0, 0, 0, 0, 0, 1.0],
    20: [224, 81, 3, 4, 0, 4, 0, 0, 0, 0, 0, 1.0],
    25: [314, 99, 4, 5, 0, 5, 0, 0, 0, 0, 0, 1.0],
    30: [419, 116, 5, 6, 0, 6, 0, 0, 0, 0, 0, 1.0],
    35: [539, 133, 6, 7, 0, 7, 0, 0, 0, 0, 0, 1.0],
    40: [674, 151, 7, 8, 0, 8, 0, 0, 0, 0, 0, 1.0],
    45: [824, 169, 8, 9, 0, 9, 0, 0, 0, 0, 0, 1.0],
    50: [984, 187, 9, 10, 0, 10, 0, 0, 0, 0, 0, 1.0],
    55: [1159, 205, 10, 11, 0, 11, 0, 0, 0, 0, 0, 1.0],
    60: [1334, 223, 11, 12, 1, 12, 0, 0, 0, 0, 0, 1.0],
    65: [1519, 241, 12, 13, 1, 13, 0, 0, 0, 0, 0, 1.0],
    70: [1704, 259, 13, 14, 1, 14, 0, 0, 0, 0, 0, 1.0],
    75: [1899, 277, 14, 15, 1, 15, 0, 0, 0, 0, 0, 1.0],
    80: [2094, 295, 15, 16, 2, 16, 0, 0, 0, 0, 0, 1.0],
    85: [2299, 313, 16, 17, 2, 17, 0, 0, 0, 0, 0, 1.0],
    90: [2494, 331, 17, 18, 2, 18, 0, 0, 0, 0, 0, 1.0],
    95: [2699, 349, 18, 19, 2, 19, 0, 0, 0, 0, 0, 1.0],
    100: [2894, 367, 19, 20, 3, 20, 0, 0, 0, 0, 0, 1.0],
}

# 法师职业等级成长表
# 等级: [HP, MP, 攻击下限, 攻击上限, 防御下限, 防御上限, 魔法攻击下限, 魔法攻击上限, 道术攻击下限, 道术攻击上限, 魔抗, 攻速]
MAGE_STATS = {
    1: [16, 18, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0.8],
    5: [25, 46, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0.8],
    10: [39, 71, 0, 0, 0, 0, 1, 3, 0, 0, 0, 0.8],
    15: [53, 178, 0, 0, 0, 0, 2, 5, 0, 0, 0, 0.8],
    20: [77, 277, 0, 0, 0, 0, 3, 7, 0, 0, 0, 0.8],
    25: [101, 391, 0, 0, 0, 0, 4, 9, 0, 0, 0, 0.8],
    30: [128, 545, 0, 0, 0, 0, 5, 11, 0, 0, 0, 0.8],
    35: [159, 706, 0, 0, 0, 0, 6, 13, 0, 0, 0, 0.8],
    40: [193, 894, 0, 0, 0, 0, 7, 15, 0, 0, 0, 0.8],
    45: [239, 1108, 0, 0, 0, 0, 8, 17, 0, 0, 0, 0.8],
    50: [276, 1362, 0, 0, 0, 0, 9, 19, 0, 0, 0, 0.8],
    55: [315, 1516, 0, 0, 0, 0, 10, 21, 0, 0, 0, 0.8],
    60: [362, 1870, 0, 0, 0, 0, 11, 23, 0, 0, 0, 0.8],
    65: [317, 1624, 0, 0, 0, 0, 12, 25, 0, 0, 0, 0.8],
    70: [344, 1778, 0, 0, 0, 0, 13, 27, 0, 0, 0, 0.8],
    75: [371, 1932, 0, 0, 0, 0, 14, 29, 0, 0, 0, 0.8],
    80: [398, 2086, 0, 0, 0, 0, 15, 31, 0, 0, 0, 0.8],
    85: [425, 2240, 0, 0, 0, 0, 16, 33, 0, 0, 0, 0.8],
    90: [452, 2494, 0, 0, 0, 0, 17, 35, 0, 0, 0, 0.8],
    95: [479, 2648, 0, 0, 0, 0, 18, 37, 0, 0, 0, 0.8],
    100: [506, 2902, 0, 0, 0, 0, 19, 39, 0, 0, 0, 0.8],
}

# 道士职业等级成长表
# 等级: [HP, MP, 攻击下限, 攻击上限, 防御下限, 防御上限, 魔法攻击下限, 魔法攻击上限, 道术攻击下限, 道术攻击上限, 魔抗, 攻速]
TAOIST_STATS = {
    1: [17, 13, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0.9],
    5: [31, 20, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0.9],
    10: [56, 41, 0, 0, 0, 0, 0, 0, 2, 4, 0, 0.9],
    15: [89, 75, 0, 0, 0, 1, 0, 0, 3, 6, 0, 0.9],
    20: [131, 123, 0, 0, 0, 2, 0, 0, 4, 8, 0, 0.9],
    25: [181, 185, 0, 0, 0, 3, 0, 0, 5, 10, 0, 0.9],
    30: [239, 261, 0, 0, 0, 4, 0, 0, 6, 12, 0, 0.9],
    35: [306, 350, 0, 0, 0, 5, 0, 0, 7, 14, 0, 0.9],
    40: [381, 453, 0, 0, 0, 6, 0, 0, 8, 16, 0, 0.9],
    45: [464, 570, 0, 0, 0, 7, 0, 0, 9, 18, 0, 0.9],
    50: [553, 700, 0, 0, 0, 8, 0, 0, 10, 20, 0, 0.9],
    55: [656, 845, 0, 0, 0, 9, 0, 0, 11, 22, 0, 0.9],
    60: [764, 1000, 0, 0, 0, 10, 0, 0, 12, 24, 0, 0.9],
    65: [865, 1150, 0, 0, 0, 11, 0, 0, 13, 26, 0, 0.9],
    70: [973, 1325, 0, 0, 0, 12, 0, 0, 14, 28, 0, 0.9],
    75: [1080, 1480, 0, 0, 0, 13, 0, 0, 15, 30, 0, 0.9],
    80: [1200, 1600, 0, 0, 0, 14, 0, 0, 15, 32, 0, 0.9],
    85: [1320, 1740, 0, 0, 0, 15, 0, 0, 17, 34, 0, 0.9],
    90: [1440, 1825, 0, 0, 0, 16, 0, 0, 18, 36, 0, 0.9],
    95: [1560, 1980, 0, 0, 0, 17, 0, 0, 19, 38, 0, 0.9],
    100: [1680, 2100, 0, 0, 0, 18, 0, 0, 20, 40, 0, 0.9],
}

def get_class_stats(character_class: str) -> Dict[str, Any]:
    """获取职业属性"""
    try:
        if character_class not in CLASS_STATS:
            logger.warning(f"无效的职业: {character_class}，使用默认职业'战士'")
            character_class = "战士"
        
        class_stats = CLASS_STATS[character_class]
        
        # 从职业成长表获取1级职业的基础属性
        base_stats = get_level_stats(character_class, 1)
        logger.debug(f"获取职业stats - 基础属性: {base_stats}")
        
        # 确保base_stats长度足够
        if len(base_stats) < 12:
            logger.debug(f"扩展基础属性数组，原长度: {len(base_stats)}")
            # 扩展到至少12个元素
            while len(base_stats) < 12:
                base_stats.append(0)
            base_stats[11] = 1.0  # 确保攻速有默认值
            logger.debug(f"扩展后的基础属性: {base_stats}")
        
        # 应用职业修正
        hp = int(base_stats[0] * class_stats["hp_modifier"])
        mp = int(base_stats[1] * class_stats["mp_modifier"])
        attack = int(base_stats[2] * class_stats["attack_modifier"])
        defense = int(base_stats[4] * class_stats["defense_modifier"])
        magic_attack = int(base_stats[6] * class_stats.get("magic_attack_modifier", 1.0))
        tao_attack = int(base_stats[8] * class_stats.get("tao_attack_modifier", 1.0))
        attack_speed = base_stats[11]
        
        result = class_stats.copy()
        # 确保base_stats列表包含足够的元素，包括物理攻击、魔法攻击和道术攻击
        result["base_stats"] = [hp, mp, attack, defense, magic_attack, tao_attack, 0, 0, 0, 0, 0, attack_speed]
        # 移除默认暴击率，暴击率现在完全由装备决定
        
        logger.debug(f"职业 {character_class} 的计算结果: {result}")
        return result
    except Exception as e:
        logger.error(f"获取职业属性出错: {e}")
        # 提供一个默认返回值以防止程序崩溃
        default_result = {
            "accuracy": 10,
            "agility": 10,
            "hp_modifier": 1.0,
            "mp_modifier": 1.0,
            "attack_modifier": 1.0,
            "defense_modifier": 1.0,
            "magic_attack_modifier": 1.0,
            "tao_attack_modifier": 1.0,
            "magic_defense": 0,
            "magic_dodge": 0,
            "base_stats": [20, 20, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1.0],  # [hp, mp, 物攻, 防御, 魔攻, 道攻, ...]
            "crit_rate": 1  # 默认暴击率降低为1%
        }
        return default_result

def get_level_stats(character_class: str, level: int) -> List[Union[int, float]]:
    """根据职业和等级获取对应的属性"""
    # 选择对应的职业成长表
    if character_class == "战士":
        stats_table = WARRIOR_STATS
    elif character_class == "法师":
        stats_table = MAGE_STATS
    elif character_class == "道士":
        stats_table = TAOIST_STATS
    else:
        logger.warning(f"未知职业: {character_class}，使用战士职业数据")
        stats_table = WARRIOR_STATS
    
    # 检查是否直接有对应的等级
    if level in stats_table:
        return stats_table[level]
    
    # 寻找最接近的等级：一个小于当前等级，一个大于当前等级
    lower_key = None
    upper_key = None
    
    # 先找出小于等于当前等级的最大等级
    for key in stats_table.keys():
        if key <= level and (lower_key is None or key > lower_key):
            lower_key = key
    
    # 再找出大于当前等级的最小等级
    for key in stats_table.keys():
        if key > level and (upper_key is None or key < upper_key):
            upper_key = key
    
    # 如果找到了上下两个等级，则进行线性插值
    if lower_key is not None and upper_key is not None:
        lower_stats = stats_table[lower_key]
        upper_stats = stats_table[upper_key]
        
        # 计算插值系数
        range_size = upper_key - lower_key
        position = level - lower_key
        ratio = position / range_size
        
        # 计算插值后的属性值
        interpolated_stats = []
        for i in range(len(lower_stats)):
            # 对每个属性进行线性插值
            value = lower_stats[i] + (upper_stats[i] - lower_stats[i]) * ratio
            # 整数属性保持整数
            if isinstance(lower_stats[i], int) and isinstance(upper_stats[i], int):
                value = int(value)
            interpolated_stats.append(value)
        
        logger.debug(f"为等级 {level} 生成插值属性: {interpolated_stats} (基于等级 {lower_key} 和 {upper_key})")
        return interpolated_stats
    
    # 如果只找到了下限等级
    elif lower_key is not None:
        base_stats = stats_table[lower_key]
        # 计算增长率
        level_diff = level - lower_key
        
        # 获取所有相邻等级对，计算平均增长率
        growth_rates = []
        keys = sorted(stats_table.keys())
        for i in range(len(keys) - 1):
            current_level = keys[i]
            next_level = keys[i + 1]
            current_stats = stats_table[current_level]
            next_stats = stats_table[next_level]
            level_diff_sample = next_level - current_level
            
            # 计算每个属性的平均增长率
            rates = []
            for j in range(len(current_stats)):
                if level_diff_sample > 0:
                    rate = (next_stats[j] - current_stats[j]) / level_diff_sample
                    rates.append(rate)
                else:
                    rates.append(0)
            growth_rates.append(rates)
        
        # 计算平均增长率
        avg_growth_rates = []
        for j in range(len(base_stats)):
            rates = [rates[j] for rates in growth_rates if rates]
            if rates:
                avg_growth_rates.append(sum(rates) / len(rates))
            else:
                avg_growth_rates.append(0)
        
        # 生成新的属性值
        extrapolated_stats = []
        for j in range(len(base_stats)):
            # 根据平均增长率计算新值
            value = base_stats[j] + avg_growth_rates[j] * level_diff
            # 整数属性保持整数
            if isinstance(base_stats[j], int):
                value = int(value)
            extrapolated_stats.append(value)
        
        logger.debug(f"为等级 {level} 生成外推属性: {extrapolated_stats} (基于等级 {lower_key} 和平均增长率)")
        return extrapolated_stats
    
    # 如果没有找到合适的等级，返回默认值
    default_stats = {
        "战士": [19, 15, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1.0],
        "法师": [15, 20, 1, 1, 0, 0, 1, 1, 0, 0, 5, 0.8],
        "道士": [17, 18, 1, 1, 0, 0, 0, 0, 1, 1, 3, 0.9]
    }
    
    logger.warning(f"无法为{character_class}职业等级{level}找到合适的属性，使用默认值")
    return default_stats.get(character_class, [19, 15, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1.0]) 