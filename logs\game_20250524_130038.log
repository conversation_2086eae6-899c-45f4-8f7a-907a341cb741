[2025-05-24 13:00:38] [WARNING] [main.py:173] 游戏启动
[2025-05-24 13:00:38] [WARNING] [main.py:313] 开始创建游戏界面
[2025-05-24 13:00:40] [WARNING] [main.py:419] 游戏界面创建完成
[2025-05-24 13:00:49] [ERROR] [main.py:938] 游戏发生未处理的异常: 'GameScreen' object has no attribute 'skill_slots'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 934, in main
    game_instance.run() # run() 现在包含清理逻辑
    ^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 443, in run
    self._update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 521, in _update
    self.ui_manager.update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\ui_manager.py", line 393, in update
    self.screens[self.active_screen].update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 2222, in update
    self._auto_cast_skills(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3507, in _auto_cast_skills
    for slot_idx in self.skill_slots.keys():
                    ^^^^^^^^^^^^^^^^
AttributeError: 'GameScreen' object has no attribute 'skill_slots'
