[2025-05-02 10:01:09] [WARNING] [main.py:105] 游戏启动
[2025-05-02 10:01:09] [WARNING] [main.py:223] 开始创建游戏界面
[2025-05-02 10:01:09] [ERROR] [main.py:276] 创建背包界面失败: 'ResourceManager' object has no attribute 'get_font'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 273, in _create_screens
    inventory_screen = InventoryScreen(self.ui_manager, self.game)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\inventory_screen.py", line 39, in __init__
    self._create_ui_elements()
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\inventory_screen.py", line 477, in _create_ui_elements
    self.inventory_list = ScrollableList(
                          ^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\components.py", line 602, in __init__
    self.item_font = item_font or resources.get_font(DEFAULT_FONT_NAME, DEFAULT_FONT_SIZE_NORMAL)
                                  ^^^^^^^^^^^^^^^^^^
AttributeError: 'ResourceManager' object has no attribute 'get_font'. Did you mean: 'get_sound'?
[2025-05-02 10:01:09] [WARNING] [main.py:315] 游戏界面创建完成
