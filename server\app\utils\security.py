from datetime import datetime, timedelta
from typing import Optional
from jose import jwt
from passlib.context import CryptContext
from app.config import settings
import logging

logger = logging.getLogger(__name__)

# 密码哈希上下文
# 使用更简单的哈希算法，避免兼容性问题
pwd_context = CryptContext(schemes=["sha256_crypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        # 记录详细的调试信息
        logger.debug(f"验证密码: 明文长度={len(plain_password)}, 哈希长度={len(hashed_password)}")
        logger.debug(f"哈希值前20个字符: {hashed_password[:20]}...")

        # 尝试验证密码
        result = pwd_context.verify(plain_password, hashed_password)
        logger.debug(f"密码验证结果: {result}")

        # 如果验证失败，尝试直接比较（仅用于调试）
        if not result:
            logger.warning("密码验证失败，尝试直接比较")
            direct_match = (plain_password == hashed_password)
            logger.debug(f"直接比较结果: {direct_match}")
            # 注意：这里不返回直接比较的结果，仅用于调试

        return result
    except Exception as e:
        logger.error(f"密码验证失败: {e}", exc_info=True)
        return False

def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    try:
        # 记录详细的调试信息
        logger.debug(f"生成密码哈希: 明文长度={len(password)}")

        # 生成哈希
        hashed = pwd_context.hash(password)
        logger.debug(f"生成的哈希长度={len(hashed)}")
        logger.debug(f"哈希值前20个字符: {hashed[:20]}...")

        return hashed
    except Exception as e:
        logger.error(f"生成密码哈希失败: {e}", exc_info=True)
        raise

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()

    # 设置过期时间
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    # 添加过期时间到令牌数据
    to_encode.update({"exp": expire})

    # 编码JWT
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Failed to create access token: {e}")
        raise
