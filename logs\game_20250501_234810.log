[2025-05-01 23:48:10] [WARNING] [main.py:101] 游戏启动
[2025-05-01 23:48:10] [WARNING] [main.py:217] 开始创建游戏界面
[2025-05-01 23:48:10] [ERROR] [main.py:312] 创建游戏界面过程中发生错误: UIManager.create_text() got an unexpected keyword argument 'wrap'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 236, in _create_screens
    game_screen = GameScreen(self.ui_manager, self.game)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 97, in __init__
    self._create_components()
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 115, in _create_components
    self._create_player_panel()
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 322, in _create_player_panel
    buff_text = self.ui_manager.create_text(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: UIManager.create_text() got an unexpected keyword argument 'wrap'
