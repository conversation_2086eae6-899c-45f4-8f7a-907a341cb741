# -*- mode: python ; coding: utf-8 -*-
import os
import sys
import json
import pygame
from typing import Dict, Any, Optional, Union, List, Tuple, TYPE_CHECKING

from utils.logger import logger

# 全局单例实例
resources = None

class ResourceManager:
    """资源管理器，负责加载和管理游戏资源（已彻底移除音效和音乐系统）"""

    _instance = None  # 单例模式

    # 资源缓存
    _images: Dict[str, pygame.Surface] = {}
    _fonts: Dict[Tuple[str, int], pygame.font.Font] = {}
    _json_data: Dict[str, Any] = {}

    @classmethod
    def get_instance(cls) -> 'ResourceManager':
        """获取资源管理器的单例实例"""
        if cls._instance is None:
            cls._instance = ResourceManager()
        return cls._instance

    def __init__(self):
        """初始化资源管理器"""
        self.images: Dict[str, pygame.Surface] = {}
        self.fonts: Dict[str, str] = {}
        self._json_data: Dict[str, Any] = {}

        # 确定是否在打包环境中运行
        self.is_frozen = getattr(sys, 'frozen', False)
        logger.info(f"资源管理器初始化，打包状态: {'已打包' if self.is_frozen else '未打包'}")

        # 资源基础路径
        self.base_path = self._get_base_path()
        logger.info(f"资源基础路径: {self.base_path}")

        # 资源路径 - 使用assets目录
        self.resource_root = os.path.join(self.base_path, "assets")
        self.image_path = os.path.join(self.resource_root, "images")
        self.font_path = os.path.join(self.resource_root, "fonts")

        # 确保资源目录存在
        self._ensure_resource_dirs()
        logger.info("资源管理器初始化完成（无音效/音乐系统）")

    def _get_base_path(self) -> str:
        """获取应用程序基础路径，处理打包与非打包环境"""
        if self.is_frozen:
            # 检查是否在单文件模式下（使用_MEIPASS临时目录）
            if hasattr(sys, '_MEIPASS'):
                base_path = getattr(sys, '_MEIPASS')
                logger.info(f"使用 PyInstaller 临时目录: {base_path}")
                return base_path
            # 常规打包环境
            return os.path.dirname(sys.executable)
        else:
            # 开发环境 (假设 utils 在项目根目录的子目录中)
            # 如果 resource_manager.py 直接在根目录，用 os.path.abspath(".")
            # 使用 __file__ 来定位，更可靠
            # 项目根目录通常是 utils 目录的上层目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            logger.info(f"开发环境项目根目录: {project_root}")
            return project_root # 返回项目根目录

    def _get_resource_path(self, relative_path: str) -> str:
        """获取资源文件的完整路径，使用初始化时确定的基础路径。"""
        # 始终使用在 __init__ 中计算好的 self.base_path
        full_path = os.path.join(self.base_path, relative_path)

        logger.debug(f"Calculated resource path: {full_path} (base: {self.base_path}, relative: {relative_path})")

        return full_path

    def _ensure_resource_dirs(self):
        """确保资源目录存在"""
        for path in [self.resource_root, self.image_path, self.font_path]:
            if not os.path.exists(path):
                # 如果在打包环境中，不尝试创建目录
                if self.is_frozen:
                    logger.warning(f"打包环境中资源目录不存在: {path}")
                else:
                    try:
                        os.makedirs(path)
                        logger.info(f"创建资源目录: {path}")
                    except Exception as e:
                        logger.warning(f"无法创建资源目录 {path}: {e}")

    def initialize(self):
        """初始化并加载基本资源（无音效/音乐）"""
        logger.info("初始化资源管理器...")
        # 加载默认字体
        self._load_default_fonts()
        # 尝试加载基本图像资源
        self._load_basic_images()
        logger.info("资源管理器初始化完成（音效和音乐系统已彻底移除）")

    def _load_default_fonts(self):
        """加载默认字体"""
        # 设置中文字体路径
        chinese_font_paths = [
            os.path.join(self.font_path, "simhei.ttf"),  # 黑体
            os.path.join(self.font_path, "simsun.ttc"),  # 宋体
            os.path.join(self.font_path, "msyh.ttc"),    # 微软雅黑
            "C:\\Windows\\Fonts\\simhei.ttf",            # Windows 系统字体
            "C:\\Windows\\Fonts\\simsun.ttc",
            "C:\\Windows\\Fonts\\msyh.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"  # Linux 系统字体
        ]

        # 查找可用的中文字体
        chinese_font_found = False
        for font_path in chinese_font_paths:
            if os.path.exists(font_path):
                self.fonts["chinese"] = font_path
                logger.info(f"已找到中文字体: {font_path}")
                chinese_font_found = True
                break

        if not chinese_font_found:
            logger.warning("未找到中文字体，将使用系统默认字体")

    def _load_basic_images(self):
        """加载基本图像资源"""
        try:
            # 日志打包环境和资源路径信息，方便调试
            logger.info(f"图像资源根目录: {self.image_path}, 是否打包环境: {self.is_frozen}")
            if not os.path.exists(self.image_path):
                logger.warning(f"图像资源根目录不存在: {self.image_path}")
                if self.is_frozen:
                    # 尝试在打包环境中查找备用路径
                    base_path = os.path.dirname(sys.executable)
                    internal_path = os.path.join(base_path, "_internal")
                    if os.path.exists(internal_path):
                        alternate_path = os.path.join(internal_path, "assets", "images")
                        logger.info(f"尝试_internal路径: {alternate_path}")
                        if os.path.exists(alternate_path):
                            self.image_path = alternate_path
                            logger.info(f"使用_internal图像路径: {self.image_path}")
                        else:
                            logger.error(f"_internal图像目录不存在")
                            return
                    else:
                        alternate_path = os.path.join(base_path, "assets", "images")
                        logger.info(f"尝试base路径: {alternate_path}")
                        if os.path.exists(alternate_path):
                            self.image_path = alternate_path
                            logger.info(f"使用base图像路径: {self.image_path}")
                        else:
                            logger.error(f"无法找到图像资源目录")
                            return

            # 可以保留这个日志，表示基础图像加载完成（虽然现在没加载什么）
            logger.info(f"已加载 {len(self.images)} 个基础图像资源")
        except Exception as e:
            logger.error(f"加载图像资源时出错: {e}", exc_info=True)

    def load_image(self, name: str) -> Optional[pygame.Surface]:
        """
        加载图像

        参数:
            name: 图像名称或路径

        返回:
            加载的图像Surface对象，如果加载失败则返回None
        """
        # 如果已经加载过，直接返回
        if name in self.images:
            return self.images[name]

        # --- 修改: 使用辅助方法获取绝对路径 ---
        full_path = self._get_resource_path(name)
        # --- 结束修改 ---

        # 日志输出，方便调试
        logger.info(f"尝试加载图像: {name} (完整路径: {full_path})")

        # 检查是否为文件路径 (使用完整路径检查)
        if os.path.exists(full_path):
            try:
                image = pygame.image.load(full_path) # 使用完整路径加载
                if full_path.lower().endswith('.jpg') or full_path.lower().endswith('.bmp'):
                    image = image.convert()
                else:
                    image = image.convert_alpha()
                self.images[name] = image # 缓存时仍使用相对路径名作为 key
                logger.debug(f"成功加载并缓存图像: {name}")
                return image
            except Exception as e:
                logger.warning(f"无法加载图像 {full_path}: {e}")
                return None
        else:
            logger.warning(f"图像文件不存在: {full_path}")
            return None

    def get_font_path(self, name: str) -> Optional[str]:

        return self.fonts.get(name)

    def get_image(self, name: str) -> Optional[pygame.Surface]:

        return self.images.get(name)

    def unload_image(self, name: str) -> None:
        """
        卸载图像

        参数:
            name: 图像名称
        """
        if name in self.images:
            del self.images[name]

    def cleanup(self) -> None:
        """清理所有资源"""
        self.images.clear()
        self.fonts.clear()
        self._json_data.clear() # 清理JSON缓存
        logger.info("资源管理器已清理所有资源")

    def load_json_data(self, file_path, default=None):
        """加载 JSON 数据文件，兼容开发和打包环境。"""
        if file_path in self._json_data:
            logger.info(f"从缓存中加载JSON数据: {file_path}")
            return self._json_data[file_path]

        # 使用 _get_resource_path 获取正确的绝对路径
        absolute_path = self._get_resource_path(file_path)

        logger.info(f"尝试加载JSON文件，计算出的绝对路径: {absolute_path}")

        try:
            if not os.path.exists(absolute_path):
                logger.error(f"文件不存在: {absolute_path} (原始请求: {file_path})")
                if self.is_frozen:
                     logger.error(f"请确保 '{file_path}' 已正确添加到 .spec 文件的 'datas' 列表中，或者检查 _get_resource_path 的逻辑。")
                return default or {}

            with open(absolute_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 记录成功加载
            logger.info(f"成功加载JSON数据: {absolute_path}, 数据大小: {len(str(data))} 字节")

            # 缓存数据
            self._json_data[file_path] = data
            return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {absolute_path}, {e}")
            return default or {}
        except Exception as e:
            logger.error(f"加载JSON数据失败: {absolute_path}, {e}", exc_info=True)
            return default or {}

    # 添加空的音频相关方法以兼容现有代码
    def get_sound(self, name: str) -> Optional[Any]:
        logger.debug(f"尝试获取已移除的音效: {name}")
        return None

    def get_music_path(self, name: str) -> Optional[str]:
        logger.debug(f"尝试获取已移除的音乐: {name}")
        return None

    def set_music_volume(self, volume: float) -> None:

        logger.debug(f"尝试设置已移除的音乐音量: {volume}")

    def set_sound_volume(self, volume: float) -> None:
        logger.debug(f"尝试设置已移除的音效音量: {volume}")

    def get_music_volume(self) -> float:
        return 0.0

    def get_sound_volume(self) -> float:
        return 0.0

    def get_map_preview_path(self, map_name: str) -> str:
        """获取指定地图预览图片的完整路径."""
        relative_path = os.path.join("assets", "map", f"{map_name}.jpg")
        # TODO:
        full_path = self._get_resource_path(relative_path)
        logger.debug(f"构造地图预览路径: {full_path} (基于 map_name: {map_name})")
        return full_path

# 创建全局资源管理器实例
resources = ResourceManager.get_instance()