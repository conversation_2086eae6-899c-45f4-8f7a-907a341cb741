[2025-05-24 15:14:19] [WARNING] [main.py:173] 游戏启动
[2025-05-24 15:14:19] [WARNING] [main.py:313] 开始创建游戏界面
[2025-05-24 15:14:19] [ERROR] [main.py:425] 创建游戏界面过程中发生错误: list indices must be integers or slices, not str
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 320, in _create_screens
    character_creation = CharacterCreation(self.ui_manager, self.game)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\character_creation.py", line 45, in __init__
    self._create_components()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\character_creation.py", line 270, in _create_components
    self._get_class_stats("战士"),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\character_creation.py", line 383, in _get_class_stats
    stats_text = f"生命值: {base_stats['hp']}\n"
                            ~~~~~~~~~~^^^^^^
TypeError: list indices must be integers or slices, not str
