"""
类型提示模块，用于解决循环导入问题
"""
from typing import Dict, List, Any, Optional, Tuple, Union, Callable, TypeVar, TYPE_CHECKING

# 定义类型变量
if TYPE_CHECKING:
    from core.game import Game
    from core.player import Player
    from core.monster import Monster
    from core.battle import BattleSystem
    from utils.resource_manager import ResourceManager
    from ui.ui_manager import UIManager

# 类型别名
GameType = TypeVar('GameType', bound='Game')
PlayerType = TypeVar('PlayerType', bound='Player')
MonsterType = TypeVar('MonsterType', bound='Monster')
BattleSystemType = TypeVar('BattleSystemType', bound='BattleSystem')
ResourceManagerType = TypeVar('ResourceManagerType', bound='ResourceManager')
UIManagerType = TypeVar('UIManagerType', bound='UIManager')

# 常用的字典类型
EquipmentDict = Dict[str, Any]
SkillDict = Dict[str, Any]
InventoryItemDict = Dict[str, Any]
MapDict = Dict[str, Any]
