import pygame
import logging
import time
from datetime import datetime, timedelta
import random
from ui.components import Panel, Button, Text, UIComponent
from ui.ui_manager import Screen

# 设置日志记录器
logger = logging.getLogger(__name__)

class SignInScreen(Screen):
    """签到系统界面类"""

    def __init__(self, ui_manager, game_manager):
        """初始化签到界面"""
        super().__init__("signin")

        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.title = "每日签到"

        # 签到奖励配置
        self.daily_rewards = {
            "normal": [
                {"type": "yuanbao", "amount": 1, "description": "1 元宝"},
                {"type": "gold", "amount": 50000, "description": "50000 金币"}
            ],
            "weekly": {"type": "blessing_oil", "amount": 1, "description": "1 祝福油"},
            "biweekly": {"type": "treasure_box", "amount": 1, "description": "1 宝箱"}
        }

        # 签到记录的保存路径（将在on_show时根据角色设置）
        self.signin_data_path = None

        # 签到记录
        self.signin_records = {
            "last_signin_time": 0,
            "signin_days": 0,
            "total_signin_days": 0
        }

        # 创建界面元素
        self._create_ui_elements()
        logger.info("签到界面初始化完成")

    def _setup_character_signin_data(self):
        """根据当前角色设置签到数据路径"""
        player = self.game_manager.player
        if not player:
            logger.warning("无法设置角色签到数据：玩家不存在")
            return

        # 根据角色名称创建独立的签到记录文件
        character_name = player.name
        safe_name = "".join(c for c in character_name if c.isalnum() or c in ('-', '_'))  # 清理文件名
        self.signin_data_path = f"data/saves/signin_records_{safe_name}.json"

        logger.info(f"设置角色 {character_name} 的签到数据路径: {self.signin_data_path}")

        # 加载该角色的签到记录
        self._load_signin_records()

    def _create_ui_elements(self):
        """创建界面元素"""
        logger.info("开始创建签到界面元素")
        screen_size = pygame.display.get_surface().get_size()

        # 顶部面板（标题）
        top_panel_height = 60
        top_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            color=(35, 35, 55),
            border_color=(70, 70, 120),
            border_width=2
        )
        self.add_component(top_panel)

        # 标题文本
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            self.title,
            "chinese_title",
            (220, 220, 220),
            "center"
        )
        self.add_component(title_text)

        # 返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 10, 100, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        # 设置返回按钮颜色
        back_button.colors.update({
            "normal": (45, 45, 80),
            "hover": (60, 60, 100),
            "pressed": (35, 35, 70),
            "text": (230, 230, 230),
            "border": (90, 90, 140)
        })
        self.add_component(back_button)

        # 添加右上角VIP按钮
        vip_button = self.ui_manager.create_button(
            pygame.Rect(screen_size[0] - 120, 10, 100, 40),
            "VIP特权",
            self._on_vip_click,
            "chinese_normal"
        )
        # 设置VIP按钮颜色 - 使用金色调
        vip_button.colors.update({
            "normal": (80, 60, 20),
            "hover": (100, 80, 30),
            "pressed": (70, 50, 15),
            "text": (255, 215, 0),  # 金色文字
            "border": (130, 100, 40)
        })
        self.add_component(vip_button)

        # 主内容区域
        content_area = Panel(
            pygame.Rect(20, top_panel_height + 20, screen_size[0] - 40, screen_size[1] - top_panel_height - 80),
            color=(25, 25, 45),
            border_color=(50, 50, 80),
            border_width=2
        )
        self.add_component(content_area)
        self.content_area = content_area

        # 当前签到状态显示
        status_rect = pygame.Rect(
            content_area.rect.left + 20,
            content_area.rect.top + 20,
            content_area.rect.width - 40,
            40
        )
        self.status_text = self.ui_manager.create_text(
            status_rect,
            "今日签到状态: 未签到",
            "chinese_large",
            (255, 215, 0),  # 金色
            "center"
        )
        self.add_component(self.status_text)

        # 累计签到天数显示
        days_rect = pygame.Rect(
            content_area.rect.left + 20,
            status_rect.bottom + 10,
            content_area.rect.width - 40,
            30
        )
        self.days_text = self.ui_manager.create_text(
            days_rect,
            "累计签到: 0 天",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(self.days_text)

        # 签到奖励说明文本
        reward_desc_rect = pygame.Rect(
            content_area.rect.left + 20,
            days_rect.bottom + 20,
            content_area.rect.width - 40,
            60
        )
        reward_desc_text = self.ui_manager.create_text(
            reward_desc_rect,
            "每日签到可获得1元宝和50000金币\n累计满7天额外获得1个祝福油\n累计满15天额外获得1个宝箱（随机获得1件装备）",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(reward_desc_text)

        # 签到按钮
        signin_button_rect = pygame.Rect(
            (screen_size[0] - 200) // 2,
            reward_desc_rect.bottom + 30,
            200,
            60
        )
        self.signin_button = self.ui_manager.create_button(
            signin_button_rect,
            "签到",
            self._on_signin_click,
            "chinese_large"
        )
        # 设置签到按钮样式
        self.signin_button.colors.update({
            "normal": (60, 100, 60),  # 绿色调
            "hover": (70, 120, 70),
            "pressed": (50, 90, 50),
            "text": (230, 230, 230),
            "border": (100, 150, 100)
        })
        self.add_component(self.signin_button)

        # 当前奖励情况面板
        rewards_panel = Panel(
            pygame.Rect(
                content_area.rect.left + 50,
                signin_button_rect.bottom + 40,
                content_area.rect.width - 100,
                150
            ),
            color=(30, 30, 50),
            border_color=(60, 60, 90),
            border_width=1
        )
        self.add_component(rewards_panel)

        # 添加奖励显示
        rewards_title_rect = pygame.Rect(
            rewards_panel.rect.left,
            rewards_panel.rect.top + 10,
            rewards_panel.rect.width,
            30
        )
        rewards_title = self.ui_manager.create_text(
            rewards_title_rect,
            "当前奖励进度",
            "chinese_normal",
            (220, 220, 220),
            "center"
        )
        self.add_component(rewards_title)

        # 日常奖励
        daily_rect = pygame.Rect(
            rewards_panel.rect.left + 20,
            rewards_title_rect.bottom + 10,
            rewards_panel.rect.width - 40,
            25
        )
        self.daily_text = self.ui_manager.create_text(
            daily_rect,
            "日常奖励: 1元宝 + 50000金币 [未领取]",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.daily_text)

        # 周奖励
        weekly_rect = pygame.Rect(
            rewards_panel.rect.left + 20,
            daily_rect.bottom + 10,
            rewards_panel.rect.width - 40,
            25
        )
        self.weekly_text = self.ui_manager.create_text(
            weekly_rect,
            "7天奖励: 1祝福油 (0/7天)",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.weekly_text)

        # 双周奖励
        biweekly_rect = pygame.Rect(
            rewards_panel.rect.left + 20,
            weekly_rect.bottom + 10,
            rewards_panel.rect.width - 40,
            25
        )
        self.biweekly_text = self.ui_manager.create_text(
            biweekly_rect,
            "15天奖励: 1宝箱 (0/15天)",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.biweekly_text)

        logger.info("签到界面元素创建完成")

    def on_show(self):
        """当界面显示时调用"""
        logger.info("签到界面显示")
        # 设置当前角色的签到数据
        self._setup_character_signin_data()
        # 更新签到状态显示
        self._update_signin_display()

    def _on_back_click(self):
        """处理返回按钮点击"""
        logger.info("点击了返回按钮，返回游戏界面")
        self.ui_manager.show_screen("game")

    def _on_vip_click(self):
        """处理VIP按钮点击"""
        logger.info("点击了VIP按钮，打开VIP特权界面")
        self.ui_manager.show_screen("vip")

    def _on_signin_click(self):
        """处理签到按钮点击"""
        logger.info("点击了签到按钮")
        player = self.game_manager.player
        if not player:
            logger.warning("签到失败：玩家数据不存在")
            self.ui_manager.show_message("签到失败", "无法找到玩家数据")
            return

        # 检查是否已经签到
        current_time = time.time()
        if self._is_already_signed_today():
            logger.info("今日已签到，无需重复签到")
            self.ui_manager.show_message("已签到", "您今天已经签到过了，请明天再来！")
            return

        # 记录签到
        last_signin_time = self.signin_records["last_signin_time"]

        # 检查是否连续签到
        is_consecutive = self._is_consecutive_signin(last_signin_time, current_time)

        # 更新签到记录
        self.signin_records["last_signin_time"] = current_time
        if is_consecutive:
            self.signin_records["signin_days"] += 1
        else:
            # 不连续则重置连续签到计数
            self.signin_records["signin_days"] = 1

        # 总签到天数始终增加
        self.signin_records["total_signin_days"] += 1

        # 保存签到记录
        self._save_signin_records()

        # 准备奖励信息
        rewards_text = []
        rewards = []

        # 每日奖励（1元宝 + 50000金币）
        daily_rewards = self.daily_rewards["normal"]
        for reward in daily_rewards:
            rewards.append({"type": reward["type"], "amount": reward["amount"]})
        rewards_text.append("每日奖励: 1元宝 + 50000金币")

        # 检查是否满足7天奖励
        if self.signin_records["signin_days"] % 7 == 0:
            weekly_reward = self.daily_rewards["weekly"]
            rewards.append({"type": weekly_reward["type"], "amount": weekly_reward["amount"]})
            rewards_text.append(f"7天奖励: {weekly_reward['description']}")

        # 检查是否满足15天奖励
        if self.signin_records["signin_days"] % 15 == 0:
            biweekly_reward = self.daily_rewards["biweekly"]
            rewards.append({"type": biweekly_reward["type"], "amount": biweekly_reward["amount"]})
            rewards_text.append(f"15天奖励: {biweekly_reward['description']}")

        # 发放奖励
        self._give_rewards(player, rewards)

        # 重置副本次数
        self._reset_dungeon_attempts()

        # 更新界面显示
        self._update_signin_display()

        # 显示签到成功消息
        self.ui_manager.show_message("签到成功", "恭喜您成功签到！\n" + "\n".join(rewards_text))

    def _is_already_signed_today(self):
        """检查今天是否已经签到"""
        last_signin_time = self.signin_records["last_signin_time"]
        if last_signin_time == 0:
            return False

        # 获取上次签到的日期
        last_signin_date = datetime.fromtimestamp(last_signin_time).date()
        # 获取当前日期
        current_date = datetime.now().date()

        # 如果上次签到日期是今天，则已经签到
        return last_signin_date == current_date

    def _is_consecutive_signin(self, last_time, current_time):
        """检查是否连续签到"""
        if last_time == 0:
            return True  # 首次签到视为连续

        # 获取上次签到的日期
        last_date = datetime.fromtimestamp(last_time).date()
        # 获取当前日期
        current_date = datetime.now().date()
        # 计算前一天的日期
        yesterday = current_date - timedelta(days=1)

        # 如果上次签到是昨天，则是连续签到
        return last_date == yesterday

    def _load_signin_records(self):
        """加载签到记录"""
        try:
            import json
            import os

            # 检查签到数据路径是否已设置
            if not self.signin_data_path:
                logger.warning("签到数据路径未设置，无法加载记录")
                return

            if not os.path.exists(self.signin_data_path):
                logger.info(f"签到记录文件不存在: {self.signin_data_path}，使用默认值")
                return

            with open(self.signin_data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.signin_records = data
                logger.info(f"成功加载角色签到记录: {data}")
        except Exception as e:
            logger.error(f"加载签到记录失败: {e}")
            # 保持默认记录不变

    def _save_signin_records(self):
        """保存签到记录"""
        try:
            import json
            import os

            # 检查签到数据路径是否已设置
            if not self.signin_data_path:
                logger.warning("签到数据路径未设置，无法保存记录")
                return

            # 确保保存目录存在
            os.makedirs(os.path.dirname(self.signin_data_path), exist_ok=True)

            with open(self.signin_data_path, 'w', encoding='utf-8') as f:
                json.dump(self.signin_records, f)
                logger.info(f"成功保存角色签到记录: {self.signin_records}")
        except Exception as e:
            logger.error(f"保存签到记录失败: {e}")

    def _give_rewards(self, player, rewards):
        """发放奖励"""
        for reward in rewards:
            reward_type = reward["type"]
            amount = reward["amount"]

            if reward_type == "yuanbao":
                # 发放元宝
                player.yuanbao += amount
                logger.info(f"发放元宝奖励: {amount}，当前元宝: {player.yuanbao}")

            elif reward_type == "gold":
                # 发放金币
                player.gold += amount
                logger.info(f"发放金币奖励: {amount}，当前金币: {player.gold}")

            elif reward_type == "refresh_token":
                # 发放商店刷新符
                # 这里假设商店刷新符是通过特定方法添加的
                if hasattr(player, "add_refresh_token"):
                    player.add_refresh_token(amount)
                    logger.info(f"发放商店刷新符: {amount}个")
                else:
                    # 如果没有专门的方法，可以尝试直接修改属性
                    if not hasattr(player, "refresh_tokens"):
                        player.refresh_tokens = 0
                    player.refresh_tokens += amount
                    logger.info(f"发放商店刷新符: {amount}个，当前: {player.refresh_tokens}个")

            elif reward_type == "blessing_oil":
                # 发放祝福油
                # 添加到玩家背包
                from core.item import Item
                blessing_oil = Item.create_from_template("祝福油", "装备强化")
                if blessing_oil:
                    player.add_item(blessing_oil)
                    logger.info(f"发放祝福油: {amount}个")
                else:
                    logger.error("无法创建祝福油物品")

            elif reward_type == "treasure_box":
                # 发放宝箱，实际上是直接随机一件装备加入背包
                self._give_treasure_box_reward(player, amount)

    def _give_treasure_box_reward(self, player, amount):
        """处理宝箱奖励，随机生成装备"""
        try:
            from core.config import GameConfig

            # 确保装备配置已加载
            if not GameConfig.loaded_equipment:
                GameConfig.load_equipment()
                logger.info("已加载装备配置库")

            # 获取装备数据库
            equipment_db = GameConfig.loaded_equipment.get("equipment_db", {})
            if not equipment_db:
                logger.warning("装备数据库为空，无法生成宝箱奖励")
                return

            # 为每个宝箱生成一件装备
            for _ in range(amount):
                # 随机选择装备类型
                equipment_types = ["武器", "防具", "勋章", "首饰"]
                weights = [40, 30, 15, 15]  # 各类型的权重
                equipment_type = random.choices(equipment_types, weights=weights, k=1)[0]

                if equipment_type == "首饰":
                    # 首饰有子类型
                    accessory_types = list(equipment_db.get("首饰", {}).keys())
                    if not accessory_types:
                        logger.warning("未找到首饰子类型")
                        continue
                    accessory_type = random.choice(accessory_types)
                    items = equipment_db.get("首饰", {}).get(accessory_type, [])
                    # 确保设置正确的子类型
                    for item in items:
                        if isinstance(item, dict) and "type" not in item:
                            item["type"] = accessory_type
                else:
                    # 其他类型直接获取
                    items = equipment_db.get(equipment_type, [])

                if not items:
                    logger.warning(f"类型 {equipment_type} 没有可用装备")
                    continue

                # 筛选等级适合的装备（不超过玩家等级）
                player_level = player.level
                suitable_items = [item for item in items if item.get("level", 1) <= player_level]

                if not suitable_items:
                    # 如果没有适合的，则选择最低等级的
                    suitable_items = sorted(items, key=lambda x: x.get("level", 1))

                if suitable_items:
                    # 随机选择一件装备
                    selected_item = random.choice(suitable_items)

                    # 复制一份添加到背包
                    item_copy = selected_item.copy()

                    # 添加到玩家背包
                    player.inventory.append(item_copy)

                    logger.info(f"宝箱奖励: 获得装备 {item_copy.get('name')}，类型: {item_copy.get('type')}，等级: {item_copy.get('level', 1)}")

                    # 显示获得装备的消息
                    self.ui_manager.show_message(
                        "宝箱奖励",
                        f"恭喜获得装备: {item_copy.get('name')}\n类型: {item_copy.get('type')}\n等级: {item_copy.get('level', 1)}"
                    )
        except Exception as e:
            logger.error(f"处理宝箱奖励时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _reset_dungeon_attempts(self):
        """重置副本挑战次数

        在签到成功后调用该方法，重置玩家的副本挑战次数
        """
        logger.info("签到成功，准备重置副本次数...")
        if hasattr(self.game_manager, 'dungeon_attempts'):
            # 获取配置中的默认次数
            try:
                from core.config import GameConfig
                default_attempts = GameConfig.DUNGEON_DEFAULT_ATTEMPTS
            except Exception as e:
                logger.error(f"无法从配置加载默认副本次数: {e}, 使用硬编码值")
                default_attempts = {
                    "gold": 3,
                    "yuanbao": 2,
                    "equip": 1
                }

            # 重置次数
            self.game_manager.dungeon_attempts = default_attempts.copy() # 使用 copy 避免意外修改配置
            logger.info(f"副本次数已重置为: {self.game_manager.dungeon_attempts}")
        else:
            logger.warning("GameManager 对象没有 dungeon_attempts 属性，无法重置")

        # 可能需要更新副本界面的显示（如果它已打开）
        # 这需要更复杂的跨屏幕通信，暂时不做处理

    def _update_signin_display(self):
        """更新签到显示"""
        # 更新签到状态
        if self._is_already_signed_today():
            self.status_text.set_text("今日签到状态: 已签到")
            self.status_text.color = (50, 200, 50)  # 绿色
            self.signin_button.set_active(False)
            self.signin_button.set_text("今日已签到")
            self.signin_button.colors["normal"] = (80, 80, 100)  # 灰色
        else:
            self.status_text.set_text("今日签到状态: 未签到")
            self.status_text.color = (255, 215, 0)  # 金色
            self.signin_button.set_active(True)
            self.signin_button.set_text("签到")
            self.signin_button.colors["normal"] = (60, 100, 60)  # 绿色

        # 更新累计签到天数
        days = self.signin_records["signin_days"]
        total_days = self.signin_records["total_signin_days"]
        self.days_text.set_text(f"当前连续签到: {days} 天 | 累计签到: {total_days} 天")

        # 更新奖励显示
        if self._is_already_signed_today():
            self.daily_text.set_text("日常奖励: 1元宝 + 50000金币 [已领取]")
            self.daily_text.color = (50, 200, 50)  # 绿色
        else:
            self.daily_text.set_text("日常奖励: 1元宝 + 50000金币 [未领取]")
            self.daily_text.color = (200, 200, 200)  # 灰色

        # 更新周奖励进度
        days_to_weekly = 7 - (days % 7)
        if days_to_weekly == 7:
            days_to_weekly = 0

        if days_to_weekly == 0 and self._is_already_signed_today():
            self.weekly_text.set_text("7天奖励: 1祝福油 [已领取]")
            self.weekly_text.color = (50, 200, 50)  # 绿色
        else:
            self.weekly_text.set_text(f"7天奖励: 1祝福油 ({days % 7}/7天)")
            self.weekly_text.color = (200, 200, 200)  # 灰色

        # 更新双周奖励进度
        days_to_biweekly = 15 - (days % 15)
        if days_to_biweekly == 15:
            days_to_biweekly = 0

        if days_to_biweekly == 0 and self._is_already_signed_today():
            self.biweekly_text.set_text("15天奖励: 1宝箱 [已领取]")
            self.biweekly_text.color = (50, 200, 50)  # 绿色
        else:
            self.biweekly_text.set_text(f"15天奖励: 1宝箱 ({days % 15}/15天)")
            self.biweekly_text.color = (200, 200, 200)  # 灰色

    def show(self):
        """显示签到界面"""
        logger.info("显示签到界面")
        super().show()

        # 设置当前角色的签到数据（如果还没有设置）
        if not self.signin_data_path:
            self._setup_character_signin_data()

        # 更新签到显示
        self._update_signin_display()

        logger.info("签到界面显示完成")

    def update(self, dt):
        """更新签到界面"""
        super().update(dt)

    def draw(self, surface):
        """绘制签到界面"""
        super().draw(surface)