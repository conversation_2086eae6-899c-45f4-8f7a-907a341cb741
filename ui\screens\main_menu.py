import pygame
from typing import Dict, List, Callable, Any, Optional, Tuple

from ui.ui_manager import Screen
from ui.components import Button, Panel, Text, ImageButton
from utils.logger import logger
from utils.resource_manager import resources

class ImagePanel(Panel):
    """带有背景图片的面板"""

    def __init__(self, rect: pygame.Rect, image: pygame.Surface):
        """
        初始化图片面板

        参数:
            rect: 面板矩形区域
            image: 背景图片
        """
        super().__init__(rect, (0, 0, 0, 0), (0, 0, 0, 0), 0)  # 透明背景和边框
        self.image = image

    def draw(self, surface: pygame.Surface):
        """绘制图片面板"""
        if not self.visible:
            return

        # 绘制背景图片
        surface.blit(self.image, self.rect.topleft)

        # 绘制子组件
        for component in self.components:
            component.draw(surface)

class MainMenu(Screen):
    """游戏主菜单界面"""

    def __init__(self, ui_manager, game_manager):
        """
        初始化主菜单

        参数:
            ui_manager: UI管理器实例
            game_manager: 游戏管理器实例
        """
        super().__init__("main_menu")
        self.ui_manager = ui_manager
        self.game_manager = game_manager

        self._create_components()

    def _create_components(self):
        """创建主菜单组件"""
        # 获取屏幕尺寸
        screen_size = pygame.display.get_surface().get_size()

        # 创建背景
        try:
            try:
                # --- 修改：使用正确的背景图片路径 ---
                background_image = resources.load_image("assets/images/background.png")
                # --- 结束修改 ---
                if background_image:
                    # 缩放图片以适应屏幕
                    self.background_image = pygame.transform.scale(background_image, screen_size)
                else:
                    logger.warning("无法加载背景图片，将使用纯色背景")
                    self.background_image = None
            except Exception as e:
                logger.error(f"加载或缩放背景图片时出错: {e}", exc_info=True)
                self.background_image = None
        except Exception as e:
            logger.warning(f"无法加载主菜单背景图片: {e}")
            # 创建一个纯色背景
            self.background = Panel(
                pygame.Rect(0, 0, screen_size[0], screen_size[1]),
                color=(50, 50, 80),
                border_width=0
            )
            self.add_component(self.background)

        # 创建标题
        title_rect = pygame.Rect(
            (screen_size[0] - 400) // 2,
            80,
            400,
            60
        )
        title = self.ui_manager.create_text(
            title_rect,
            "萝卜放置传奇",
            "chinese_title",
            (255, 255, 220),
            "center"
        )
        self.add_component(title)

        # 创建菜单面板
        menu_width = 300
        menu_height = 380  # 增加面板高度，容纳更多按钮
        menu_rect = pygame.Rect(
            (screen_size[0] - menu_width) // 2,
            (screen_size[1] - menu_height) // 2,
            menu_width,
            menu_height
        )

        # 使用半透明背景色创建面板
        menu_panel = self.ui_manager.create_panel(
            menu_rect,
            color=(30, 30, 50),  # 深色背景
            border_color=(100, 100, 150),  # 高亮边框
            border_width=2,
            alpha=180  # 设置透明度为180（半透明）
        )
        self.add_component(menu_panel)

        # 创建菜单按钮
        button_width = 220
        button_height = 50
        button_spacing = 20
        start_y = menu_rect.y + 60

        # 开始新游戏按钮
        new_game_rect = pygame.Rect(
            (screen_size[0] - button_width) // 2,
            start_y,
            button_width,
            button_height
        )
        new_game_btn = self.ui_manager.create_button(
            new_game_rect,
            "开始新游戏",
            self._on_new_game_click,
            "chinese_large"
        )
        self.add_component(new_game_btn)

        # 加载游戏按钮
        load_game_rect = pygame.Rect(
            (screen_size[0] - button_width) // 2,
            start_y + button_height + button_spacing,
            button_width,
            button_height
        )
        load_game_btn = self.ui_manager.create_button(
            load_game_rect,
            "加载游戏",
            self._on_load_game_click,
            "chinese_large"
        )
        self.add_component(load_game_btn)

        # 在线登录按钮
        login_rect = pygame.Rect(
            (screen_size[0] - button_width) // 2,
            start_y + (button_height + button_spacing) * 2,
            button_width,
            button_height
        )
        login_btn = self.ui_manager.create_button(
            login_rect,
            "在线登录",
            self._on_login_click,
            "chinese_large"
        )
        self.add_component(login_btn)

        # 退出游戏按钮
        exit_rect = pygame.Rect(
            (screen_size[0] - button_width) // 2,
            start_y + (button_height + button_spacing) * 3,
            button_width,
            button_height
        )
        exit_btn = self.ui_manager.create_button(
            exit_rect,
            "退出游戏",
            self._on_exit_click,
            "chinese_large"
        )
        self.add_component(exit_btn)

    def _on_new_game_click(self):
        """处理开始新游戏按钮点击"""
        logger.info("点击了开始新游戏按钮")
        self.ui_manager.show_screen("character_creation")

    def _on_load_game_click(self):
        """处理加载游戏按钮点击"""
        logger.info("点击了加载游戏按钮")
        self.ui_manager.show_screen("load_game")

    def _on_login_click(self):
        """处理在线登录按钮点击"""
        logger.info("点击了在线登录按钮")
        self.ui_manager.show_screen("login")

    def _on_exit_click(self):
        """处理退出游戏按钮点击"""
        logger.info("点击了退出游戏按钮")
        self.ui_manager.show_confirmation(
            "确认退出",
            "确定要退出游戏吗？",
            self.game_manager.exit_game
        )

    def draw(self, surface: pygame.Surface):
        """绘制主菜单界面"""
        # 先绘制背景图片 (如果存在)
        if hasattr(self, 'background_image') and self.background_image:
            surface.blit(self.background_image, (0, 0))
        elif hasattr(self, 'background') and self.background: # 如果没有图片，绘制纯色背景
             self.background.draw(surface)

        # 然后调用父类的 draw 方法绘制所有组件
        super().draw(surface)