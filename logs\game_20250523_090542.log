[2025-05-23 09:05:42] [WARNING] [main.py:106] 游戏启动
[2025-05-23 09:05:43] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-23 09:05:43] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-23 09:06:11] [ERROR] [main.py:601] 游戏发生未处理的异常: 'Game' object has no attribute 'use_skill'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 597, in main
    game_instance.run() # run() 现在包含清理逻辑
    ^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 348, in run
    self._update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 418, in _update
    self.ui_manager.update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\ui_manager.py", line 385, in update
    self.screens[self.active_screen].update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 2199, in update
    self._auto_cast_skills(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3480, in _auto_cast_skills
    self.skill_manager.auto_cast_skills(self.game_manager)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\skill_manager.py", line 548, in auto_cast_skills
    success = game_manager.use_skill(slot.skill_id)
              ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Game' object has no attribute 'use_skill'
