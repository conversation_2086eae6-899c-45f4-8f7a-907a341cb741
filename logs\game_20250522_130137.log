[2025-05-22 13:01:37] [WARNING] [main.py:105] 游戏启动
[2025-05-22 13:01:37] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 13:01:37] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 13:02:15] [WARNING] [battle.py:856] 战斗日志: 获得了 9 经验值
[2025-05-22 13:02:15] [WARNING] [battle.py:856] 战斗日志: 获得了 123 金币
[2025-05-22 13:02:23] [WARNING] [map_screen.py:404] 无法进入地图: 骷髅洞
[2025-05-22 13:02:40] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 13:02:40] [WARNING] [battle.py:856] 战斗日志: 获得了 68 金币
[2025-05-22 13:02:50] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 13:02:50] [WARNING] [battle.py:856] 战斗日志: 获得了 51 金币
[2025-05-22 13:03:04] [WARNING] [battle.py:856] 战斗日志: 获得了 3 经验值
[2025-05-22 13:03:04] [WARNING] [battle.py:856] 战斗日志: 获得了 34 金币
[2025-05-22 13:03:16] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 13:03:16] [WARNING] [battle.py:856] 战斗日志: 获得了 85 金币
[2025-05-22 13:03:23] [WARNING] [battle.py:856] 战斗日志: 获得了 3 经验值
[2025-05-22 13:03:23] [WARNING] [battle.py:856] 战斗日志: 获得了 32 金币
[2025-05-22 13:03:31] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 13:03:31] [WARNING] [battle.py:856] 战斗日志: 获得了 67 金币
[2025-05-22 13:03:43] [ERROR] [battle.py:841] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:03:43] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:03:43] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 848, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:03:43] [ERROR] [battle.py:390] AttributeError in execute_battle_round: 'NoneType' object has no attribute 'hp'
[2025-05-22 13:03:43] [ERROR] [battle.py:391] Player: None, Monster: None
[2025-05-22 13:03:43] [ERROR] [battle.py:392] Traceback:\nTraceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 371, in execute_battle_round
    if self.player.hp <= 0:
       ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'hp'

[2025-05-22 13:04:09] [ERROR] [battle.py:841] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:04:09] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:04:09] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 848, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:04:09] [ERROR] [battle.py:390] AttributeError in execute_battle_round: 'NoneType' object has no attribute 'hp'
[2025-05-22 13:04:09] [ERROR] [battle.py:391] Player: None, Monster: None
[2025-05-22 13:04:09] [ERROR] [battle.py:392] Traceback:\nTraceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 371, in execute_battle_round
    if self.player.hp <= 0:
       ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'hp'

[2025-05-22 13:04:34] [ERROR] [battle.py:841] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:04:34] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:04:34] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 848, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 13:04:34] [ERROR] [battle.py:390] AttributeError in execute_battle_round: 'NoneType' object has no attribute 'hp'
[2025-05-22 13:04:34] [ERROR] [battle.py:391] Player: None, Monster: None
[2025-05-22 13:04:34] [ERROR] [battle.py:392] Traceback:\nTraceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 371, in execute_battle_round
    if self.player is None or self.player.hp <= 0:
       ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'hp'

