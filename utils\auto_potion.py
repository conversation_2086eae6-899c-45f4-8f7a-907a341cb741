import time
from typing import Dict, List, Any, Optional
from enum import Enum, auto
from utils.logger import logger

class PotionEffectType(Enum):
    """药水效果类型"""
    HEAL = auto()  # 恢复生命
    MANA = auto()  # 恢复魔法
    DUAL = auto()  # 双重恢复
    UNKNOWN = auto() # 未知或非药水效果

class AutoPotionSystem:
    """自动吃药系统"""

    def __init__(self, game):
        """初始化自动吃药系统

        Args:
            game: 游戏实例
        """
        self.game = game

        # 默认设置
        self.enabled = False  # 自动吃药系统是否启用
        self.hp_threshold = 50  # 当生命值低于最大生命值的50%时使用药品
        self.mp_threshold = 30  # 当魔法值低于最大魔法值的30%时使用药品

        # 药品优先级配置
        self.hp_potion_priority = [
            "强效金创药",
            "金创药(中量)",
            "金创药(小量)"
        ]

        self.mp_potion_priority = [
            "强效魔法药",
            "魔法药(中量)",
            "魔法药(小量)"
        ]

        # 双重恢复药品（同时恢复HP和MP）
        self.dual_potion_priority = [
            "万年雪霜",
            "强效太阳水",
            "太阳水",
            "疗伤药",
            "鹿茸",
            "鹿血"
        ]

        # 药品使用冷却时间（秒）
        self.potion_cooldown = 3.0
        self.last_potion_time = 0

        # 从玩家对象获取设置（如果存在）
        if self.game and self.game.player:
            player = self.game.player

            # 获取启用状态
            if hasattr(player, 'is_auto_potion_enabled'):
                self.enabled = player.is_auto_potion_enabled()
                logger.info(f"从玩家对象获取自动吃药启用状态: {self.enabled}")

            # 获取阈值设置
            if hasattr(player, 'get_hp_potion_threshold'):
                hp_threshold = player.get_hp_potion_threshold()
                if hp_threshold is not None:
                    self.hp_threshold = hp_threshold
                    logger.info(f"从玩家对象获取HP阈值设置: {self.hp_threshold}%")

            if hasattr(player, 'get_mp_potion_threshold'):
                mp_threshold = player.get_mp_potion_threshold()
                if mp_threshold is not None:
                    self.mp_threshold = mp_threshold
                    logger.info(f"从玩家对象获取MP阈值设置: {self.mp_threshold}%")

            # 获取药水优先级设置
            if hasattr(player, 'get_selected_hp_potions') and player.get_selected_hp_potions():
                self.hp_potion_priority = player.get_selected_hp_potions()
                logger.info(f"从玩家对象获取HP药水优先级: {', '.join(self.hp_potion_priority)}")

            if hasattr(player, 'get_selected_mp_potions') and player.get_selected_mp_potions():
                self.mp_potion_priority = player.get_selected_mp_potions()
                logger.info(f"从玩家对象获取MP药水优先级: {', '.join(self.mp_potion_priority)}")

            if hasattr(player, 'get_selected_dual_potions') and player.get_selected_dual_potions():
                self.dual_potion_priority = player.get_selected_dual_potions()
                logger.info(f"从玩家对象获取双重药水优先级: {', '.join(self.dual_potion_priority)}")

        logger.info("自动吃药系统已初始化")

    def toggle(self):
        """切换自动吃药系统的启用状态"""
        # 切换内部状态
        self.enabled = not self.enabled
        status = "启用" if self.enabled else "禁用"
        logger.info(f"自动吃药系统已{status}")

        # 同步到玩家对象
        if self.game and self.game.player:
            self.game.player.auto_potion_enabled = self.enabled
            logger.info(f"已将自动吃药状态同步到玩家对象: {self.enabled}")

        return self.enabled

    def set_hp_threshold(self, threshold: int):
        """设置生命值阈值

        Args:
            threshold: 生命值阈值百分比（0-100）
        """
        if 1 <= threshold <= 99:
            self.hp_threshold = threshold
            logger.info(f"生命值阈值已设置为 {threshold}%")

            # 同步到玩家对象
            if self.game and self.game.player:
                self.game.player.hp_potion_threshold = threshold
                logger.info(f"已将HP阈值同步到玩家对象: {threshold}%")
        else:
            logger.warning(f"无效的生命值阈值: {threshold}，应为1-99之间的整数")

    def set_mp_threshold(self, threshold: int):
        """设置魔法值阈值

        Args:
            threshold: 魔法值阈值百分比（0-100）
        """
        if 1 <= threshold <= 99:
            self.mp_threshold = threshold
            logger.info(f"魔法值阈值已设置为 {threshold}%")

            # 同步到玩家对象
            if self.game and self.game.player:
                self.game.player.mp_potion_threshold = threshold
                logger.info(f"已将MP阈值同步到玩家对象: {threshold}%")
        else:
            logger.warning(f"无效的魔法值阈值: {threshold}，应为1-99之间的整数")

    def set_hp_potion_priority(self, potion_list: List[str]):
        """设置生命药水优先级列表

        Args:
            potion_list: 生命药水名称列表，按优先级排序
        """
        if isinstance(potion_list, list) and all(isinstance(item, str) for item in potion_list):
            self.hp_potion_priority = potion_list
            logger.info(f"生命药水优先级已更新: {', '.join(potion_list)}")

            # 同步到玩家对象
            if self.game and self.game.player:
                self.game.player.selected_hp_potions = potion_list
                logger.info(f"已将HP药水优先级同步到玩家对象")
        else:
            logger.warning(f"无效的生命药水优先级列表")

    def set_mp_potion_priority(self, potion_list: List[str]):
        """设置魔法药水优先级列表

        Args:
            potion_list: 魔法药水名称列表，按优先级排序
        """
        if isinstance(potion_list, list) and all(isinstance(item, str) for item in potion_list):
            self.mp_potion_priority = potion_list
            logger.info(f"魔法药水优先级已更新: {', '.join(potion_list)}")

            # 同步到玩家对象
            if self.game and self.game.player:
                self.game.player.selected_mp_potions = potion_list
                logger.info(f"已将MP药水优先级同步到玩家对象")
        else:
            logger.warning(f"无效的魔法药水优先级列表")

    def set_dual_potion_priority(self, potion_list: List[str]):
        """设置双重恢复药水优先级列表

        Args:
            potion_list: 双重恢复药水名称列表，按优先级排序
        """
        if isinstance(potion_list, list) and all(isinstance(item, str) for item in potion_list):
            self.dual_potion_priority = potion_list
            logger.info(f"双重恢复药水优先级已更新: {', '.join(potion_list)}")

            # 同步到玩家对象
            if self.game and self.game.player and hasattr(self.game.player, 'selected_dual_potions'):
                self.game.player.selected_dual_potions = potion_list
                logger.info(f"已将双重药水优先级同步到玩家对象")
        else:
            logger.warning(f"无效的双重恢复药水优先级列表")

    def get_available_potions(self):
        """获取玩家背包中可用的各类药水

        Returns:
            Dict[str, List[str]]: 各类药水列表，包括hp_potions, mp_potions, dual_potions
        """
        result = {
            "hp_potions": [],
            "mp_potions": [],
            "dual_potions": []
        }

        if not self.game.player or not hasattr(self.game.player, 'inventory'):
            return result

        inventory = self.game.player.inventory

        for item in inventory:
            if not isinstance(item, dict):
                continue

            name = item.get("name", "")
            effect_type, _ = self._parse_item_effect(item)

            if effect_type == PotionEffectType.HEAL:
                if name not in result["hp_potions"]:
                    result["hp_potions"].append(name)
            elif effect_type == PotionEffectType.MANA:
                if name not in result["mp_potions"]:
                    result["mp_potions"].append(name)
            elif effect_type == PotionEffectType.DUAL:
                if name not in result["dual_potions"]:
                    result["dual_potions"].append(name)

        return result

    def find_potion_in_inventory(self, potion_names: List[str]) -> Optional[Dict]:
        """在背包中查找指定药品

        Args:
            potion_names: 药品名称列表，按优先级排序

        Returns:
            找到的药品对象，如果没有找到返回None
        """
        if not self.game.player or not hasattr(self.game.player, 'inventory'):
            return None

        inventory = self.game.player.inventory

        # 按优先级搜索药品
        for potion_name in potion_names:
            for item in inventory:
                if isinstance(item, dict) and item.get("name") == potion_name:
                    return item

        return None

    def use_potion(self, potion: Dict) -> bool:
        """使用药品

        Args:
            potion: 要使用的药品对象

        Returns:
            是否成功使用药品
        """
        if not potion or not isinstance(potion, dict):
            return False

        player = self.game.player
        potion_name = potion.get("name", "未知药品")

        # 使用辅助方法解析效果
        effect_type, effect_details = self._parse_item_effect(potion)

        consumed = False

        if effect_type == PotionEffectType.HEAL:
            effect_value = effect_details.get("value", 0)
            if player.hp < player.max_hp:
                old_hp = player.hp
                player.hp = min(player.max_hp, player.hp + effect_value)
                actual_heal = player.hp - old_hp
                logger.info(f"自动使用 {potion_name} 恢复生命值 {actual_heal} (当前: {player.hp}/{player.max_hp})")
                consumed = True

        elif effect_type == PotionEffectType.MANA:
            effect_value = effect_details.get("value", 0)
            if player.mp < player.max_mp:
                old_mp = player.mp
                player.mp = min(player.max_mp, player.mp + effect_value)
                actual_restore = player.mp - old_mp
                logger.info(f"自动使用 {potion_name} 恢复魔法值 {actual_restore} (当前: {player.mp}/{player.max_mp})")
                consumed = True

        elif effect_type == PotionEffectType.DUAL:
            hp_restore = effect_details.get("hp_value", 0)
            mp_restore = effect_details.get("mp_value", 0)
            if player.hp < player.max_hp or player.mp < player.max_mp:
                old_hp = player.hp
                old_mp = player.mp
                player.hp = min(player.max_hp, player.hp + hp_restore)
                player.mp = min(player.max_mp, player.mp + mp_restore)
                actual_hp_heal = player.hp - old_hp
                actual_mp_restore = player.mp - old_mp
                logger.info(f"自动使用 {potion_name} 恢复生命值 {actual_hp_heal} 和魔法值 {actual_mp_restore}")
                consumed = True

        if consumed:
            try:
                if "amount" in potion:
                    if potion["amount"] > 1:
                        potion["amount"] -= 1
                        logger.info(f"消耗1个{potion_name}，剩余: {potion['amount']}个")
                    else:
                        player.inventory.remove(potion)
                        logger.info(f"使用最后1个{potion_name}，已从背包移除")
                else:
                    player.inventory.remove(potion)
                    logger.info(f"使用{potion_name}，已从背包移除")

                self.last_potion_time = time.time()
                return True
            except ValueError:
                logger.error(f"移除药品失败: 背包中未找到药品 {potion_name}")
            except Exception as e:
                logger.error(f"处理药品消耗时发生错误: {e}")

        return False

    def update(self, current_time: float):
        """更新自动吃药系统的逻辑

        Args:
            current_time: 当前时间戳
        """
        player = self.game.player

        # 如果玩家不存在，或玩家已死亡，则直接返回
        if not player or player.is_dead:
            return

        # 检查玩家的自动吃药设置是否启用
        # 优先使用玩家对象中的设置，如果不存在则使用系统内部设置
        player_auto_potion_enabled = player.is_auto_potion_enabled() if hasattr(player, 'is_auto_potion_enabled') else self.enabled

        # 如果自动吃药未启用，直接返回
        if not player_auto_potion_enabled and not self.enabled:
            return

        # 检查冷却时间
        if current_time - self.last_potion_time < self.potion_cooldown:
            return

        # 获取玩家设置的阈值，如果没有设置则使用系统默认值
        hp_threshold = player.get_hp_potion_threshold() if hasattr(player, 'get_hp_potion_threshold') else self.hp_threshold
        if hp_threshold is None:  # 如果玩家未设置，使用默认值
            hp_threshold = self.hp_threshold

        mp_threshold = player.get_mp_potion_threshold() if hasattr(player, 'get_mp_potion_threshold') else self.mp_threshold
        if mp_threshold is None:  # 如果玩家未设置，使用默认值
            mp_threshold = self.mp_threshold

        # 获取玩家设置的药水优先级列表
        hp_potion_priority = player.get_selected_hp_potions() if hasattr(player, 'get_selected_hp_potions') and player.get_selected_hp_potions() else self.hp_potion_priority
        mp_potion_priority = player.get_selected_mp_potions() if hasattr(player, 'get_selected_mp_potions') and player.get_selected_mp_potions() else self.mp_potion_priority
        dual_potion_priority = player.get_selected_dual_potions() if hasattr(player, 'get_selected_dual_potions') and player.get_selected_dual_potions() else self.dual_potion_priority

        # 检查是否需要使用HP药水
        hp_percent = (player.hp / player.max_hp) * 100
        if hp_percent <= hp_threshold:
            logger.debug(f"生命值低于阈值 ({hp_percent:.1f}% <= {hp_threshold}%)，尝试使用HP药水")
            # 优先使用双重恢复药水
            dual_potion = self.find_potion_in_inventory(dual_potion_priority)
            if dual_potion:
                if self.use_potion(dual_potion):
                    return # 使用成功，结束本轮更新

            # 然后尝试使用纯HP药水
            hp_potion = self.find_potion_in_inventory(hp_potion_priority)
            if hp_potion:
                if self.use_potion(hp_potion):
                    return # 使用成功，结束本轮更新

            logger.info("需要恢复生命值，但找不到合适的药水")

        # 检查是否需要使用MP药水 (与HP检查独立，避免HP满了但MP低的情况)
        mp_percent = (player.mp / player.max_mp) * 100
        if mp_percent <= mp_threshold:
            logger.debug(f"魔法值低于阈值 ({mp_percent:.1f}% <= {mp_threshold}%)，尝试使用MP药水")
            # 优先使用双重恢复药水（如果上面没用掉）
            if current_time - self.last_potion_time >= self.potion_cooldown: # 再次检查冷却，防止上面用了药
                dual_potion = self.find_potion_in_inventory(dual_potion_priority)
                if dual_potion:
                    if self.use_potion(dual_potion):
                        return # 使用成功，结束本轮更新

            # 然后尝试使用纯MP药水
            if current_time - self.last_potion_time >= self.potion_cooldown: # 再次检查冷却
                mp_potion = self.find_potion_in_inventory(mp_potion_priority)
                if mp_potion:
                    if self.use_potion(mp_potion):
                        return # 使用成功，结束本轮更新

                logger.info("需要恢复魔法值，但找不到合适的药水")

    def _parse_item_effect(self, item: Dict) -> tuple[Optional[PotionEffectType], Dict[str, Any]]:
        """解析物品字典，返回效果类型Enum和效果数值字典"""
        if not isinstance(item, dict):
            return PotionEffectType.UNKNOWN, {}

        effect_data = item.get("effect", {})
        effect_details = {}
        effect_type = PotionEffectType.UNKNOWN

        if isinstance(effect_data, dict):
            type_str = effect_data.get("type", "").lower()
            if type_str == "heal":
                effect_type = PotionEffectType.HEAL
                effect_details["value"] = effect_data.get("value", 0)
            elif type_str == "mana":
                effect_type = PotionEffectType.MANA
                effect_details["value"] = effect_data.get("value", 0)
            elif type_str == "dual":
                effect_type = PotionEffectType.DUAL
                effect_details["hp_value"] = effect_data.get("hp_value", 0)
                effect_details["mp_value"] = effect_data.get("mp_value", 0)
        elif isinstance(effect_data, str):
            # 兼容旧的字符串格式
            effect_str = effect_data.lower()
            if effect_str == "恢复生命":
                effect_type = PotionEffectType.HEAL
                effect_details["value"] = item.get("effect_value", 0) # 尝试获取外部的 effect_value
            elif effect_str == "恢复魔法":
                effect_type = PotionEffectType.MANA
                effect_details["value"] = item.get("effect_value", 0)
            elif effect_str == "双重恢复":
                effect_type = PotionEffectType.DUAL
                effect_details["hp_value"] = item.get("hp_restore", 0) # 兼容旧的 hp_restore
                effect_details["mp_value"] = item.get("mp_restore", 0) # 兼容旧的 mp_restore

        return effect_type, effect_details
