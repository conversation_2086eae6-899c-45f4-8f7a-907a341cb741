import pygame
import os
from typing import Dict, List, Optional, Any, Tuple
import sys

from ui.ui_manager import Screen
from utils.logger import logger
from core.game import Game
from utils.resource_manager import resources
from core.config import GameConfig

class MapScreen(Screen):
    """地图选择界面"""

    def __init__(self, ui_manager, game_manager: Game):
        """
        初始化地图选择界面

        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
        """
        super().__init__("map")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.components_map = {}  # 组件映射
        self.selected_map = None  # 当前选中的地图
        self.map_background_image = None  # 当前选中的地图背景图片

        # 创建界面组件
        self._create_components()
        logger.info("地图选择界面初始化完成")

    def _create_components(self):
        """创建界面组件"""
        try:
            logger.info("开始创建地图界面组件")
            screen_size = pygame.display.get_surface().get_size()

            # 添加一个背景面板覆盖整个屏幕 - 使用深蓝色背景
            background = self.ui_manager.create_panel(
                pygame.Rect(0, 0, screen_size[0], screen_size[1]),
                color=(20, 30, 50, 255),  # 深蓝色背景
                border_width=0
            )
            background.visible = True
            self.add_component(background)
            logger.info("添加地图界面背景")

            # 创建标题 - 使用亮色文本
            title_text = self.ui_manager.create_text(
                pygame.Rect(0, 20, screen_size[0], 40),
                "地图选择",
                "chinese_title",
                color=(220, 230, 255),  # 亮色文本
                align="center"
            )
            title_text.visible = True
            self.add_component(title_text)
            logger.info("地图界面标题已创建")

            # 创建返回按钮 - 使用蓝色调按钮
            back_button = self.ui_manager.create_button(
                pygame.Rect(20, screen_size[1] - 60, 120, 40),
                "返回",
                self._on_back_click,
                "chinese_normal"
            )
            # 设置按钮颜色
            if hasattr(back_button, 'colors'):
                back_button.colors.update({
                    "normal": (50, 70, 120),      # 中等蓝色
                    "hover": (70, 90, 150),       # 亮蓝色
                    "pressed": (40, 60, 100),     # 深蓝色
                    "text": (220, 230, 255),      # 亮色文本
                    "border": (60, 80, 140)       # 亮蓝色边框
                })
            back_button.visible = True
            self.add_component(back_button)
            logger.info("地图界面返回按钮已创建")

            # 副本系统已移除

            # 创建地图区域背景面板 - 左侧
            maps_panel = self.ui_manager.create_panel(
                pygame.Rect(50, 80, 350, screen_size[1] - 160),
                color=(30, 40, 65, 255),  # 稍浅的蓝色
                border_color=(60, 80, 140, 255),  # 亮蓝色边框
                border_width=2
            )
            maps_panel.visible = True
            self.add_component(maps_panel)
            self.components_map["maps_panel"] = maps_panel
            logger.info("地图列表面板已创建")

            # 创建地图列表标题
            maps_title = self.ui_manager.create_text(
                pygame.Rect(50, 80, 350, 30),
                "可用地图",
                "chinese_large",
                color=(255, 215, 0),  # 金色标题
                align="center"
            )
            maps_title.visible = True
            self.add_component(maps_title)

            # 创建地图列表 (只创建一次)
            self.map_buttons = []
            try:
                maps_data = GameConfig.MAPS_DATA
                logger.info(f"初始化地图列表按钮: {len(maps_data)} 个地图配置")

                y_offset = 120  # 起始位置
                button_height = 40
                button_gap = 10
                button_width = 300
                left_margin = 70

                # 过滤出普通地图
                normal_maps = {name: info for name, info in maps_data.items() if not info.get("is_dungeon", False)}
                logger.info(f"找到 {len(normal_maps)} 个普通地图")

                for map_name in normal_maps.keys():
                    # 修复闭包问题，为每个地图名称创建独立的回调函数
                    def create_on_click_callback(name):
                         def on_click():
                             logger.info(f"点击了地图按钮: {name}")
                             self._on_map_click(name)
                         return on_click

                    map_button = self.ui_manager.create_button(
                        pygame.Rect(left_margin, y_offset, button_width, button_height),
                        map_name,
                        create_on_click_callback(map_name),
                        "chinese_normal"
                    )
                    # 设置按钮颜色
                    if hasattr(map_button, 'colors'):
                        map_button.colors.update({
                            "normal": (50, 70, 120),      # 中等蓝色
                            "hover": (70, 90, 150),       # 亮蓝色
                            "pressed": (40, 60, 100),     # 深蓝色
                            "disabled": (40, 50, 80),     # 灰蓝色（禁用状态）
                            "text": (220, 230, 255),      # 亮色文本
                            "border": (60, 80, 140)       # 亮蓝色边框
                        })
                    map_button.visible = True
                    map_button.map_name = map_name # 关联地图名称
                    self.add_component(map_button)
                    self.map_buttons.append(map_button)
                    logger.info(f"添加地图按钮: {map_name}, 位置: x={left_margin}, y={y_offset}")
                    y_offset += button_height + button_gap

                # 如果没有普通地图，显示提示信息
                if len(normal_maps) == 0:
                    logger.warning("没有找到普通地图数据，在列表中显示提示")
                    no_maps_text = self.ui_manager.create_text(
                        pygame.Rect(left_margin, y_offset, button_width, 40),
                        "没有可用地图",
                        "chinese_large",
                        align="center"
                    )
                    no_maps_text.visible = True
                    self.add_component(no_maps_text)
                    # 注意：这个提示文本不会被添加到 self.map_buttons, 因为它不是一个可选地图

            except Exception as e:
                logger.error(f"创建地图按钮时发生错误: {e}", exc_info=True)

            # 创建地图信息面板 - 右侧
            info_panel = self.ui_manager.create_panel(
                pygame.Rect(screen_size[0] - 350, 100, 300, screen_size[1] - 200),
                color=(30, 40, 65, 255),  # 稍浅的蓝色
                border_color=(60, 80, 140, 255),  # 亮蓝色边框
                border_width=2
            )
            info_panel.visible = True
            self.add_component(info_panel)
            self.components_map["info_panel"] = info_panel
            logger.info("地图信息面板已创建")

            # 创建地图信息文本
            map_info_title = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 110, 280, 30),
                "地图信息",
                "chinese_large",
                color=(255, 215, 0),  # 金色标题
                align="center"
            )
            map_info_title.visible = True
            self.add_component(map_info_title)

            # 地图名称
            map_name_text = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 150, 280, 30),
                "",
                "chinese_normal",
                color=(200, 210, 230)  # 浅色文本
            )
            map_name_text.visible = True
            self.add_component(map_name_text)
            self.components_map["map_name"] = map_name_text

            # 地图难度
            map_difficulty_text = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 180, 280, 30),
                "",
                "chinese_normal",
                color=(200, 210, 230)  # 浅色文本
            )
            map_difficulty_text.visible = True
            self.add_component(map_difficulty_text)
            self.components_map["map_difficulty"] = map_difficulty_text

            # 地图等级要求
            map_level_text = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 210, 280, 30),
                "",
                "chinese_normal",
                color=(200, 210, 230)  # 浅色文本
            )
            map_level_text.visible = True
            self.add_component(map_level_text)
            self.components_map["map_level"] = map_level_text

            # 地图描述
            map_desc_text = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 240, 280, 60),
                "",
                "chinese_normal",
                color=(200, 210, 230)  # 浅色文本
            )
            map_desc_text.visible = True
            self.add_component(map_desc_text)
            self.components_map["map_desc"] = map_desc_text

            # 地图怪物标题
            map_monsters_title = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 310, 280, 30),
                "怪物列表:",
                "chinese_normal",
                color=(255, 215, 0)  # 金色标题
            )
            map_monsters_title.visible = True
            self.add_component(map_monsters_title)

            # 地图怪物列表
            map_monsters_text = self.ui_manager.create_text(
                pygame.Rect(screen_size[0] - 340, 340, 280, 200),
                "",
                "chinese_normal",
                color=(200, 210, 230)  # 浅色文本
            )
            map_monsters_text.visible = True
            self.add_component(map_monsters_text)
            self.components_map["map_monsters"] = map_monsters_text

            # 进入地图按钮 - 使用突出的绿色调
            enter_button = self.ui_manager.create_button(
                pygame.Rect(screen_size[0] - 250, screen_size[1] - 100, 150, 40),
                "进入地图",
                self._on_enter_click,
                "chinese_normal"
            )
            # 设置按钮颜色 - 使用绿色调突出显示
            if hasattr(enter_button, 'colors'):
                enter_button.colors.update({
                    "normal": (40, 120, 60),      # 绿色
                    "hover": (50, 150, 70),       # 亮绿色
                    "pressed": (30, 100, 50),     # 深绿色
                    "disabled": (40, 60, 50),     # 灰绿色（禁用状态）
                    "text": (220, 255, 220),      # 亮色文本
                    "border": (60, 160, 80)       # 亮绿色边框
                })
            enter_button.visible = True
            self.add_component(enter_button)
            self.components_map["enter_button"] = enter_button

            # 默认禁用进入按钮
            enter_button.enabled = False

            logger.info("地图界面组件创建完成")
        except Exception as e:
            logger.error(f"创建地图界面组件时发生错误: {e}", exc_info=True)

    def _update_map_buttons_state(self):
        """更新地图按钮的可点击状态"""
        logger.info("开始更新地图按钮状态")
        maps_data = GameConfig.MAPS_DATA
        player_level = 1
        if hasattr(self.game_manager, 'player') and self.game_manager.player is not None:
            player_level = getattr(self.game_manager.player, 'level', 1)
            logger.info(f"当前玩家等级: {player_level}")
        else:
            logger.warning("无法获取玩家等级，将使用默认值 1")

        can_enter_map_implemented = hasattr(self.game_manager, 'can_enter_map')
        if not can_enter_map_implemented:
            logger.info("GameManager 未实现 can_enter_map 方法，将仅基于等级检查")

        for button in self.map_buttons:
            # 确保处理的是我们添加了 map_name 属性的按钮
            if not hasattr(button, 'map_name'):
                continue # 跳过非地图按钮（例如可能的"无地图"提示文本）

            map_name = button.map_name
            button.enabled = True # 默认启用

            # 优先使用 can_enter_map 检查
            if can_enter_map_implemented:
                try:
                    if not self.game_manager.can_enter_map(map_name):
                        button.enabled = False
                        logger.info(f"禁用地图按钮 (can_enter_map): {map_name}")
                except Exception as e:
                    logger.error(f"检查地图进入条件 can_enter_map({map_name}) 出错: {e}", exc_info=True)
                    button.enabled = False # 出错时禁用
            else:
                # 回退到等级检查
                map_info = maps_data.get(map_name, {})
                required_level = map_info.get("level_required", 1)
                if player_level < required_level:
                    button.enabled = False
                    logger.info(f"禁用地图按钮 (等级不足): {map_name}, 需要: {required_level}, 当前: {player_level}")

        logger.info("地图按钮状态更新完成")

    def _on_map_click(self, map_name: str):
        """处理地图按钮点击"""
        self.selected_map = map_name
        maps_data = GameConfig.MAPS_DATA
        map_info = maps_data.get(map_name, {})

        # 更新地图信息
        self.components_map["map_name"].set_text(f"地图名称: {map_name}")
        self.components_map["map_difficulty"].set_text(f"难度级别: {map_info.get('difficulty', '未知')}")
        self.components_map["map_level"].set_text(f"等级要求: {map_info.get('level_required', 0)}")
        self.components_map["map_desc"].set_text(f"描述: {map_info.get('description', '无描述')}")

        # 加载并显示地图背景图片
        # 检查是否已经创建了地图预览面板
        if "map_background_panel" not in self.components_map:
            screen_size = pygame.display.get_surface().get_size()
            # 创建用于显示地图图片的Panel
            background_panel = self.ui_manager.create_panel(
                pygame.Rect(440, 120, 300, 180),
                color=(25, 35, 60, 255),  # 深蓝色背景
                border_color=(60, 80, 140, 255),  # 亮蓝色边框
                border_width=2
            )
            self.add_component(background_panel)
            self.components_map["map_background_panel"] = background_panel

            # 创建地图背景图片标题
            bg_title = self.ui_manager.create_text(
                pygame.Rect(440, 90, 300, 30),
                "地图预览",
                "chinese_normal",
                color=(255, 215, 0),  # 金色标题
                align="center"
            )
            self.add_component(bg_title)
            self.components_map["map_bg_title"] = bg_title

        # 尝试使用资源管理器加载地图图片
        try:
            # 首先尝试从资源管理器获取图片
            rm = resources.get_instance()
            map_image_path = rm.get_map_preview_path(map_name)
            logger.info(f"尝试加载地图图片: {map_image_path}")

            # 检查文件是否存在，如果存在则直接加载
            if os.path.exists(map_image_path):
                bg_image = pygame.image.load(map_image_path)
                bg_image = pygame.transform.scale(bg_image, (300, 180))
                self.map_background_image = bg_image
                logger.info(f"成功加载地图图片: {map_name}")
            else:
                # 如果直接路径不存在，尝试使用资源管理器加载
                rm = resources.get_instance()
                # 检查是否在打包环境中
                if rm.is_frozen:
                    # 尝试从打包后的资源路径加载
                    base_dir = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
                    alt_path = os.path.join(base_dir, "resources", "map", f"{map_name}.jpg")
                    logger.info(f"打包环境中尝试加载: {alt_path}")

                    if os.path.exists(alt_path):
                        bg_image = pygame.image.load(alt_path)
                        bg_image = pygame.transform.scale(bg_image, (300, 180))
                        self.map_background_image = bg_image
                        logger.info(f"打包环境中成功加载地图图片: {map_name}")
                    else:
                        self.map_background_image = None
                        logger.warning(f"打包环境中地图图片不存在: {alt_path}")
                else:
                    self.map_background_image = None
                    logger.warning(f"地图图片不存在: {map_image_path}")
        except Exception as e:
            self.map_background_image = None
            logger.error(f"加载地图图片失败: {e}")

        # 更新怪物列表
        monsters_text = ""
        for monster in map_info.get("monsters", []):
            monster_name = monster.get("name", "未知怪物")
            monster_weight = monster.get("weight", 0)
            monsters_text += f"· {monster_name}\n"

        if not monsters_text:
            monsters_text = "无怪物信息"

        self.components_map["map_monsters"].set_text(monsters_text)

        # 启用进入按钮
        self.components_map["enter_button"].enabled = True

    def _on_enter_click(self):
        """处理进入地图按钮点击"""
        if self.selected_map:
            logger.info(f"玩家选择进入地图: {self.selected_map}")

            # 检查玩家是否存在
            if not hasattr(self.game_manager, 'player') or self.game_manager.player is None:
                logger.warning("尝试进入地图，但玩家不存在")
                self.ui_manager.show_message("无法进入地图", "请先创建角色")
                return

            success = False

            # 检查Game类是否实现了change_map方法
            if hasattr(self.game_manager, 'change_map'):
                success = self.game_manager.change_map(self.selected_map)
            elif hasattr(self.game_manager, 'current_map'):
                # 如果没有change_map方法，尝试直接设置current_map
                self.game_manager.current_map = self.selected_map
                success = True
                logger.info(f"直接设置当前地图为: {self.selected_map}")

            if success:
                logger.debug(f"成功进入地图: {self.selected_map}")
                self.ui_manager.show_screen("game")
            else:
                logger.warning(f"无法进入地图: {self.selected_map}")
                self.ui_manager.show_message("无法进入地图", f"你无法进入 {self.selected_map}，请检查等级要求。")

    def _on_back_click(self):
        """返回按钮点击事件"""
        logger.info("返回主游戏界面")
        self.hide()
        self.ui_manager.show_screen("game")

    # 副本系统已移除

    def show(self):
        """显示地图界面"""
        logger.info("正在显示地图选择界面")

        try:
            self._update_map_buttons_state()
            logger.info("地图按钮状态已更新")
        except Exception as e:
            logger.error(f"更新地图按钮状态时出错: {e}", exc_info=True)

        # 启用组件
        for component in self.components:
            if hasattr(component, 'visible'):
                component.visible = True

        # 调用父类show方法
        super().show()
        logger.info("地图选择界面显示完成")

        # 清除选中的地图
        self.selected_map = None
        self.components_map["map_name"].set_text("")
        self.components_map["map_difficulty"].set_text("")
        self.components_map["map_level"].set_text("")
        self.components_map["map_desc"].set_text("")
        self.components_map["map_monsters"].set_text("")
        self.components_map["enter_button"].enabled = False

    def draw(self, surface: pygame.Surface):
        """绘制界面"""
        # 先调用父类的绘制方法
        super().draw(surface)

        # 绘制地图背景图片
        if self.map_background_image and "map_background_panel" in self.components_map:
            panel = self.components_map["map_background_panel"]
            if panel and panel.visible:
                surface.blit(self.map_background_image, panel.rect.topleft)