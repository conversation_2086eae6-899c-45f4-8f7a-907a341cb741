import os
import sys
import logging

logger = logging.getLogger(__name__)

def resource_path(relative_path):
    """获取资源的绝对路径，兼容开发环境和打包环境"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS
        base_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
        
        # 如果文件路径是基于项目根目录的，需要向上一级
        if os.path.basename(os.path.dirname(os.path.abspath(__file__))) == "utils":
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            
        full_path = os.path.join(base_path, relative_path)
        logger.debug(f"资源路径: {full_path}")
        return full_path
    except Exception as e:
        logger.error(f"获取资源路径出错: {e}")
        return relative_path 