[2025-05-22 16:42:31] [WARNING] [main.py:105] 游戏启动
[2025-05-22 16:42:31] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 16:42:31] [ERROR] [main.py:326] 创建游戏界面过程中发生错误: 'GameScreen' object has no attribute 'components_map'
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 250, in _create_screens
    game_screen = GameScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 79, in __init__
    self.components_map["hunting_title"] = hunting_title
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'GameScreen' object has no attribute 'components_map'. Did you mean: 'components'?
