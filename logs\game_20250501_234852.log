[2025-05-01 23:48:52] [WARNING] [main.py:101] 游戏启动
[2025-05-01 23:48:52] [WARNING] [main.py:217] 开始创建游戏界面
[2025-05-01 23:48:52] [WARNING] [main.py:309] 游戏界面创建完成
[2025-05-01 23:48:54] [ERROR] [game_screen.py:3090] 初始化游戏界面时出错: cannot access local variable 'buff_display_text' where it is not associated with a value
[2025-05-01 23:48:54] [ERROR] [game_screen.py:3092] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 3054, in initialize
    self.update_player_stats(self.game_manager.player)
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 1169, in update_player_stats
    self.player_stats_components["buff_text"].set_text(buff_display_text)
                                                       ^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'buff_display_text' where it is not associated with a value

[2025-05-01 23:48:54] [ERROR] [main.py:573] 游戏发生未处理的异常: cannot access local variable 'buff_display_text' where it is not associated with a value
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 555, in main
    game_instance.run()
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 326, in run
    self._update(dt)
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 366, in _update
    self.ui_manager.update(dt)
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\ui_manager.py", line 443, in update
    self.screens[self.active_screen].update(dt)
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 1832, in update
    self.update_player_stats(self.game_manager.player)
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\game_screen.py", line 1169, in update_player_stats
    self.player_stats_components["buff_text"].set_text(buff_display_text)
                                                       ^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'buff_display_text' where it is not associated with a value
