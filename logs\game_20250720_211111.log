[2025-07-20 21:11:11] [WARNING] [main.py:173] 游戏启动
[2025-07-20 21:11:11] [WARNING] [main.py:313] 开始创建游戏界面
[2025-07-20 21:11:11] [ERROR] [main.py:366] 创建背包界面失败: name 'item_detail_panel' is not defined
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 363, in _create_screens
    inventory_screen = InventoryScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 46, in __init__
    self._create_ui_elements()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 646, in _create_ui_elements
    item_detail_panel.rect.left + 20,
    ^^^^^^^^^^^^^^^^^
NameError: name 'item_detail_panel' is not defined
[2025-07-20 21:11:13] [WARNING] [main.py:422] 游戏界面创建完成
[2025-07-20 21:11:29] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:30] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:30] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:30] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:32] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:33] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:11:33] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
