[2025-07-20 21:01:21] [WARNING] [main.py:173] 游戏启动
[2025-07-20 21:01:21] [WARNING] [main.py:313] 开始创建游戏界面
[2025-07-20 21:01:21] [ERROR] [main.py:366] 创建背包界面失败: name 'rows' is not defined
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 363, in _create_screens
    inventory_screen = InventoryScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 46, in __init__
    self._create_ui_elements()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 560, in _create_ui_elements
    slots_area_height = rows * (slot_height + slot_spacing) - slot_spacing
                        ^^^^
NameError: name 'rows' is not defined
[2025-07-20 21:01:23] [WARNING] [main.py:422] 游戏界面创建完成
[2025-07-20 21:01:57] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:01:58] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:02:00] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:02:01] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:02:01] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
[2025-07-20 21:02:01] [ERROR] [ui_manager.py:341] 尝试显示不存在的界面: inventory
