from fastapi import APIRouter, Depends, HTTPException, status
from bson import ObjectId
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.models.player import PlayerCreate, Player, PlayerInDB, GameState
from app.models.user import User
from app.api.auth import get_current_user
from app.database import players_collection, game_states_collection

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/players", response_model=Player)
async def create_player(player: PlayerCreate, current_user: User = Depends(get_current_user)):
    """创建玩家角色"""
    # 检查用户是否已有角色
    existing_player = await players_collection.find_one({"user_id": current_user.id})
    if existing_player:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has a player character"
        )

    # 创建初始角色数据
    player_data = {
        "user_id": current_user.id,
        "name": player.name,
        "character_class": player.character_class,
        "gender": player.gender,
        "level": 1,
        "exp": 0,
        "gold": 0,
        "yuanbao": 5,
        "vip_level": 0,
        "vip_activated": [],
        "hp": 100,
        "max_hp": 100,
        "mp": 50,
        "max_mp": 50,
        "attack": 10,
        "defense": 5,
        "attack_speed": 1.0,
        "crit_rate": 5,
        "base_accuracy": 70,
        "agility": 10,
        "luck": 1,
        "magic_defense": 0,
        "magic_dodge": 0,
        "equipment": {
            "武器": None,
            "防具": None,
            "头盔": None,
            "项链": None,
            "左手镯": None,
            "右手镯": None,
            "左戒指": None,
            "右戒指": None,
            "勋章": None
        },
        "inventory": [],
        "storage": [],
        "locked_items": [],
        "skills": {},
        "disabled_skills": [],
        "skill_slots": {},
        "skill_proficiencies": {},
        "created_at": datetime.now(),
        "last_updated": datetime.now()
    }

    # 根据职业调整初始属性
    if player.character_class == "战士":
        player_data["hp"] = 120
        player_data["max_hp"] = 120
        player_data["mp"] = 30
        player_data["max_mp"] = 30
        player_data["attack"] = 12
        player_data["defense"] = 8
    elif player.character_class == "法师":
        player_data["hp"] = 80
        player_data["max_hp"] = 80
        player_data["mp"] = 100
        player_data["max_mp"] = 100
        player_data["attack"] = 15
        player_data["defense"] = 3
    elif player.character_class == "道士":
        player_data["hp"] = 100
        player_data["max_hp"] = 100
        player_data["mp"] = 80
        player_data["max_mp"] = 80
        player_data["attack"] = 8
        player_data["defense"] = 6

    # 插入数据库
    try:
        result = await players_collection.insert_one(player_data)
        player_data["_id"] = str(result.inserted_id)
        logger.info(f"Player created: {player.name} for user {current_user.username}")
    except Exception as e:
        logger.error(f"Failed to create player: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create player"
        )

    # 创建初始游戏状态
    game_state = {
        "player_id": player_data["_id"],
        "current_map": "比奇省",
        "in_battle": False,
        "auto_battle": False,
        "battle_stats": {
            "exp_gained": 0,
            "gold_gained": 0,
            "monsters_killed": 0,
            "battles_count": 0
        },
        "last_updated": datetime.now()
    }

    try:
        await game_states_collection.insert_one(game_state)
        logger.info(f"Game state created for player {player.name}")
    except Exception as e:
        logger.error(f"Failed to create game state: {e}")
        # 不抛出异常，因为玩家已经创建成功

    return player_data

@router.get("/players/me", response_model=Player)
async def get_my_player(current_user: User = Depends(get_current_user)):
    """获取当前用户的玩家角色"""
    player = await players_collection.find_one({"user_id": current_user.id})
    if not player:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Player not found"
        )
    player["_id"] = str(player["_id"])
    return player

@router.get("/state", response_model=Dict[str, Any])
async def get_game_state(current_user: User = Depends(get_current_user)):
    """获取游戏状态，包括玩家信息和游戏状态"""
    # 获取玩家信息
    player = await players_collection.find_one({"user_id": current_user.id})
    if not player:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Player not found"
        )

    player_id = str(player["_id"])
    player["_id"] = player_id

    # 获取游戏状态
    game_state = await game_states_collection.find_one({"player_id": player_id})
    if not game_state:
        # 创建默认游戏状态
        game_state = {
            "player_id": player_id,
            "current_map": "比奇省",
            "in_battle": False,
            "auto_battle": False,
            "battle_stats": {
                "exp_gained": 0,
                "gold_gained": 0,
                "monsters_killed": 0,
                "battles_count": 0
            },
            "last_updated": datetime.now()
        }

        try:
            result = await game_states_collection.insert_one(game_state)
            game_state["_id"] = str(result.inserted_id)
            logger.info(f"Created default game state for player {player_id}")
        except Exception as e:
            logger.error(f"Failed to create default game state: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create game state"
            )
    else:
        game_state["_id"] = str(game_state["_id"])

    return {
        "player": player,
        "game_state": game_state
    }

@router.patch("/state", response_model=Dict[str, Any])
async def update_game_state(
    update_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """更新游戏状态"""
    # 获取玩家信息
    player = await players_collection.find_one({"user_id": current_user.id})
    if not player:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Player not found"
        )

    player_id = str(player["_id"])

    # 分离玩家数据和游戏状态数据
    player_data = update_data.get("player", {})
    game_state_data = update_data.get("game_state", {})

    # 更新玩家数据
    if player_data:
        # 移除不允许客户端更新的字段
        if "_id" in player_data:
            del player_data["_id"]
        if "user_id" in player_data:
            del player_data["user_id"]

        # 添加更新时间
        player_data["last_updated"] = datetime.now()

        try:
            # 更新玩家数据
            await players_collection.update_one(
                {"_id": ObjectId(player_id)},
                {"$set": player_data}
            )
            logger.info(f"Updated player data for {player_id}")
        except Exception as e:
            logger.error(f"Failed to update player data: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update player data"
            )

    # 更新游戏状态
    if game_state_data:
        # 移除不允许客户端更新的字段
        if "_id" in game_state_data:
            del game_state_data["_id"]
        if "player_id" in game_state_data:
            del game_state_data["player_id"]

        # 添加更新时间
        game_state_data["last_updated"] = datetime.now()

        try:
            # 更新游戏状态
            await game_states_collection.update_one(
                {"player_id": player_id},
                {"$set": game_state_data},
                upsert=True
            )
            logger.info(f"Updated game state for player {player_id}")
        except Exception as e:
            logger.error(f"Failed to update game state: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update game state"
            )

    # 获取更新后的数据
    updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
    updated_game_state = await game_states_collection.find_one({"player_id": player_id})

    # 转换ID为字符串
    if updated_player:
        updated_player["_id"] = str(updated_player["_id"])
    if updated_game_state:
        updated_game_state["_id"] = str(updated_game_state["_id"])

    return {
        "player": updated_player,
        "game_state": updated_game_state
    }

@router.post("/inventory/add", response_model=Dict[str, Any])
async def add_item_to_inventory(
    item_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """添加物品到玩家背包"""
    if players_collection is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available"
        )

    try:
        # 获取玩家信息
        player = await players_collection.find_one({"user_id": current_user.id})
        if not player:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Player not found"
            )

        player_id = str(player["_id"])

        # 验证物品数据
        if not item_data or not isinstance(item_data, dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid item data"
            )

        # 添加物品到背包
        result = await players_collection.update_one(
            {"_id": ObjectId(player_id)},
            {"$push": {"inventory": item_data}}
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add item to inventory"
            )

        # 获取更新后的玩家数据
        updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
        if updated_player:
            updated_player["_id"] = str(updated_player["_id"])

        return {
            "success": True,
            "message": "Item added to inventory",
            "player": updated_player
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding item to inventory: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add item to inventory"
        )

@router.post("/inventory/remove", response_model=Dict[str, Any])
async def remove_item_from_inventory(
    item_index: int,
    current_user: User = Depends(get_current_user)
):
    """从玩家背包移除物品"""
    if players_collection is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available"
        )

    try:
        # 获取玩家信息
        player = await players_collection.find_one({"user_id": current_user.id})
        if not player:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Player not found"
            )

        player_id = str(player["_id"])

        # 验证物品索引
        if item_index < 0 or "inventory" not in player or item_index >= len(player["inventory"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid item index"
            )

        # 获取要移除的物品
        item = player["inventory"][item_index]

        # 移除物品
        result = await players_collection.update_one(
            {"_id": ObjectId(player_id)},
            {"$pull": {"inventory": item}}
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to remove item from inventory"
            )

        # 获取更新后的玩家数据
        updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
        if updated_player:
            updated_player["_id"] = str(updated_player["_id"])

        return {
            "success": True,
            "message": "Item removed from inventory",
            "player": updated_player
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing item from inventory: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove item from inventory"
        )

@router.post("/action", response_model=Dict[str, Any])
async def execute_game_action(
    action: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """执行游戏动作"""
    # 获取玩家信息
    player = await players_collection.find_one({"user_id": current_user.id})
    if not player:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Player not found"
        )

    player_id = str(player["_id"])

    # 获取游戏状态
    game_state = await game_states_collection.find_one({"player_id": player_id})
    if not game_state:
        # 创建默认游戏状态
        game_state = {
            "player_id": player_id,
            "current_map": "比奇省",
            "in_battle": False,
            "auto_battle": False,
            "battle_stats": {
                "exp_gained": 0,
                "gold_gained": 0,
                "monsters_killed": 0,
                "battles_count": 0
            },
            "last_updated": datetime.now()
        }

        try:
            result = await game_states_collection.insert_one(game_state)
            game_state["_id"] = str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to create default game state: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create game state"
            )

    # 处理不同类型的动作
    action_type = action.get("type", "")
    action_data = action.get("data", {})
    result = {"success": False, "message": "Unknown action type"}

    if action_type == "change_map":
        # 处理地图切换
        map_name = action_data.get("map_name", "")
        if map_name:
            # 更新游戏状态
            await game_states_collection.update_one(
                {"player_id": player_id},
                {"$set": {"current_map": map_name, "last_updated": datetime.now()}}
            )
            result = {"success": True, "message": f"Changed map to {map_name}"}

    elif action_type == "battle":
        # 处理战斗
        monster_id = action_data.get("monster_id", "")
        auto_battle = action_data.get("auto_battle", False)

        # 更新游戏状态
        await game_states_collection.update_one(
            {"player_id": player_id},
            {"$set": {
                "in_battle": True,
                "auto_battle": auto_battle,
                "last_updated": datetime.now()
            }}
        )

        # 这里可以添加更多战斗逻辑
        result = {"success": True, "message": "Battle started"}

    elif action_type == "end_battle":
        # 处理结束战斗
        battle_result = action_data.get("result", {})

        # 更新游戏状态
        await game_states_collection.update_one(
            {"player_id": player_id},
            {"$set": {
                "in_battle": False,
                "last_updated": datetime.now()
            }}
        )

        # 更新战斗统计
        if battle_result:
            # 获取当前战斗统计
            current_stats = game_state.get("battle_stats", {})

            # 更新统计
            current_stats["exp_gained"] = current_stats.get("exp_gained", 0) + battle_result.get("exp_gained", 0)
            current_stats["gold_gained"] = current_stats.get("gold_gained", 0) + battle_result.get("gold_gained", 0)
            current_stats["monsters_killed"] = current_stats.get("monsters_killed", 0) + 1
            current_stats["battles_count"] = current_stats.get("battles_count", 0) + 1

            # 保存更新后的统计
            await game_states_collection.update_one(
                {"player_id": player_id},
                {"$set": {"battle_stats": current_stats}}
            )

        result = {"success": True, "message": "Battle ended"}

    # 获取更新后的数据
    updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
    updated_game_state = await game_states_collection.find_one({"player_id": player_id})

    # 转换ID为字符串
    if updated_player:
        updated_player["_id"] = str(updated_player["_id"])
    if updated_game_state:
        updated_game_state["_id"] = str(updated_game_state["_id"])

    result["player"] = updated_player
    result["game_state"] = updated_game_state

    return result

@router.post("/player/update", response_model=Dict[str, Any])
async def update_player_attributes(
    attributes: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """更新玩家属性"""
    if players_collection is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available"
        )

    try:
        # 获取玩家信息
        player = await players_collection.find_one({"user_id": current_user.id})
        if not player:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Player not found"
            )

        player_id = str(player["_id"])

        # 验证属性数据
        if not attributes or not isinstance(attributes, dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid attributes data"
            )

        # 移除不允许客户端更新的字段
        if "_id" in attributes:
            del attributes["_id"]
        if "user_id" in attributes:
            del attributes["user_id"]

        # 添加更新时间
        attributes["last_updated"] = datetime.now()

        # 更新玩家属性
        result = await players_collection.update_one(
            {"_id": ObjectId(player_id)},
            {"$set": attributes}
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update player attributes"
            )

        # 获取更新后的玩家数据
        updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
        if updated_player:
            updated_player["_id"] = str(updated_player["_id"])

        return {
            "success": True,
            "message": "Player attributes updated",
            "player": updated_player
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating player attributes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update player attributes"
        )

@router.post("/skills/update", response_model=Dict[str, Any])
async def update_player_skills(
    skills_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """更新玩家技能"""
    if players_collection is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available"
        )

    try:
        # 获取玩家信息
        player = await players_collection.find_one({"user_id": current_user.id})
        if not player:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Player not found"
            )

        player_id = str(player["_id"])

        # 验证技能数据
        if not skills_data or not isinstance(skills_data, dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid skills data"
            )

        # 更新玩家技能
        update_data = {}

        if "skills" in skills_data:
            update_data["skills"] = skills_data["skills"]

        if "disabled_skills" in skills_data:
            update_data["disabled_skills"] = skills_data["disabled_skills"]

        if "skill_slots" in skills_data:
            update_data["skill_slots"] = skills_data["skill_slots"]

        if "skill_proficiencies" in skills_data:
            update_data["skill_proficiencies"] = skills_data["skill_proficiencies"]

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid skills data provided"
            )

        # 添加更新时间
        update_data["last_updated"] = datetime.now()

        # 更新玩家技能
        result = await players_collection.update_one(
            {"_id": ObjectId(player_id)},
            {"$set": update_data}
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update player skills"
            )

        # 获取更新后的玩家数据
        updated_player = await players_collection.find_one({"_id": ObjectId(player_id)})
        if updated_player:
            updated_player["_id"] = str(updated_player["_id"])

        return {
            "success": True,
            "message": "Player skills updated",
            "player": updated_player
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating player skills: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update player skills"
        )
