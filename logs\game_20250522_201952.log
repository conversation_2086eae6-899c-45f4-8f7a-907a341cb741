[2025-05-22 20:19:52] [WARNING] [main.py:106] 游戏启动
[2025-05-22 20:19:52] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 20:19:52] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 20:19:55] [WARNING] [player.py:1734] 加载技能槽位时发现无效的键: initial
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:55] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:55] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:56] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:56] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

[2025-05-22 20:19:57] [ERROR] [game_screen.py:3403] 更新技能按钮 initial 时出错: can only concatenate str (not "int") to str
[2025-05-22 20:19:57] [ERROR] [game_screen.py:3405] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3322, in _update_skill_buttons
    logger.info(f"技能 {skill_name} (槽位 {slot_idx+1}) 可用，设置 active=True")
                                           ~~~~~~~~^~
TypeError: can only concatenate str (not "int") to str

