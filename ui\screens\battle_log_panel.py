# -*- coding: utf-8 -*-
"""
战斗日志面板
显示战斗日志信息，支持滚动查看
"""

import pygame
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from ui.screens.game_ui_base import GameUIPanel, GameUIConstants, UIComponentFactory
from utils.logger import logger

if TYPE_CHECKING:
    from core.game import Game

class BattleLogPanel(GameUIPanel):
    """战斗日志面板类"""
    
    def __init__(self, ui_manager, game_manager: 'Game', panel_rect: pygame.Rect):
        """
        初始化战斗日志面板
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
            panel_rect: 面板区域
        """
        super().__init__(ui_manager, game_manager, "battle_log_panel")
        self.panel_rect = panel_rect
        self.log_components = []
        self.update_interval = GameUIConstants.UPDATE_INTERVALS["battle_logs"]
        
        # 日志相关配置
        self.max_display_logs = 8  # 最多显示8行日志
        self.log_scroll_offset = 0  # 滚动偏移量
        self.full_battle_logs = []  # 完整的战斗日志列表
        self.line_height = 20  # 每行日志的高度
        
        self.create_components()
    
    def create_components(self):
        """创建战斗日志面板组件"""
        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            self.panel_rect,
            color=GameUIConstants.COLORS["panel_bg"],
            border_color=GameUIConstants.COLORS["panel_border"],
            border_width=GameUIConstants.SIZES["border_width"]
        )
        self.add_component(main_panel)
        self.components_map["main_panel"] = main_panel
        
        # 创建标题
        title_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 10,
            200,
            25
        )
        title_text = self.ui_manager.create_text(
            title_rect,
            "战斗日志",
            GameUIConstants.FONTS["normal"],
            GameUIConstants.COLORS["text_secondary"],
            "left"
        )
        self.add_component(title_text)
        self.components_map["title"] = title_text
        
        # 创建滚动提示
        scroll_hint_rect = pygame.Rect(
            self.panel_rect.right - 150,
            self.panel_rect.y + 10,
            140,
            25
        )
        scroll_hint = self.ui_manager.create_text(
            scroll_hint_rect,
            "使用滚轮查看更多",
            GameUIConstants.FONTS["small"],
            (150, 150, 180),
            "right"
        )
        self.add_component(scroll_hint)
        self.components_map["scroll_hint"] = scroll_hint
        
        # 创建日志条目
        self._create_log_entries()
        
        logger.debug("战斗日志面板组件创建完成")
    
    def _create_log_entries(self):
        """创建日志条目"""
        start_y = self.panel_rect.y + 40  # 标题下方40像素开始
        
        for i in range(self.max_display_logs):
            log_rect = pygame.Rect(
                self.panel_rect.x + 20,
                start_y + i * self.line_height,
                self.panel_rect.width - 40,
                18
            )
            log_text = self.ui_manager.create_text(
                log_rect,
                "",
                GameUIConstants.FONTS["small"],
                GameUIConstants.COLORS["text_primary"],
                "left"
            )
            self.add_component(log_text)
            self.log_components.append(log_text)
    
    def update_data(self):
        """更新战斗日志数据"""
        if not hasattr(self.game_manager, 'battle_logs'):
            return
        
        try:
            # 获取游戏管理器中的战斗日志
            game_logs = self.game_manager.battle_logs
            
            # 如果日志有更新，更新本地日志列表
            if len(game_logs) != len(self.full_battle_logs):
                self.full_battle_logs = game_logs.copy()
                self._update_log_display()
            
        except Exception as e:
            logger.error(f"更新战斗日志数据时出错: {e}")
    
    def _update_log_display(self):
        """更新日志显示"""
        # 计算要显示的日志
        total_logs = len(self.full_battle_logs)
        
        if total_logs == 0:
            # 没有日志，清空显示
            for log_component in self.log_components:
                log_component.set_text("")
            return
        
        # 计算显示范围
        start_index = max(0, total_logs - self.max_display_logs - self.log_scroll_offset)
        end_index = min(total_logs, start_index + self.max_display_logs)
        
        # 获取要显示的日志
        display_logs = self.full_battle_logs[start_index:end_index]
        
        # 更新日志组件
        for i, log_component in enumerate(self.log_components):
            if i < len(display_logs):
                log_text = display_logs[i]
                # 限制日志长度，避免显示过长
                if len(log_text) > 50:
                    log_text = log_text[:47] + "..."
                log_component.set_text(log_text)
            else:
                log_component.set_text("")
    
    def add_log(self, message: str):
        """添加新的日志消息"""
        if not message:
            return
        
        # 添加时间戳（可选）
        import time
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        # 添加到日志列表
        self.full_battle_logs.append(formatted_message)
        
        # 限制日志数量，避免内存占用过多
        max_logs = 1000
        if len(self.full_battle_logs) > max_logs:
            self.full_battle_logs = self.full_battle_logs[-max_logs:]
        
        # 更新显示
        self._update_log_display()
        
        # 自动滚动到最新日志
        self.scroll_to_bottom()
    
    def scroll_up(self, lines: int = 1):
        """向上滚动日志"""
        max_scroll = max(0, len(self.full_battle_logs) - self.max_display_logs)
        self.log_scroll_offset = min(self.log_scroll_offset + lines, max_scroll)
        self._update_log_display()
    
    def scroll_down(self, lines: int = 1):
        """向下滚动日志"""
        self.log_scroll_offset = max(0, self.log_scroll_offset - lines)
        self._update_log_display()
    
    def scroll_to_bottom(self):
        """滚动到底部（最新日志）"""
        self.log_scroll_offset = 0
        self._update_log_display()
    
    def scroll_to_top(self):
        """滚动到顶部（最旧日志）"""
        max_scroll = max(0, len(self.full_battle_logs) - self.max_display_logs)
        self.log_scroll_offset = max_scroll
        self._update_log_display()
    
    def handle_event(self, event: pygame.event.Event):
        """处理事件（主要是滚轮事件）"""
        if event.type == pygame.MOUSEWHEEL:
            # 检查鼠标是否在日志面板区域内
            mouse_pos = pygame.mouse.get_pos()
            if self.panel_rect.collidepoint(mouse_pos):
                if event.y > 0:  # 向上滚动
                    self.scroll_up(2)
                elif event.y < 0:  # 向下滚动
                    self.scroll_down(2)
                return True
        
        # 调用父类的事件处理
        super().handle_event(event)
        return False
    
    def clear_logs(self):
        """清空所有日志"""
        self.full_battle_logs.clear()
        self.log_scroll_offset = 0
        self._update_log_display()
    
    def get_log_count(self) -> int:
        """获取日志总数"""
        return len(self.full_battle_logs)
    
    def get_recent_logs(self, count: int = 10) -> List[str]:
        """获取最近的日志"""
        return self.full_battle_logs[-count:] if self.full_battle_logs else []
    
    def search_logs(self, keyword: str) -> List[str]:
        """搜索包含关键词的日志"""
        if not keyword:
            return []
        
        matching_logs = []
        for log in self.full_battle_logs:
            if keyword.lower() in log.lower():
                matching_logs.append(log)
        
        return matching_logs
    
    def export_logs(self, filename: str = None) -> bool:
        """导出日志到文件"""
        if not filename:
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"battle_logs_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("战斗日志导出\n")
                f.write("=" * 50 + "\n")
                for log in self.full_battle_logs:
                    f.write(log + "\n")
            
            logger.info(f"战斗日志已导出到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出战斗日志失败: {e}")
            return False
    
    def get_scroll_info(self) -> Dict[str, Any]:
        """获取滚动信息"""
        total_logs = len(self.full_battle_logs)
        max_scroll = max(0, total_logs - self.max_display_logs)
        
        return {
            "total_logs": total_logs,
            "displayed_logs": min(total_logs, self.max_display_logs),
            "scroll_offset": self.log_scroll_offset,
            "max_scroll": max_scroll,
            "can_scroll_up": self.log_scroll_offset < max_scroll,
            "can_scroll_down": self.log_scroll_offset > 0
        }
