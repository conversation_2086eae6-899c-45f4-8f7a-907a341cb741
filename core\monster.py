import random
import math # 新增 math 导入
from typing import Dict, Tuple, Any, Optional, Union, TYPE_CHECKING

from utils.logger import logger

# 类型提示导入
if TYPE_CHECKING:
    from core.player import Player
    from core.types import PlayerType


class Monster:
    def __init__(self, name, level, hp, defense, magic_defense, attack_range, exp, attack_speed=1.0, gold=None,
                 x=0, y=0, size=9, color=(255, 0, 0)):  # 添加 gold 和新的地图属性参数
        self.name = name
        self.level = level
        self.hp = hp
        self.max_hp = hp
        self.defense = defense
        self.magic_defense = magic_defense
        self.attack_range = attack_range
        self.exp = exp  # 改名
        self.attack_speed = attack_speed
        # 设置金币掉落，默认为等级 * 20
        self.gold = gold if gold is not None else level * 20

        # 地图相关属性
        self.x = x
        self.y = y
        self.size = size
        self.color = color
        self.base_speed = 2  # 像素/帧或秒，根据游戏循环调整
        self.chase_speed = 4 # 像素/帧或秒
        self.current_speed = self.base_speed
        self.direction = [random.uniform(-1, 1), random.uniform(-1, 1)]
        dir_len = math.sqrt(self.direction[0]**2 + self.direction[1]**2)
        if dir_len > 0:
            self.direction[0] /= dir_len
            self.direction[1] /= dir_len
        else:
            self.direction = [0, -1] # 默认向上移动

        # 导入全局战斗速度修正因子
        from core.config import GameConfig
        # 应用全局战斗速度修正 - 降低战斗速度
        modified_speed = attack_speed * GameConfig.BATTLE_SPEED_MODIFIER

        # 根据攻击速度计算攻击间隔（单位：秒）
        self.attack_interval = 1.0 / modified_speed if modified_speed > 0 else float('inf')
        self.last_attack_time = 0
        self.is_dead = False
        self.death_handled = False # 添加死亡处理标记

        # 基础属性 - 调整名称为准确性和敏捷性
        self.accuracy = max(10, level * 1.0)  # 准确度随等级提升，但有基础值
        self.agility = max(5, level * 0.8)     # 敏捷性随等级提升，但有基础值

        # 初始化状态效果
        self.poison_effects = []

        # 记录怪物初始化属性
        logger.debug(f"怪物初始化: {name} - 等级:{level} 准确:{self.accuracy} 敏捷:{self.agility} 攻击间隔:{self.attack_interval}秒")

    def take_damage(self, damage):
        """受到伤害"""
        # 处理damage是元组的情况（可能包含暴击信息）
        if isinstance(damage, tuple):
            # 如果是 (damage, is_crit) 形式的元组，只取实际伤害值
            if len(damage) >= 1:
                damage = damage[0]
            else:
                logger.warning(f"无效的怪物伤害值元组: {damage}")
                return 0

        # 确保damage是一个有效的数值
        if not isinstance(damage, (int, float)) or damage < 0:
            logger.warning(f"无效的怪物伤害值: {damage}")
            return 0

        # 计算实际伤害（可以考虑怪物防御）
        actual_damage = max(1, damage - self.defense)

        # 减少生命值
        self.hp -= actual_damage
        if self.hp <= 0:
            self.hp = 0
            self.is_dead = True

        # 返回实际伤害
        return actual_damage

    def calculate_damage(self):
        """计算伤害"""
        if isinstance(self.attack_range, dict):
            min_attack = self.attack_range.get("min", 1)
            max_attack = self.attack_range.get("max", 2)
        else:
            # 处理旧格式的攻击范围
            try:
                min_attack, max_attack = self.attack_range
            except:
                min_attack, max_attack = 1, 2  # 默认值

        # 确保最小和最大值正确
        if min_attack > max_attack:
            min_attack, max_attack = max_attack, min_attack

        # 计算基础伤害
        base_damage = random.randint(int(min_attack), int(max_attack))

        # 伤害波动 (±10%)
        final_damage = int(base_damage * random.uniform(0.9, 1.1))

        # 确保至少造成1点伤害
        return max(1, final_damage)

    def attack_player(self, player):
        """攻击玩家"""
        # 命中判定
        hit_chance = min(95, max(30, (self.accuracy / (player.agility + 1)) * 100))
        if random.randint(1, 100) > hit_chance:
            return 0  # 未命中

        # 计算伤害
        damage_result = self.calculate_damage()

        # 检查是否是元组（包含暴击信息）
        if isinstance(damage_result, tuple):
            damage = damage_result[0]  # 只取伤害值
        else:
            damage = damage_result

        # 应用伤害到玩家
        actual_damage = player.take_damage(damage)  # 使用玩家的 take_damage 方法计算实际伤害
        return actual_damage

    def reset_effects(self):
        """重置所有效果"""
        # 清除中毒和地面伤害效果
        if hasattr(self, "poison_effects"):
            self.poison_effects = []

        # 可以在这里清除其他效果，如眩晕、减速等
        logger.debug(f"已清除 {self.name} 的所有状态效果")

    def to_dict(self):
        """将怪物数据转换为字典"""
        try:
            data = {
                "name": self.name,
                "level": self.level,
                "hp": self.hp,
                "max_hp": self.max_hp,
                "defense": self.defense,
                "magic_defense": self.magic_defense,
                "attack_range": self.attack_range,
                "exp": self.exp,
                "attack_speed": self.attack_speed,
                "gold": self.gold,
                "accuracy": self.accuracy,
                "agility": self.agility,
                "x": self.x,
                "y": self.y,
                "size": self.size,
                "color": self.color,
                "base_speed": self.base_speed,
                "chase_speed": self.chase_speed,
                "current_speed": self.current_speed,
                "direction": self.direction
            }

            # 添加魅惑相关属性
            if hasattr(self, "charmed"):
                data["charmed"] = self.charmed
            if hasattr(self, "charmed_by"):
                data["charmed_by"] = self.charmed_by
            if hasattr(self, "charmed_time"):
                data["charmed_time"] = self.charmed_time
            if hasattr(self, "charmed_duration"):
                data["charmed_duration"] = self.charmed_duration

            return data
        except Exception as e:
            logger.error(f"怪物数据转换为字典时出错: {e}")
            return {
                "name": self.name,
                "level": self.level,
                "hp": 1,
                "max_hp": 1,
                "defense": 0,
                "magic_defense": 0,
                "attack_range": {"min": 1, "max": 2},
                "exp": 0
            }