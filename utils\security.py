import json
import os
import time
from typing import Dict, Any, Tuple, Union, Optional

from utils.logger import logger

class SecureValue:
    """安全值类，原先提供加密和验证功能，现在只是简单存储值"""
    
    def __init__(self, initial_value: Union[int, float]):
        """初始化安全值"""
        self._value = initial_value
    
    def _generate_key(self) -> bytes:
        """生成加密密钥（已禁用）"""
        return b''
    
    def _encrypt(self, value: Union[int, float]) -> bytes:
        """加密值（已禁用）"""
        return str(value).encode()
    
    def _decrypt(self) -> Union[int, float]:
        """解密值（已禁用）"""
        return self._value
    
    def _calculate_checksum(self, value: Union[int, float]) -> str:
        """计算校验和（已禁用）"""
        return "valid"
    
    def get(self) -> Union[int, float]:
        """获取值"""
        return self._value
    
    def set(self, value: Union[int, float]):
        """设置值"""
        self._value = value

class DataSecurity:
    """数据安全工具类，处理数据存储和加载，现在不再使用加密"""
    
    @staticmethod
    def encode_save_data(data: Dict[str, Any]) -> str:
        """
        编码保存数据，不再使用加密，只进行JSON序列化
        
        参数:
            data: 要编码的数据
            
        返回:
            JSON字符串
        """
        try:
            # 直接转换为JSON字符串
            json_data = json.dumps(data, ensure_ascii=False, indent=2)
            return json_data
        except Exception as e:
            logger.error(f"编码保存数据失败: {e}")
            return "{}"
    
    @staticmethod
    def decode_save_data(encoded_data: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        解码保存数据，不再使用解密，只进行JSON反序列化
        
        参数:
            encoded_data: JSON字符串
            
        返回:
            (成功标志, 解码数据, 错误消息)
        """
        try:
            # 直接解析JSON
            data = json.loads(encoded_data)
            return True, data, ""
        except Exception as e:
            logger.error(f"解码保存数据失败: {e}")
            return False, {}, str(e)
    
    @staticmethod
    def validate_data(data: Dict[str, Any], required_fields: list) -> Tuple[bool, str]:
        """
        验证数据完整性
        
        参数:
            data: 要验证的数据
            required_fields: 必需的字段列表
            
        返回:
            (验证结果, 错误消息)
        """
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
                
        return True, ""
    
    @staticmethod
    def create_backup(filename: str, data: Dict[str, Any]) -> bool:
        """
        创建数据备份
        
        参数:
            filename: 备份文件名
            data: 要备份的数据
            
        返回:
            是否成功
        """
        try:
            # 确保备份目录存在
            backup_dir = os.path.dirname(filename)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
                
            # 添加时间戳
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{filename}_{timestamp}.bak"
            
            # 编码数据（不再加密）
            json_data = json.dumps(data, ensure_ascii=False, indent=2)
            
            # 写入备份文件
            with open(backup_filename, "w", encoding="utf-8") as f:
                f.write(json_data)
                
            logger.info(f"创建备份: {backup_filename}")
            return True
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False 