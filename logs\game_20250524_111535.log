[2025-05-24 11:15:35] [INFO] [logger.py:60] 日志记录到文件: logs\game_20250524_111535.log
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/equipment.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/equipment.json, 数据大小: 14514 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载equipment配置: data/configs/equipment.json
[2025-05-24 11:15:35] [INFO] [config.py:265] 开始加载怪物配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/monsters.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/monsters.json, 数据大小: 20637 字节
[2025-05-24 11:15:35] [INFO] [config.py:275] 成功加载 0 个怪物配置
[2025-05-24 11:15:35] [INFO] [config.py:282] 开始加载地图数据
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/maps.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/maps.json, 数据大小: 4017 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载maps配置: data/configs/maps.json
[2025-05-24 11:15:35] [INFO] [config.py:288] 加载的原始地图数据类型: <class 'dict'>
[2025-05-24 11:15:35] [INFO] [config.py:290] 原始地图数据包含的键: ['maps']
[2025-05-24 11:15:35] [INFO] [config.py:292] maps键下的数据类型: <class 'dict'>
[2025-05-24 11:15:35] [INFO] [config.py:294] maps键下包含 13 个地图
[2025-05-24 11:15:35] [INFO] [config.py:308] 成功加载地图数据: 13个地图
[2025-05-24 11:15:35] [INFO] [config.py:309] 地图列表: ['比奇省', '沃玛森林', '骷髅洞', '比奇矿区', '毒蛇山谷', '沃玛寺庙', '蜈蚣洞', '石墓', '祖玛寺庙', '封魔谷', '金币副本', '元宝副本', '装备副本']
[2025-05-24 11:15:35] [INFO] [config.py:364] 已移除副本地图: 金币副本
[2025-05-24 11:15:35] [INFO] [config.py:364] 已移除副本地图: 元宝副本
[2025-05-24 11:15:35] [INFO] [config.py:364] 已移除副本地图: 装备副本
[2025-05-24 11:15:35] [INFO] [config.py:372] 最终加载了 10 个有效地图: ['比奇省', '沃玛森林', '骷髅洞', '比奇矿区', '毒蛇山谷', '沃玛寺庙', '蜈蚣洞', '石墓', '祖玛寺庙', '封魔谷']
[2025-05-24 11:15:35] [INFO] [config.py:384] 开始加载掉落率数据
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/drop_rates.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/drop_rates.json, 数据大小: 41587 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载drop_rates配置: data/configs/drop_rates.json
[2025-05-24 11:15:35] [INFO] [config.py:400] 成功加载掉落率数据
[2025-05-24 11:15:35] [INFO] [config.py:424] 开始加载技能配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/skills.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/skills.json, 数据大小: 8595 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载skills配置: data/configs/skills.json
[2025-05-24 11:15:35] [INFO] [config.py:434] 成功加载技能配置: 3个职业，21个技能
[2025-05-24 11:15:35] [INFO] [config.py:445] 开始加载技能书配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/skillbooks.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/skillbooks.json, 数据大小: 2916 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载skillbooks配置: data/configs/skillbooks.json
[2025-05-24 11:15:35] [INFO] [config.py:455] 成功加载技能书配置: 1个职业，3个技能书
[2025-05-24 11:15:35] [INFO] [config.py:466] 开始加载消耗品配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:237] 尝试加载JSON文件，计算出的绝对路径: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/consumables.json
[2025-05-24 11:15:35] [INFO] [resource_manager.py:250] 成功加载JSON数据: E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\data/configs/consumables.json, 数据大小: 3030 字节
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载consumables配置: data/configs/consumables.json
[2025-05-24 11:15:35] [INFO] [config.py:476] 成功加载消耗品配置: 1个分类，4个消耗品
[2025-05-24 11:15:35] [INFO] [auto_potion.py:91] 自动吃药系统已初始化
[2025-05-24 11:15:35] [INFO] [battle.py:71] 设置怪物死亡回调函数
[2025-05-24 11:15:35] [INFO] [battle.py:76] 设置玩家死亡回调函数
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/equipment.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载equipment配置: data/configs/equipment.json
[2025-05-24 11:15:35] [INFO] [config.py:265] 开始加载怪物配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/monsters.json
[2025-05-24 11:15:35] [INFO] [config.py:275] 成功加载 0 个怪物配置
[2025-05-24 11:15:35] [INFO] [config.py:282] 开始加载地图数据
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/maps.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载maps配置: data/configs/maps.json
[2025-05-24 11:15:35] [INFO] [config.py:288] 加载的原始地图数据类型: <class 'dict'>
[2025-05-24 11:15:35] [INFO] [config.py:290] 原始地图数据包含的键: ['maps']
[2025-05-24 11:15:35] [INFO] [config.py:292] maps键下的数据类型: <class 'dict'>
[2025-05-24 11:15:35] [INFO] [config.py:294] maps键下包含 10 个地图
[2025-05-24 11:15:35] [INFO] [config.py:308] 成功加载地图数据: 10个地图
[2025-05-24 11:15:35] [INFO] [config.py:309] 地图列表: ['比奇省', '沃玛森林', '骷髅洞', '比奇矿区', '毒蛇山谷', '沃玛寺庙', '蜈蚣洞', '石墓', '祖玛寺庙', '封魔谷']
[2025-05-24 11:15:35] [INFO] [config.py:372] 最终加载了 10 个有效地图: ['比奇省', '沃玛森林', '骷髅洞', '比奇矿区', '毒蛇山谷', '沃玛寺庙', '蜈蚣洞', '石墓', '祖玛寺庙', '封魔谷']
[2025-05-24 11:15:35] [INFO] [config.py:384] 开始加载掉落率数据
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/drop_rates.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载drop_rates配置: data/configs/drop_rates.json
[2025-05-24 11:15:35] [INFO] [config.py:400] 成功加载掉落率数据
[2025-05-24 11:15:35] [INFO] [config.py:424] 开始加载技能配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/skills.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载skills配置: data/configs/skills.json
[2025-05-24 11:15:35] [INFO] [config.py:434] 成功加载技能配置: 3个职业，21个技能
[2025-05-24 11:15:35] [INFO] [config.py:445] 开始加载技能书配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/skillbooks.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载skillbooks配置: data/configs/skillbooks.json
[2025-05-24 11:15:35] [INFO] [config.py:455] 成功加载技能书配置: 1个职业，3个技能书
[2025-05-24 11:15:35] [INFO] [config.py:466] 开始加载消耗品配置
[2025-05-24 11:15:35] [INFO] [resource_manager.py:231] 从缓存中加载JSON数据: data/configs/consumables.json
[2025-05-24 11:15:35] [INFO] [config.py:496] 成功加载consumables配置: data/configs/consumables.json
[2025-05-24 11:15:35] [INFO] [config.py:476] 成功加载消耗品配置: 1个分类，4个消耗品
[2025-05-24 11:15:35] [INFO] [auto_potion.py:91] 自动吃药系统已初始化
[2025-05-24 11:15:35] [INFO] [battle.py:71] 设置怪物死亡回调函数
[2025-05-24 11:15:35] [INFO] [battle.py:76] 设置玩家死亡回调函数
[2025-05-24 11:15:35] [INFO] [player.py:163] 添加初始武器: 木剑
[2025-05-24 11:15:35] [INFO] [player.py:177] 添加初始防具: 布衣(男)
[2025-05-24 11:15:35] [INFO] [player.py:163] 添加初始武器: 木剑
[2025-05-24 11:15:35] [INFO] [player.py:177] 添加初始防具: 布衣(男)
