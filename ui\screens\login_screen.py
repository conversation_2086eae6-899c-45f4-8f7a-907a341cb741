"""
登录界面
用于用户登录和注册
"""

import pygame
import time
import logging
from typing import Dict, Any, Optional, Callable

from ui.ui_manager import Screen
from ui.components import Panel, Button, Text, InputField
from network.api import GameAPI

logger = logging.getLogger(__name__)

class LoginScreen(Screen):
    """登录界面类"""

    def __init__(self, ui_manager, game_manager=None):
        """初始化登录界面"""
        super().__init__("login")
        self.ui_manager = ui_manager
        self.game_manager = game_manager

        # 初始化API
        self.api = GameAPI("http://localhost:8000")

        # 测试服务器连接
        try:
            import requests
            response = requests.get("http://localhost:8000/health")
            print(f"服务器连接测试: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"服务器连接测试失败: {e}")

        # 界面状态
        self.is_registering = True  # 默认为注册模式

        # 输入字段
        self.username_input = None
        self.password_input = None
        self.email_input = None
        self.confirm_password_input = None

        # 错误提示
        self.error_text = None

        # 创建UI组件
        self._create_ui()

        # 自动填充测试用户名和密码
        import random
        random_num = random.randint(1000, 9999)
        self.username_input.text = f"testuser{random_num}"
        self.password_input.text = "password123"
        if self.confirm_password_input:
            self.confirm_password_input.text = "password123"

        # 设置自动提交定时器
        self.auto_submit_timer = 2.0  # 2秒后自动提交

    def _create_ui(self):
        """创建UI组件"""
        screen_size = pygame.display.get_surface().get_size()

        # 创建主面板
        panel_width = 400
        panel_height = 300 if self.is_registering else 250  # 减小注册模式的面板高度
        panel_rect = pygame.Rect(
            (screen_size[0] - panel_width) // 2,
            (screen_size[1] - panel_height) // 2,
            panel_width,
            panel_height
        )
        panel = Panel(
            panel_rect,
            color=(40, 40, 60),
            border_color=(80, 80, 120),
            border_width=2
        )
        self.add_component(panel)

        # 标题
        title_text = "注册账号" if self.is_registering else "登录游戏"
        title_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + 20,
            panel_rect.width - 40,
            30
        )
        title = self.ui_manager.create_text(
            title_rect,
            title_text,
            "chinese_large",
            (220, 220, 255),
            "center"
        )
        self.add_component(title)

        # 用户名输入框
        username_label_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + 70,
            80,
            30
        )
        username_label = self.ui_manager.create_text(
            username_label_rect,
            "用户名:",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(username_label)

        username_input_rect = pygame.Rect(
            panel_rect.x + 120,
            panel_rect.y + 70,
            panel_rect.width - 150,
            30
        )
        self.username_input = InputField(
            username_input_rect,
            "",
            self.ui_manager.fonts["chinese_normal"],
            max_length=20
        )
        self.add_component(self.username_input)

        # 设置密码输入框位置
        password_y = 110

        # 密码输入框
        password_label_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + password_y,
            80,
            30
        )
        password_label = self.ui_manager.create_text(
            password_label_rect,
            "密码:",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(password_label)

        password_input_rect = pygame.Rect(
            panel_rect.x + 120,
            panel_rect.y + password_y,
            panel_rect.width - 150,
            30
        )
        self.password_input = InputField(
            password_input_rect,
            "",
            self.ui_manager.fonts["chinese_normal"],
            max_length=20,
            password=True
        )
        self.add_component(self.password_input)

        # 设置Tab键跳转
        self.username_input.next_input_field = self.password_input

        # 如果是注册模式，添加确认密码输入框
        if self.is_registering:
            confirm_password_label_rect = pygame.Rect(
                panel_rect.x + 30,
                panel_rect.y + 150,  # 调整位置
                80,
                30
            )
            confirm_password_label = self.ui_manager.create_text(
                confirm_password_label_rect,
                "确认密码:",
                "chinese_normal",
                (200, 200, 200),
                "left"
            )
            self.add_component(confirm_password_label)

            confirm_password_input_rect = pygame.Rect(
                panel_rect.x + 120,
                panel_rect.y + 150,  # 调整位置
                panel_rect.width - 150,
                30
            )
            self.confirm_password_input = InputField(
                confirm_password_input_rect,
                "",
                self.ui_manager.fonts["chinese_normal"],
                max_length=20,
                password=True
            )
            self.add_component(self.confirm_password_input)

            # 设置Tab键跳转链
            self.password_input.next_input_field = self.confirm_password_input
            self.confirm_password_input.next_input_field = self.username_input

            # 调整按钮位置
            button_y = 200  # 调整位置
        else:
            button_y = 160
            # 登录模式下的Tab键循环跳转
            self.password_input.next_input_field = self.username_input

        # 登录/注册按钮
        button_text = "注册" if self.is_registering else "登录"
        button_rect = pygame.Rect(
            panel_rect.x + (panel_rect.width - 200) // 2,
            panel_rect.y + button_y,
            200,
            40
        )
        login_button = self.ui_manager.create_button(
            button_rect,
            button_text,
            self._on_submit,
            "chinese_normal"
        )
        self.add_component(login_button)

        # 切换模式按钮
        switch_text = "已有账号？去登录" if self.is_registering else "没有账号？去注册"
        switch_rect = pygame.Rect(
            panel_rect.x + (panel_rect.width - 200) // 2,
            panel_rect.y + button_y + 50,
            200,
            30
        )
        switch_button = self.ui_manager.create_button(
            switch_rect,
            switch_text,
            self._on_switch_mode,
            "chinese_small"
        )
        self.add_component(switch_button)

        # 错误信息文本
        error_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + panel_rect.height - 40,
            panel_rect.width - 40,
            30
        )
        self.error_text = self.ui_manager.create_text(
            error_rect,
            "",
            "chinese_small",
            (255, 100, 100),
            "center"
        )
        self.add_component(self.error_text)

    def _on_submit(self):
        """处理登录/注册按钮点击"""
        username = self.username_input.text.strip()
        password = self.password_input.text.strip()

        # 基本验证
        if not username:
            self.error_text.set_text("用户名不能为空")
            return

        if not password:
            self.error_text.set_text("密码不能为空")
            return

        # 更严格的验证
        if len(username) < 3:
            self.error_text.set_text("用户名必须至少包含3个字符")
            return

        if len(password) < 6:
            self.error_text.set_text("密码必须至少包含6个字符")
            return

        if self.is_registering:
            confirm_password = self.confirm_password_input.text.strip()

            if password != confirm_password:
                self.error_text.set_text("两次输入的密码不一致")
                return

            try:
                # 显示加载中提示
                self.error_text.set_text("正在注册，请稍候...")

                # 调用注册API - 使用用户名作为邮箱（因为服务器端需要邮箱字段）
                email = username + "@example.com"  # 创建一个虚拟邮箱
                result = self.api.register(username, email, password)
                logger.info(f"注册成功: {result}")

                # 显示登录中提示
                self.error_text.set_text("注册成功，正在登录...")

                # 自动登录
                self.api.login(username, password)

                # 切换到角色创建界面
                self.ui_manager.show_screen("character_creation")
            except ValueError as ve:
                # 客户端验证错误
                logger.error(f"注册验证失败: {ve}")
                self.error_text.set_text(f"注册失败: {str(ve)}")
            except Exception as e:
                # 服务器错误
                logger.error(f"注册失败: {e}")

                # 尝试提取更友好的错误信息
                error_msg = str(e)
                if "Username already registered" in error_msg:
                    self.error_text.set_text("用户名已被注册")
                elif "Email already registered" in error_msg:
                    self.error_text.set_text("邮箱已被注册")
                elif "400 Client Error" in error_msg:
                    self.error_text.set_text("注册信息格式不正确")
                elif "503 Server Error" in error_msg:
                    self.error_text.set_text("服务器暂时不可用，请稍后再试")
                else:
                    self.error_text.set_text(f"注册失败: {error_msg}")
        else:
            try:
                # 显示加载中提示
                self.error_text.set_text("正在登录，请稍候...")

                # 调用登录API
                result = self.api.login(username, password)
                logger.info(f"登录成功: {result}")

                # 将令牌保存到游戏管理器
                if self.game_manager:
                    self.game_manager.set_online_mode(result["access_token"])

                # 获取玩家信息
                try:
                    # 显示加载中提示
                    self.error_text.set_text("正在获取角色信息...")

                    try:
                        player = self.api.get_player()
                        logger.info(f"获取玩家信息成功: {player}")

                        # 如果有角色，进入游戏
                        self.ui_manager.show_screen("game")
                    except Exception as player_error:
                        logger.error(f"获取玩家信息失败: {player_error}")
                        logger.info("跳转到角色创建界面")

                        # 如果没有角色，进入角色创建界面
                        self.ui_manager.show_screen("character_creation")
                except Exception as e:
                    logger.error(f"处理玩家信息时发生错误: {e}")

                    # 如果发生错误，也进入角色创建界面
                    logger.info("发生错误，跳转到角色创建界面")
                    self.ui_manager.show_screen("character_creation")
            except ValueError as ve:
                # 客户端验证错误
                logger.error(f"登录验证失败: {ve}")
                self.error_text.set_text(f"登录失败: {str(ve)}")
            except Exception as e:
                # 服务器错误
                logger.error(f"登录失败: {e}")

                # 尝试提取更友好的错误信息
                error_msg = str(e)
                if "401 Client Error" in error_msg:
                    self.error_text.set_text("用户名或密码不正确")
                elif "503 Server Error" in error_msg:
                    self.error_text.set_text("服务器暂时不可用，请稍后再试")
                else:
                    self.error_text.set_text(f"登录失败: {error_msg}")

    def _on_switch_mode(self):
        """切换登录/注册模式"""
        self.is_registering = not self.is_registering
        self.clear_components()
        self._create_ui()

    def update(self, dt):
        """更新界面"""
        super().update(dt)

        # 处理自动提交
        if hasattr(self, 'auto_submit_timer') and self.auto_submit_timer > 0:
            self.auto_submit_timer -= dt
            if self.auto_submit_timer <= 0:
                print("自动提交登录/注册表单")
                self._on_submit()
