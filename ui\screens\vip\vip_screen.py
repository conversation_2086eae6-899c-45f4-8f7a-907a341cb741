import pygame
import logging
from ui.components import Panel, Button, Text, UIComponent
from ui.ui_manager import Screen

# 设置日志记录器
logger = logging.getLogger(__name__)

class VIPScreen(Screen):
    """VIP界面类"""
    
    def __init__(self, ui_manager, game_manager):
        """初始化VIP界面"""
        super().__init__("vip")
        logger.info("初始化VIP界面")
        
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.title = "VIP特权"
        
        # VIP等级和价格配置
        self.vip_costs = {
            1: 30,    # VIP1需要30元宝
            2: 100,   # VIP2需要100元宝
            3: 500,   # VIP3需要500元宝
            4: 1000,  # VIP4需要1000元宝
            5: 3000,  # VIP5需要3000元宝
            6: 5000   # VIP6需要5000元宝
        }
        
        # VIP特权说明
        self.vip_benefits = {
            1: ["提升10%装备掉落几率", "装备售卖金币+20%"],
            2: ["提升20%装备掉落几率", "装备售卖金币+40%"],
            3: ["提升30%装备掉落几率", "装备售卖金币+60%"],
            4: ["提升40%装备掉落几率", "装备售卖金币+80%"],
            5: ["提升50%装备掉落几率", "装备售卖金币+100%"],
            6: ["提升60%装备掉落几率", "装备售卖金币+120%"]
        }
        
        # 创建界面元素
        self._create_ui_elements()
        logger.info("VIP界面初始化完成")

    def _create_ui_elements(self):
        """创建界面元素"""
        logger.info("开始创建VIP界面元素")
        screen_size = pygame.display.get_surface().get_size()
        
        # 顶部面板（标题）- 使用渐变色背景
        top_panel_height = 60
        top_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            color=(35, 35, 55),  # 稍微调深色调
            border_color=(70, 70, 120),  # 增加边框对比度
            border_width=2  # 增加边框宽度
        )
        self.add_component(top_panel)
        
        # 标题文本
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            self.title,
            "chinese_title",
            (220, 220, 220),
            "center"
        )
        self.add_component(title_text)
        
        # 返回按钮 - 优化样式
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 10, 100, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        # 设置返回按钮的颜色
        back_button.colors.update({
            "normal": (45, 45, 80),  # 稍微调亮
            "hover": (60, 60, 100),  # 增强悬停效果
            "pressed": (35, 35, 70),
            "text": (230, 230, 230),  # 增亮文字
            "border": (90, 90, 140)  # 增加边框对比度
        })
        self.add_component(back_button)
        
        # 元宝显示 - 移至右上角并优化显示
        yuanbao_rect = pygame.Rect(
            screen_size[0] - 300, 15, 280, 30
        )
        self.yuanbao_text = self.ui_manager.create_text(
            yuanbao_rect,
            "当前元宝: 0",
            "chinese_normal",
            (0, 191, 255),  # 深天蓝色
            "left"
        )
        self.add_component(self.yuanbao_text)
        
        # 主内容区域
        content_area = Panel(
            pygame.Rect(20, top_panel_height + 20, screen_size[0] - 40, screen_size[1] - top_panel_height - 80),
            color=(25, 25, 45),  # 更深的背景
            border_color=(50, 50, 80),
            border_width=2
        )
        self.add_component(content_area)
        self.content_area = content_area
        
        # 计算可用空间和滚动区域
        available_height = content_area.rect.height - 160  # 减去标题和说明区域高度
        
        # 当前VIP等级显示 - 更加醒目
        vip_title_rect = pygame.Rect(
            content_area.rect.left + 20,
            content_area.rect.top + 20,
            content_area.rect.width - 40,
            40
        )
        self.vip_title = self.ui_manager.create_text(
            vip_title_rect,
            "当前VIP等级: 0",
            "chinese_large",
            (255, 215, 0),  # 金色更醒目
            "center"
        )
        self.add_component(self.vip_title)
        
        # 添加VIP说明文本 - 更清晰的说明
        vip_desc_rect = pygame.Rect(
            content_area.rect.left + 20,
            vip_title_rect.bottom + 10,
            content_area.rect.width - 40,
            40  # 降低高度
        )
        self.vip_desc = self.ui_manager.create_text(
            vip_desc_rect,
            "VIP特权可以提升装备掉落率和出售价格，购买后永久有效",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(self.vip_desc)
        
        # 创建VIP等级列表面板
        vip_list_panel = Panel(
            pygame.Rect(
                content_area.rect.left + 20,
                vip_desc_rect.bottom + 20,
                content_area.rect.width - 40,
                available_height
            ),
            color=(30, 30, 50),
            border_color=(60, 60, 90),
            border_width=1
        )
        self.add_component(vip_list_panel)
        
        # 创建各个VIP等级的面板
        self.vip_panels = {}
        self.activate_buttons = {}
        
        # 计算每个VIP等级面板的尺寸和间距
        vip_count = 6  # 总共6个VIP等级
        panel_height = min(70, (available_height - (vip_count-1) * 10) // vip_count)  # 自适应高度
        panel_spacing = 10  # 减小间距
        panel_width = vip_list_panel.rect.width - 20  # 减小宽度留出边距
        
        # 计算说明文本和价格文本的最大宽度
        benefit_width = panel_width - 220  # 为按钮和VIP等级标题留出空间
        
        for level in range(1, 7):
            # 每个VIP等级的面板
            y_pos = vip_list_panel.rect.top + 10 + (panel_height + panel_spacing) * (level - 1)
            panel = Panel(
                pygame.Rect(
                    vip_list_panel.rect.left + 10,
                    y_pos,
                    panel_width,
                    panel_height
                ),
                color=(35, 35, 55),
                border_color=(60, 60, 90),
                border_width=1
            )
            self.add_component(panel)
            self.vip_panels[level] = panel
            
            # VIP等级标题 - 使用更加醒目的样式
            vip_level_text = self.ui_manager.create_text(
                pygame.Rect(
                    panel.rect.left + 10,
                    panel.rect.top + (panel_height - 25) // 2,  # 垂直居中
                    80,
                    25
                ),
                f"VIP{level}",
                "chinese_normal",
                (255, 215, 0),  # 金色
                "left"
            )
            self.add_component(vip_level_text)
            
            # VIP价格 - 调整位置和颜色
            vip_cost_text = self.ui_manager.create_text(
                pygame.Rect(
                    panel.rect.left + 90,
                    panel.rect.top + (panel_height - 25) // 2,  # 垂直居中
                    120,
                    25
                ),
                f"{self.vip_costs[level]}元宝",
                "chinese_small",
                (0, 191, 255),  # 深天蓝色
                "left"
            )
            self.add_component(vip_cost_text)
            
            # VIP特权描述 - 优化布局，确保文本不会被截断
            benefit_text = " | ".join(self.vip_benefits[level])
            vip_benefit_text = self.ui_manager.create_text(
                pygame.Rect(
                    panel.rect.left + 210,
                    panel.rect.top + (panel_height - 25) // 2,  # 垂直居中
                    benefit_width,
                    25
                ),
                benefit_text,
                "chinese_small",
                (180, 230, 180),
                "left"
            )
            self.add_component(vip_benefit_text)
            
            # 激活按钮 - 优化样式和位置
            activate_button = self.ui_manager.create_button(
                pygame.Rect(
                    panel.rect.right - 100,
                    panel.rect.top + (panel_height - 30) // 2,  # 垂直居中
                    90,
                    30
                ),
                "激活",
                lambda lvl=level: self._on_activate_vip(lvl),
                "chinese_normal"
            )
            # 设置按钮样式
            activate_button.colors.update({
                "normal": (60, 60, 100),
                "hover": (80, 80, 120),
                "pressed": (50, 50, 90),
                "text": (230, 230, 230),
                "border": (90, 90, 140)
            })
            self.add_component(activate_button)
            self.activate_buttons[level] = activate_button
            
        logger.info("VIP界面元素创建完成")

    def _on_back_click(self):
        """处理返回按钮点击"""
        logger.info("点击了返回按钮，返回游戏界面")
        self.ui_manager.show_screen("game")

    def _on_activate_vip(self, level):
        """处理VIP激活按钮点击"""
        player = self.game_manager.player
        if not player:
            logger.warning("激活VIP但玩家不存在")
            return
            
        # 获取当前VIP等级和激活所需元宝
        current_vip = player.vip_level
        required_yuanbao = self.vip_costs.get(level, 0)
        current_yuanbao = player.yuanbao
        
        # 检查是否已激活该等级
        if level in player.vip_activated:
            self.ui_manager.show_message("无需激活", f"VIP{level}已激活，无需重复购买")
            return
            
        # 检查是否满足激活条件
        if current_yuanbao < required_yuanbao:
            self.ui_manager.show_message("元宝不足", f"激活VIP{level}需要{required_yuanbao}元宝，当前元宝：{current_yuanbao}")
            return
            
        # 弹出确认对话框
        self.ui_manager.show_confirmation(
            f"激活VIP{level}",
            f"确定要使用{required_yuanbao}元宝激活VIP{level}吗？\n特权：{' | '.join(self.vip_benefits[level])}",
            lambda: self._confirm_activate_vip(level)
        )

    def _confirm_activate_vip(self, level):
        """确认激活VIP"""
        player = self.game_manager.player
        if not player:
            return
            
        # 尝试激活VIP
        if player.activate_vip(level):
            self.ui_manager.show_message("激活成功", f"VIP{level}激活成功！您已获得对应特权")
            # 更新界面
            self._update_vip_display()
        else:
            self.ui_manager.show_message("激活失败", "VIP激活失败，请稍后再试")

    def _update_vip_display(self):
        """更新VIP显示"""
        player = self.game_manager.player
        if not player:
            return
            
        # 更新当前VIP等级和元宝显示
        current_vip = player.vip_level
        current_yuanbao = player.yuanbao
        activated_levels = player.vip_activated
        
        self.vip_title.set_text(f"当前VIP等级: {current_vip}")
        self.yuanbao_text.set_text(f"当前元宝: {current_yuanbao}")
        
        # 更新各个等级的激活按钮状态
        for level, button in self.activate_buttons.items():
            if level in activated_levels:
                button.set_text("已激活")
                button.set_active(False)
                # 修改颜色为已激活状态
                button.colors["normal"] = (50, 100, 50)  # 绿色
                button.colors["hover"] = (60, 110, 60)  # 悬停状态
                button.colors["pressed"] = (50, 100, 50)  # 点击状态
            else:
                button.set_text("激活")
                button.set_active(True)
                # 恢复默认颜色
                button.colors["normal"] = (60, 60, 100)
                button.colors["hover"] = (80, 80, 120)
                button.colors["pressed"] = (50, 50, 90)
                
                # 如果元宝不足，禁用按钮
                if current_yuanbao < self.vip_costs[level]:
                    button.colors["normal"] = (80, 80, 100)  # 灰色
                    button.set_text(f"激活")  # 简化显示

    def show(self):
        """显示VIP界面"""
        logger.info("显示VIP界面")
        super().show()
        
        # 更新VIP显示
        self._update_vip_display()
        
        logger.info("VIP界面显示完成")

    def update(self, dt):
        """更新VIP界面"""
        super().update(dt)

    def draw(self, surface):
        """绘制VIP界面"""
        super().draw(surface) 