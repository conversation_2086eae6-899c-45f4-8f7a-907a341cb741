[2025-07-20 21:34:39] [WARNING] [main.py:173] 游戏启动
[2025-07-20 21:34:40] [WARNING] [main.py:313] 开始创建游戏界面
[2025-07-20 21:34:42] [WARNING] [main.py:422] 游戏界面创建完成
[2025-07-20 21:34:45] [ERROR] [main.py:1006] 游戏发生未处理的异常: No module named 'ui.components.panel'; 'ui.components' is not a package
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 1002, in main
    game_instance.run() # run() 现在包含清理逻辑
    ^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 511, in run
    self._update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 589, in _update
    self.ui_manager.update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\ui_manager.py", line 393, in update
    self.screens[self.active_screen].update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 2034, in update
    self.tooltip.show(self.hovered_item, mouse_pos[0], mouse_pos[1])
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 53, in show
    from ui.components.panel import Panel
ModuleNotFoundError: No module named 'ui.components.panel'; 'ui.components' is not a package
