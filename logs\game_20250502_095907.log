[2025-05-02 09:59:07] [WARNING] [main.py:105] 游戏启动
[2025-05-02 09:59:07] [WARNING] [main.py:223] 开始创建游戏界面
[2025-05-02 09:59:07] [ERROR] [main.py:276] 创建背包界面失败: Panel.__init__() takes from 2 to 6 positional arguments but 7 were given
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\main.py", line 273, in _create_screens
    inventory_screen = InventoryScreen(self.ui_manager, self.game)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\inventory_screen.py", line 39, in __init__
    self._create_ui_elements()
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\screens\inventory_screen.py", line 477, in _create_ui_elements
    self.inventory_list = ScrollableList(
                          ^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\youxi\pygame\Demo\Demo\ui\components.py", line 591, in __init__
    super().__init__(rect, color, border_color, border_width, border_radius, visible)
TypeError: Panel.__init__() takes from 2 to 6 positional arguments but 7 were given
[2025-05-02 09:59:07] [WARNING] [main.py:315] 游戏界面创建完成
