from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import uvicorn

from app.api import auth, game
from app.database import create_indexes, connect_to_mongodb
from app.db_init import init_database
from app.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("server.log")
    ]
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title=settings.APP_NAME)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路由
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(game.router, prefix="/api/game", tags=["Game"])

@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    logger.info("Server starting up...")

    # 连接到MongoDB
    db_connected = connect_to_mongodb()
    if db_connected:
        logger.info("Successfully connected to MongoDB")

        # 初始化数据库
        db_initialized = await init_database()
        if db_initialized:
            logger.info("Successfully initialized database")
        else:
            logger.warning("Failed to initialize database")
    else:
        logger.warning("Running without MongoDB connection. Some features will be unavailable.")

@app.get("/")
async def root():
    """根路径处理函数"""
    return {"message": f"Welcome to {settings.APP_NAME}"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok"}

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
