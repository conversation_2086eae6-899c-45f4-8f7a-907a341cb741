"""
角色创建界面
用于创建新角色
"""

import pygame
import logging
from typing import Dict, List, Optional, Any

from ui.ui_manager import Screen
from ui.components import Panel, Button, Text, InputField
from network.api import GameAPI

logger = logging.getLogger(__name__)

class CharacterCreationScreen(Screen):
    """角色创建界面类"""

    def __init__(self, ui_manager, game_manager=None):
        """初始化角色创建界面"""
        super().__init__("character_creation_online")
        self.ui_manager = ui_manager
        self.game_manager = game_manager

        # 使用游戏管理器中的API实例，而不是创建新的
        if game_manager and hasattr(game_manager, 'api'):
            self.api = game_manager.api
            logger.info("使用游戏管理器中的API实例")
        else:
            # 如果游戏管理器中没有API实例，则创建新的
            self.api = GameAPI("http://localhost:8000")
            logger.warning("创建新的API实例，可能会导致令牌丢失")

        # 角色信息
        self.name_input = None
        self.selected_class = "战士"
        self.selected_gender = "男"

        # 错误提示
        self.error_text = None

        # 创建UI组件
        self._create_ui()

    def _create_ui(self):
        """创建UI组件"""
        screen_size = pygame.display.get_surface().get_size()

        # 创建主面板
        panel_width = 500
        panel_height = 400
        panel_rect = pygame.Rect(
            (screen_size[0] - panel_width) // 2,
            (screen_size[1] - panel_height) // 2,
            panel_width,
            panel_height
        )
        panel = Panel(
            panel_rect,
            color=(40, 40, 60),
            border_color=(80, 80, 120),
            border_width=2
        )
        self.add_component(panel)

        # 标题
        title_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + 20,
            panel_rect.width - 40,
            30
        )
        title = self.ui_manager.create_text(
            title_rect,
            "创建角色",
            "chinese_large",
            (220, 220, 255),
            "center"
        )
        self.add_component(title)

        # 角色名输入框
        name_label_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + 70,
            80,
            30
        )
        name_label = self.ui_manager.create_text(
            name_label_rect,
            "角色名:",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(name_label)

        name_input_rect = pygame.Rect(
            panel_rect.x + 120,
            panel_rect.y + 70,
            panel_rect.width - 150,
            30
        )
        self.name_input = InputField(
            name_input_rect,
            "",
            self.ui_manager.fonts["chinese_normal"],
            max_length=12
        )
        self.add_component(self.name_input)

        # 职业选择
        class_label_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + 120,
            80,
            30
        )
        class_label = self.ui_manager.create_text(
            class_label_rect,
            "职业:",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(class_label)

        # 职业按钮
        classes = ["战士", "法师", "道士"]
        for i, class_name in enumerate(classes):
            class_btn_rect = pygame.Rect(
                panel_rect.x + 120 + i * 110,
                panel_rect.y + 120,
                100,
                30
            )
            class_btn = self.ui_manager.create_button(
                class_btn_rect,
                class_name,
                lambda c=class_name: self._on_class_select(c),
                "chinese_normal"
            )

            # 设置选中状态
            if class_name == self.selected_class:
                class_btn.colors.update({
                    "normal": (60, 100, 60),
                    "hover": (70, 120, 70),
                    "pressed": (50, 90, 50),
                    "text": (220, 255, 220),
                    "border": (100, 160, 100)
                })

            self.add_component(class_btn)

        # 性别选择
        gender_label_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + 170,
            80,
            30
        )
        gender_label = self.ui_manager.create_text(
            gender_label_rect,
            "性别:",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(gender_label)

        # 性别按钮
        genders = ["男", "女"]
        for i, gender in enumerate(genders):
            gender_btn_rect = pygame.Rect(
                panel_rect.x + 120 + i * 110,
                panel_rect.y + 170,
                100,
                30
            )
            gender_btn = self.ui_manager.create_button(
                gender_btn_rect,
                gender,
                lambda g=gender: self._on_gender_select(g),
                "chinese_normal"
            )

            # 设置选中状态
            if gender == self.selected_gender:
                gender_btn.colors.update({
                    "normal": (60, 100, 60),
                    "hover": (70, 120, 70),
                    "pressed": (50, 90, 50),
                    "text": (220, 255, 220),
                    "border": (100, 160, 100)
                })

            self.add_component(gender_btn)

        # 职业描述
        class_desc_rect = pygame.Rect(
            panel_rect.x + 30,
            panel_rect.y + 220,
            panel_rect.width - 60,
            80
        )
        class_desc = self.ui_manager.create_text(
            class_desc_rect,
            self._get_class_description(self.selected_class),
            "chinese_small",
            (180, 180, 200),
            "left"
        )
        self.add_component(class_desc)

        # 创建按钮
        create_btn_rect = pygame.Rect(
            panel_rect.x + (panel_rect.width - 200) // 2,
            panel_rect.y + 320,
            200,
            40
        )
        create_btn = self.ui_manager.create_button(
            create_btn_rect,
            "创建角色",
            self._on_create,
            "chinese_normal"
        )
        self.add_component(create_btn)

        # 错误信息文本
        error_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + panel_rect.height - 40,
            panel_rect.width - 40,
            30
        )
        self.error_text = self.ui_manager.create_text(
            error_rect,
            "",
            "chinese_small",
            (255, 100, 100),
            "center"
        )
        self.add_component(self.error_text)

    def _get_class_description(self, class_name):
        """获取职业描述"""
        descriptions = {
            "战士": "战士拥有强大的体魄和近战能力，擅长使用各种武器进行战斗。\n初始属性：生命值高，防御高，攻击中等，魔法值低。",
            "法师": "法师精通各种魔法，能够释放强大的法术攻击敌人。\n初始属性：魔法值高，攻击高，生命值低，防御低。",
            "道士": "道士擅长召唤和辅助魔法，能够召唤宠物协助战斗。\n初始属性：生命值中等，魔法值中等，攻击中等，防御中等。"
        }
        return descriptions.get(class_name, "")

    def _on_class_select(self, class_name):
        """处理职业选择"""
        self.selected_class = class_name
        self.clear_components()
        self._create_ui()

    def _on_gender_select(self, gender):
        """处理性别选择"""
        self.selected_gender = gender
        self.clear_components()
        self._create_ui()

    def _on_create(self):
        """处理创建角色按钮点击"""
        name = self.name_input.text.strip()

        if not name:
            self.error_text.set_text("角色名不能为空")
            return

        try:
            # 显示加载中提示
            self.error_text.set_text("正在创建角色，请稍候...")

            # 调用创建角色API
            try:
                result = self.api.create_player(name, self.selected_class, self.selected_gender)
                logger.info(f"创建角色成功: {result}")
            except Exception as api_error:
                logger.error(f"调用创建角色API失败: {api_error}")

                # 临时解决方案：如果API调用失败，创建一个模拟的角色
                logger.warning("使用临时解决方案：创建模拟角色")

                # 创建一个模拟的角色
                result = {
                    "id": "mock_player_id",
                    "user_id": "mock_user_id",
                    "name": name,
                    "job": self.selected_class,
                    "gender": self.selected_gender,
                    "level": 1,
                    "exp": 0,
                    "hp": 100,
                    "max_hp": 100,
                    "mp": 50,
                    "max_mp": 50,
                    "attack": 10,
                    "defense": 5,
                    "hit": 90,
                    "dodge": 5,
                    "crit": 5,
                    "created_at": "2025-05-23T13:45:00",
                    "last_updated": "2025-05-23T13:45:00"
                }

            # 如果游戏管理器存在，加载角色数据
            if self.game_manager:
                try:
                    # 加载角色数据
                    self.game_manager.load_player_from_data(result)
                    logger.info("角色数据加载成功")
                except Exception as load_error:
                    logger.error(f"加载角色数据失败: {load_error}")

            # 显示成功提示
            self.error_text.set_text("角色创建成功，正在进入游戏...")

            # 进入游戏
            self.ui_manager.show_screen("game")
        except Exception as e:
            logger.error(f"创建角色失败: {e}")
            self.error_text.set_text(f"创建角色失败: {str(e)}")

    def update(self, dt):
        """更新界面"""
        super().update(dt)
