import pygame
from typing import Dict, List, Tuple, Callable, Any, Optional, Union

from utils.logger import logger
from utils.resource_manager import resources

# --- Constants (Re-added) ---
DEFAULT_FONT_NAME = "chinese"
DEFAULT_FONT_SIZE_NORMAL = 22
DEFAULT_FONT_SIZE_MEDIUM = 28
DEFAULT_FONT_SIZE_LARGE = 32

BORDER_RADIUS_NORMAL = 4
BORDER_RADIUS_LARGE = 5

BORDER_WIDTH_THIN = 1
BORDER_WIDTH_NORMAL = 2

PADDING_SMALL = 2
PADDING_NORMAL = 5
PADDING_LARGE = 10

# Button Specific
BUTTON_IMAGE_V_OFFSET = 5 # Vertical offset for image when text is below
BUTTON_TEXT_BOTTOM_MARGIN = 2 # Margin between bottom of rect and text (when image present)

# Dialog Specific
DIALOG_TITLE_HEIGHT = 40
DIALOG_CLOSE_BUTTON_SIZE = 20
DIALOG_CLOSE_BUTTON_MARGIN = 10 # Margin from dialog edge to close button
DIALOG_CLOSE_BUTTON_PADDING = 5 # Padding inside close button for drawing 'X'

# Toggle Specific
TOGGLE_KNOB_PADDING = 4 # Padding around the knob inside the track
TOGGLE_ANIMATION_SPEED = 5.0

# InputBox Specific
INPUT_BOX_H_PADDING = 5 # Horizontal padding for text
INPUT_BOX_V_PADDING = 3 # Approximate vertical padding for text/cursor centering
INPUT_BOX_CURSOR_WIDTH = 2
INPUT_BOX_CURSOR_BLINK_INTERVAL = 0.5

# Dropdown Specific
DROPDOWN_OPTION_V_PADDING = 5 # Vertical padding inside each option item
DROPDOWN_TEXT_LEFT_PADDING = 10 # Left padding for text in button and options
DROPDOWN_ARROW_SIZE = 8
DROPDOWN_ARROW_RIGHT_MARGIN = 10 # Margin between right edge and arrow

# DraggableSkillIcon Specific
DRAGGABLE_ICON_IMAGE_SCALE_FACTOR = 0.6 # Scale factor for the skill image within the rect
DRAGGABLE_ICON_IMAGE_V_OFFSET = 8 # Vertical offset for the skill image
DRAGGABLE_ICON_DRAG_THRESHOLD = 5 # Pixels mouse must move to initiate drag

# Tooltip Specific
TOOLTIP_PADDING = PADDING_NORMAL # Use constant PADDING_NORMAL

class UIComponent:
    """UI组件基类"""

    def __init__(self, rect: pygame.Rect, visible: bool = True):
        """
        初始化UI组件

        参数:
            rect: 组件矩形区域
            visible: 是否可见
        """
        self.rect = rect
        self.visible = visible
        self.parent = None

    def draw(self, surface: pygame.Surface):
        """绘制组件"""
        if not self.visible:
            return

        if hasattr(self, 'image') and self.image:
            try:
                surface.blit(self.image, self.rect.topleft)
            except Exception as e:
                logger.error(f"绘制 UIComponent 图像时出错: {e}", exc_info=True)

    def update(self, dt: float):
        """更新组件状态"""
        pass

    def handle_event(self, event) -> bool:
        """处理事件"""
        return False

    def set_position(self, x: int, y: int):
        """设置组件位置"""
        self.rect.x = x
        self.rect.y = y

    def set_size(self, width: int, height: int):
        """设置组件大小"""
        self.rect.width = width
        self.rect.height = height

    def show(self):
        """显示组件"""
        self.visible = True

    def hide(self):
        """隐藏组件"""
        self.visible = False

    def toggle_visibility(self):
        """切换可见性"""
        self.visible = not self.visible

class Button(UIComponent):
    """按钮组件"""

    def __init__(self, rect: pygame.Rect, text: str,
                 colors: Dict[str, Tuple[int, int, int]],
                 font: pygame.font.Font,
                 action: Callable[[], Any] = None,
                 visible: bool = True):
        """
        初始化按钮

        参数:
            rect: 按钮矩形区域
            text: 按钮文本
            colors: 按钮颜色字典，包含 "normal", "hover", "pressed", "text" 等键
            font: 按钮文本字体
            action: 点击按钮时执行的回调函数
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.text = text
        self.font = font
        self.action = action
        self.on_right_click = None  # 添加右键点击处理函数
        self.active = True  # 添加active属性，默认为True
        self.image = None   # 添加图片属性，默认为None

        # 设置默认颜色
        self.colors = {
            "normal": (60, 60, 100),
            "hover": (80, 80, 140),
            "pressed": (40, 40, 80),
            "text": (255, 255, 255),
            "inactive": (100, 100, 100),  # 不可点击状态的颜色
            "inactive_text": (150, 150, 150)  # 不可点击状态的文本颜色
        }

        # 更新颜色
        if colors:
            self.colors.update(colors)

        # 状态
        self.hovered = False
        self.hover_time = 0
        self.pressed = False

        # 预渲染文本
        self.text_surface = self.font.render(self.text, True, self.colors["text"])

        # 强制渲染标志
        self._force_active_render = False

        # 记录按钮ID，用于调试
        self._debug_id = f"Button_{id(self) % 1000000}"  # 取ID的后6位数字

    def debug_info(self):
        """返回按钮的调试信息"""
        return {
            "id": self._debug_id,
            "text": self.text,
            "rect": self.rect,
            "visible": self.visible,
            "active": self.active,
            "force_active": self._force_active_render,
            "has_action": self.action is not None
        }

    def __str__(self):
        """返回按钮的字符串表示"""
        return f"Button('{self.text}', visible={self.visible}, active={self.active}, rect={self.rect})"

    def set_active(self, active: bool):
        """设置按钮是否激活

        参数:
            active: 是否激活
        """
        if self.active != active:
            self.active = active
            logger.debug(f"按钮 '{self.text}' 激活状态更改为: {self.active}")

    def force_active_render(self, force=True):
        """强制按钮以激活状态渲染

        参数:
            force: 是否强制激活渲染
        """
        self._force_active_render = force
        logger.debug(f"按钮 '{self.text}' 强制激活渲染状态: {self._force_active_render}")

    def draw(self, surface: pygame.Surface):
        """绘制按钮"""
        if not self.visible:
            return

        # 按钮状态和透明度动画的状态
        current_state = "normal"

        if self.pressed:
            current_state = "pressed"
        elif self.hovered and self.active:
            current_state = "hover"

        # 绘制按钮背景
        if not self.active and not self._force_active_render:
            # 如果按钮不可用，使用inactive颜色
            bg_color = self.colors.get("inactive", (100, 100, 100))
            border_color = self.colors.get("inactive_border", self.colors.get("border", (40, 40, 80)))
        else:
            # 使用当前状态的颜色
            bg_color = self.colors.get(current_state, (60, 60, 100))
            border_color = self.colors.get("border", (40, 40, 80))

        # 绘制圆角矩形背景
        pygame.draw.rect(surface, bg_color, self.rect, border_radius=4)

        # 绘制边框(如果有设置)
        border_width = getattr(self, "border_width", 1)
        if border_width > 0:
            pygame.draw.rect(
                surface,
                border_color,
                self.rect,
                width=border_width,
                border_radius=4
            )

        # 绘制图片(如果有)
        if self.image and isinstance(self.image, pygame.Surface):
            # 计算图片的居中位置
            image_rect = self.image.get_rect(center=(self.rect.centerx, self.rect.centery - 5))
            surface.blit(self.image, image_rect)

            # 如果有图片，文本显示在底部
            if self.text:
                # 获取文本颜色
                if not self.active and not self._force_active_render:
                    text_color = self.colors.get("inactive_text", (150, 150, 150))
                else:
                    text_color = self.colors.get("text", (255, 255, 255))

                # 分割多行文本
                lines = self.text.split('\n')
                if len(lines) == 1:
                    # 只有一行，直接渲染在底部
                    self.text_surface = self.font.render(self.text, True, text_color)
                    text_rect = self.text_surface.get_rect(centerx=self.rect.centerx,
                                                         bottom=self.rect.bottom - 2)
                    surface.blit(self.text_surface, text_rect)
                else:
                    # 多行文本，依次渲染
                    line_height = self.font.get_height()
                    total_height = line_height * len(lines)
                    start_y = self.rect.bottom - total_height - 2

                    for i, line in enumerate(lines):
                        line_surface = self.font.render(line, True, text_color)
                        line_rect = line_surface.get_rect(centerx=self.rect.centerx,
                                                        y=start_y + i * line_height)
                        surface.blit(line_surface, line_rect)
        else:
            # 没有图片，居中显示文本
            if self.text:
                # 获取文本颜色
                if not self.active and not self._force_active_render:
                    text_color = self.colors.get("inactive_text", (150, 150, 150))
                else:
                    text_color = self.colors.get("text", (255, 255, 255))

                # 分割多行文本
                lines = self.text.split('\n')
                if len(lines) == 1:
                    # 只有一行，居中显示
                    self.text_surface = self.font.render(self.text, True, text_color)
                    text_rect = self.text_surface.get_rect(center=self.rect.center)
                    surface.blit(self.text_surface, text_rect)
                else:
                    # 多行文本，垂直居中整体文本
                    line_height = self.font.get_height()
                    total_height = line_height * len(lines)
                    start_y = self.rect.centery - total_height / 2

                    for i, line in enumerate(lines):
                        line_surface = self.font.render(line, True, text_color)
                        line_rect = line_surface.get_rect(centerx=self.rect.centerx,
                                                        y=start_y + i * line_height)
                        surface.blit(line_surface, line_rect)

    def update(self, dt: float):
        """更新按钮状态"""
        # 悬停动画
        if self.hovered:
            self.hover_time += dt
        else:
            self.hover_time = 0

    def handle_event(self, event) -> bool:
        """处理事件"""
        if not self.visible:
            return False

        if event.type == pygame.MOUSEMOTION:
            # 鼠标移动事件，更新hover状态
            mouse_pos = pygame.mouse.get_pos()
            self.hovered = self.rect.collidepoint(mouse_pos)
            return self.hovered

        elif event.type == pygame.MOUSEBUTTONDOWN:
            # 鼠标按下事件
            mouse_pos = pygame.mouse.get_pos()
            if self.rect.collidepoint(mouse_pos):
                if event.button == 1:  # 左键点击
                    self.pressed = True
                    return True
                elif event.button == 3 and self.on_right_click is not None:  # 右键点击
                    # 直接调用右键点击函数
                    try:
                        self.on_right_click()
                    except Exception as e:
                        logger.error(f"右键点击回调函数出错: 按钮[{self.text}] - {e.__class__.__name__}: {e}")
                    return True

        elif event.type == pygame.MOUSEBUTTONUP:
            # 鼠标释放事件
            if event.button == 1 and self.pressed:  # 仅处理左键
                self.pressed = False
                mouse_pos = pygame.mouse.get_pos()
                if self.rect.collidepoint(mouse_pos) and self.active and self.action:
                    try:
                        self.action()
                    except Exception as e:
                        logger.error(f"按钮回调函数出错: 按钮[{self.text}] - {e.__class__.__name__}: {e}")
                return True

        return False

    def set_text(self, text: str):
        """设置按钮文本

        参数:
            text: 新的按钮文本
        """
        if self.text != text:
            self.text = text
            # 重新生成文本表面
            self.text_surface = self.font.render(self.text, True, self.colors["text"])

    def set_text_color(self, color):
        """设置按钮文本颜色

        参数:
            color: 新的文本颜色 (R,G,B)元组
        """
        if color:
            self.colors["text"] = color
            # 重新生成文本表面
            self.text_surface = self.font.render(self.text, True, self.colors["text"])

    @property
    def text_color(self):
        """获取按钮文本颜色"""
        return self.colors.get("text", (255, 255, 255))

    @text_color.setter
    def text_color(self, color):
        """设置按钮文本颜色"""
        self.set_text_color(color)

    def get_text(self) -> str:
        """获取按钮文本

        返回:
            str: 按钮文本
        """
        return self.text

class Text(UIComponent):
    """文本组件"""

    def __init__(self, rect: pygame.Rect, text: str,
                 font: pygame.font.Font,
                 color: Tuple[int, int, int] = (0, 0, 0),
                 align: str = "left",
                 visible: bool = True):
        """
        初始化文本组件

        参数:
            rect: 文本矩形区域
            text: 文本内容
            font: 字体
            color: 颜色
            align: 对齐方式 ('left', 'center', 'right')
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.text = text
        self.font = font
        self.color = color
        self.align = align
        self._rendered_lines = []  # 存储多行渲染结果
        self._render_text()

    def _render_text(self):
        """渲染文本，支持多行"""
        self._rendered_lines = []

        # 检查文本是否为空
        if not self.text:
            return

        # 按换行符分割文本
        lines = self.text.split('\n')

        # 渲染每一行
        for line in lines:
            if line.strip():  # 跳过空行
                rendered_line = self.font.render(line, True, self.color)
                self._rendered_lines.append(rendered_line)

    def draw(self, surface: pygame.Surface):
        """绘制多行文本"""
        if not self.visible or not self._rendered_lines:
            return

        # 计算行间距（使用字体高度）
        line_height = self.font.get_height()

        # 计算所有行的总高度
        total_height = line_height * len(self._rendered_lines)

        # 确定绘制起始Y坐标，根据对齐方式
        if self.align == "center":
            start_y = self.rect.y + (self.rect.height - total_height) // 2
        else:
            start_y = self.rect.y

        # 绘制每一行
        for i, line_surface in enumerate(self._rendered_lines):
            line_rect = line_surface.get_rect()
            y_position = start_y + i * line_height

            # 根据对齐方式设置水平位置
            if self.align == "left":
                line_rect.left = self.rect.left
            elif self.align == "center":
                line_rect.centerx = self.rect.centerx
            elif self.align == "right":
                line_rect.right = self.rect.right

            line_rect.y = y_position
            surface.blit(line_surface, line_rect)

    def set_text(self, text: str):
        """设置文本内容"""
        if self.text != text:
            self.text = text
            self._render_text()

    def set_color(self, color: Tuple[int, int, int]):
        """设置文本颜色"""
        if self.color != color:
            self.color = color
            self._render_text()

    def set_font(self, font: pygame.font.Font):
        """设置字体"""
        if self.font != font:
            self.font = font
            self._render_text()

class InputField(UIComponent):
    """输入字段组件"""

    def __init__(self, rect: pygame.Rect,
                 text: str = "",
                 font: pygame.font.Font = None,
                 max_length: int = 100,
                 password: bool = False,
                 visible: bool = True,
                 next_input_field=None):
        """
        初始化输入字段

        参数:
            rect: 输入字段矩形区域
            text: 初始文本
            font: 字体
            max_length: 最大文本长度
            password: 是否为密码输入
            visible: 是否可见
            next_input_field: Tab键跳转到的下一个输入字段
        """
        super().__init__(rect, visible)
        self.text = text
        self.font = font
        self.max_length = max_length
        self.password = password
        self.next_input_field = next_input_field  # Tab键跳转到的下一个输入字段

        # 输入字段状态
        self.active = False
        self.cursor_visible = True
        self.cursor_blink_time = 0
        self.cursor_blink_interval = 0.5  # 光标闪烁间隔（秒）

        # 颜色
        self.colors = {
            "inactive_bg": (220, 220, 220),
            "active_bg": (255, 255, 255),
            "border_inactive": (150, 150, 150),
            "border_active": (100, 100, 200),
            "text": (0, 0, 0),
            "cursor": (0, 0, 0)
        }

        # 渲染文本
        self._render_text()

    def _render_text(self):
        """渲染文本"""
        if not self.font:
            return

        display_text = self.text
        if self.password:
            display_text = "*" * len(self.text)

        self.text_surface = self.font.render(display_text, True, self.colors["text"])

    def draw(self, surface: pygame.Surface):
        """绘制输入字段"""
        if not self.visible:
            return

        # 绘制背景
        bg_color = self.colors["active_bg"] if self.active else self.colors["inactive_bg"]
        border_color = self.colors["border_active"] if self.active else self.colors["border_inactive"]

        pygame.draw.rect(surface, bg_color, self.rect, border_radius=4)
        pygame.draw.rect(surface, border_color, self.rect, width=2, border_radius=4)

        # 绘制文本
        if self.font and self.text:
            # 计算文本位置
            text_x = self.rect.x + 5  # 左边距
            text_y = self.rect.y + (self.rect.height - self.text_surface.get_height()) // 2  # 垂直居中

            # 绘制文本
            surface.blit(self.text_surface, (text_x, text_y))

            # 如果激活，绘制光标
            if self.active and self.cursor_visible:
                display_text = self.text
                if self.password:
                    display_text = "*" * len(self.text)

                cursor_x = text_x + self.font.size(display_text)[0]
                cursor_y = text_y
                cursor_height = self.text_surface.get_height()

                pygame.draw.line(
                    surface,
                    self.colors["cursor"],
                    (cursor_x, cursor_y),
                    (cursor_x, cursor_y + cursor_height),
                    2
                )

    def update(self, dt: float):
        """更新输入字段状态"""
        if self.active:
            # 更新光标闪烁
            self.cursor_blink_time += dt
            if self.cursor_blink_time >= self.cursor_blink_interval:
                self.cursor_blink_time = 0
                self.cursor_visible = not self.cursor_visible

    def handle_event(self, event) -> bool:
        """处理事件"""
        if not self.visible:
            return False

        if event.type == pygame.MOUSEBUTTONDOWN:
            # 鼠标点击事件
            if event.button == 1:  # 左键点击
                # 检查是否点击在输入字段上
                if self.rect.collidepoint(event.pos):
                    self.active = True
                else:
                    self.active = False

                # 重置光标状态
                self.cursor_visible = True
                self.cursor_blink_time = 0

                return self.active

        elif event.type == pygame.KEYDOWN and self.active:
            # 键盘事件
            if event.key == pygame.K_BACKSPACE:
                # 删除最后一个字符
                self.text = self.text[:-1]
            elif event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                # 回车键，取消激活状态
                self.active = False
            elif event.key == pygame.K_ESCAPE:
                # ESC键，取消激活状态
                self.active = False
            elif event.key == pygame.K_TAB:
                # Tab键，跳转到下一个输入字段
                if self.next_input_field:
                    self.active = False
                    self.next_input_field.active = True
                    # 重置下一个输入字段的光标状态
                    self.next_input_field.cursor_visible = True
                    self.next_input_field.cursor_blink_time = 0
            elif event.unicode and len(self.text) < self.max_length:
                # 添加字符
                self.text += event.unicode

            # 重新渲染文本
            self._render_text()

            # 重置光标状态
            self.cursor_visible = True
            self.cursor_blink_time = 0

            return True

        return False

class Panel(UIComponent):
    """面板组件，可包含其他组件"""

    def __init__(self, rect: pygame.Rect,
                 color: Tuple[int, int, int, int] = (200, 200, 200, 255),
                 border_color: Tuple[int, int, int, int] = (100, 100, 100, 255),
                 border_width: int = 2,
                 visible: bool = True):
        """
        初始化面板

        参数:
            rect: 面板矩形区域
            color: 背景颜色 (r, g, b, a)，a为透明度，0-255，0为完全透明
            border_color: 边框颜色 (r, g, b, a)
            border_width: 边框宽度
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.color = color
        self.border_color = border_color
        self.border_width = border_width
        self.components = []

    def draw(self, surface: pygame.Surface):
        """绘制面板及其子组件"""
        if not self.visible:
            return

        # 绘制面板背景，支持透明度
        if len(self.color) == 4 and self.color[3] > 0:
            # 创建临时surface用于透明绘制
            temp_surface = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
            pygame.draw.rect(temp_surface, self.color,
                            pygame.Rect(0, 0, self.rect.width, self.rect.height),
                            border_radius=5)
            surface.blit(temp_surface, self.rect.topleft)
        elif len(self.color) == 3 or (len(self.color) == 4 and self.color[3] == 255):
            # 不透明背景，直接绘制
            pygame.draw.rect(surface, self.color[:3], self.rect, border_radius=5)

        # 绘制边框，支持透明度
        if self.border_width > 0:
            if len(self.border_color) == 4 and self.border_color[3] < 255:
                # 创建临时surface用于透明绘制
                temp_surface = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
                pygame.draw.rect(temp_surface, self.border_color,
                                pygame.Rect(0, 0, self.rect.width, self.rect.height),
                                width=self.border_width, border_radius=5)
                surface.blit(temp_surface, self.rect.topleft)
            else:
                # 不透明边框，直接绘制
                border_color = self.border_color[:3] if len(self.border_color) == 4 else self.border_color
                pygame.draw.rect(surface, border_color, self.rect,
                                width=self.border_width, border_radius=5)

        # 绘制子组件 (应用坐标偏移)
        panel_offset = self.rect.topleft # Panel 的左上角绝对坐标
        original_rects = {} # 存储原始相对坐标
        try:
            for component in self.components:
                if not component.visible: # 跳过不可见的子组件
                    continue
                # 存储原始相对坐标
                original_rects[component] = component.rect.copy()
                # 计算并设置绝对坐标用于绘制
                component.rect.move_ip(panel_offset)
                # 使用绝对坐标绘制子组件
                component.draw(surface)
        finally:
            # 恢复所有子组件的相对坐标
            for component, original_rect in original_rects.items():
                component.rect = original_rect

    def update(self, dt: float):
        """更新面板及其子组件"""
        if not self.visible:
            return

        for component in self.components:
            component.update(dt)

    def handle_event(self, event) -> bool:
        """处理面板及其子组件的事件"""
        if not self.visible:
            return False

        panel_offset = self.rect.topleft # Panel 的左上角绝对坐标
        original_rects = {} # 存储原始相对坐标
        event_handled = False
        try:
            # 检查是否有子组件处理了事件 (从上层组件开始检查)
            for component in reversed(self.components):
                if not component.visible: # 跳过不可见的子组件
                    continue

                # 存储原始相对坐标
                original_rects[component] = component.rect.copy()
                # 计算并设置绝对坐标用于事件处理
                component.rect.move_ip(panel_offset)

                # 使用绝对坐标处理事件
                if component.handle_event(event):
                    event_handled = True
                    # 注意：一旦事件被处理，应该立即退出循环并恢复坐标
                    break # 退出循环

        finally:
            # 恢复所有子组件的相对坐标 (包括处理了事件的和未处理的)
            for component, original_rect in original_rects.items():
                component.rect = original_rect

        # 如果子组件处理了事件，则返回 True
        if event_handled:
            return True

        # 如果没有子组件处理事件，面板自身不处理额外事件
        return False

    def add_component(self, component: UIComponent):
        """添加子组件"""
        component.parent = self
        self.components.append(component)

    def remove_component(self, component: UIComponent):
        """移除子组件"""
        if component in self.components:
            component.parent = None
            self.components.remove(component)

    def clear_components(self):
        """清除所有子组件"""
        for component in self.components:
            component.parent = None
        self.components.clear()

class ScrollableList(Panel):
    """可滚动列表组件 (优化版 - 复用组件)"""

    def __init__(self, rect: pygame.Rect,
                 item_height: int = 30,
                 color: Optional[Tuple[int, int, int, int]] = (200, 200, 200, 255), # Use Panel's updated signature
                 border_color: Optional[Tuple[int, int, int, int]] = (100, 100, 100, 255), # Use Panel's updated signature
                 border_width: int = BORDER_WIDTH_NORMAL,
                 border_radius: int = BORDER_RADIUS_LARGE,
                 item_font: Optional[pygame.font.Font] = None,
                 item_colors: Optional[Dict[str, Tuple[int, int, int]]] = None,
                 visible: bool = True):
        """
        初始化可滚动列表 (优化版)
        """
        # Call Panel's init without border_radius as it's not part of Panel's signature
        super().__init__(rect, color, border_color, border_width, visible)
        # Store border_radius locally if needed for ScrollableList drawing (if Panel doesn't handle it)
        self.border_radius = border_radius
        self.item_height = max(1, item_height) # Ensure positive height
        self.scroll_offset = 0
        self.items: List[Tuple[str, Any]] = [] # List of (text, data)
        self._item_components_pool: List[Button] = [] # Pool of reusable Button components
        self.visible_item_indices: List[int] = [] # Indices of items currently displayed in the pool components

        # Item appearance configuration
        # Get font path from resources and create font object
        default_font_path = resources.get_font_path(DEFAULT_FONT_NAME)
        if default_font_path:
            self.item_font = item_font or pygame.font.Font(default_font_path, DEFAULT_FONT_SIZE_NORMAL)
        else:
            # Fallback if font path not found
            logger.warning(f"Default font '{DEFAULT_FONT_NAME}' path not found, using system default.")
            self.item_font = item_font or pygame.font.SysFont(None, DEFAULT_FONT_SIZE_NORMAL)

        self.item_colors = item_colors or {
            "normal": (30, 30, 50), "hover": (40, 40, 60), "pressed": (20, 20, 40),
            "text": (200, 200, 200), "border": (60, 60, 80)
        }

        # Dragging state for scrolling the list itself
        self.dragging = False
        self.drag_start_y = 0
        self.drag_start_scroll_offset = 0

        # Click callback
        self._on_item_clicked_callback: Optional[Callable[[Any], None]] = None

        self._recalculate_max_visible()
        self._update_visible_components() # Initial population if needed

    def _recalculate_max_visible(self):
         """根据当前尺寸和项目高度计算最大可见项数"""
         content_height = self.rect.height - (self.border_width * 2 if self.border_color else 0)
         self.max_items_visible = max(1, content_height // self.item_height)
         self.scroll_offset = max(0, min(self.scroll_offset, self._get_max_scroll_offset()))

    def _get_max_scroll_offset(self) -> int:
        """计算有效的最大滚动偏移量"""
        return max(0, len(self.items) - self.max_items_visible)

    def set_on_item_clicked(self, callback: Callable[[Any], None]):
        """设置列表项点击回调函数"""
        self._on_item_clicked_callback = callback

    def add_item(self, item_text: str, item_data: Any = None):
        """添加单个列表项"""
        self.items.append((item_text, item_data))
        self._recalculate_max_visible() # Max scroll offset might change
        self._update_visible_components() # Update display

    def add_items(self, items: List[Tuple[str, Any]]):
         """添加多个列表项"""
         self.items.extend(items)
         self._recalculate_max_visible()
         self._update_visible_components()

    def clear_items(self):
        """清除所有列表项"""
        self.items.clear()
        self.scroll_offset = 0
        self.visible_item_indices = []
        # Hide all components in the pool and remove them from panel's drawing/event handling
        for component in self._item_components_pool:
            component.hide()
            if component in self.components: # Check if it was added before
                 self.remove_component(component) # Use Panel's remove
        logger.debug("ScrollableList cleared items and hid pooled components.")
        # _update_visible_components() will be called implicitly if needed later

    def scroll_to_index(self, index: int):
         """滚动列表以使指定索引的项可见 (尽量置顶)"""
         target_offset = max(0, min(index, self._get_max_scroll_offset()))
         if self.scroll_offset != target_offset:
             self.scroll_offset = target_offset
             self._update_visible_components()

    def scroll_to_bottom(self):
         """滚动到底部"""
         self.scroll_to_index(self._get_max_scroll_offset())

    def _update_visible_components(self):
        """(核心优化) 更新可见列表项组件，复用现有按钮对象"""
        logger.debug(f"Updating visible components: offset={self.scroll_offset}, pool_size={len(self._item_components_pool)}, total_items={len(self.items)}")

        # 1. Determine which item indices should be visible
        self.visible_item_indices = list(range(
            self.scroll_offset,
            min(self.scroll_offset + self.max_items_visible, len(self.items))
        ))

        active_components_count = len(self.visible_item_indices)
        logger.debug(f"Visible indices: {self.visible_item_indices}")

        # 2. Ensure component pool is large enough
        while len(self._item_components_pool) < active_components_count:
            logger.debug(f"Expanding component pool (current size {len(self._item_components_pool)})")
            # Create a placeholder rect, will be updated
            placeholder_rect = pygame.Rect(0,0,1,1)
            new_component = Button(
                placeholder_rect, "", self.item_colors.copy(), self.item_font,
                None, # Action set below
                visible=False # Start hidden
            )
            self._item_components_pool.append(new_component)
            # DO NOT add to self.components here, add only when activated

        # 3. Update/Activate components for visible items
        current_components = [] # Track components actually used this update
        for i, item_index in enumerate(self.visible_item_indices):
            component = self._item_components_pool[i]
            item_text, item_data = self.items[item_index]

            # Calculate position for this component
            item_y = self.rect.y + i * self.item_height + (self.border_width if self.border_color else 0)
            item_rect = pygame.Rect(
                self.rect.x + (self.border_width if self.border_color else 0),
                item_y,
                self.rect.width - (2 * self.border_width if self.border_color else 0),
                self.item_height
            )

            # Update the reused component
            component.rect = item_rect
            component.set_text(item_text)
            # Crucial: Use lambda default arg capture for item_data
            component.action = lambda data=item_data: self._handle_item_click(data)
            component.colors.update(self.item_colors) # Ensure colors are reset/updated if needed
            component.set_active(True)
            component.show()
            current_components.append(component)

            # Ensure this component is in the Panel's component list for drawing/events
            if component not in self.components:
                 self.add_component(component)
                 logger.debug(f"Adding component {i} (item {item_index}) to Panel list")

        # 4. Deactivate/Hide unused components in the pool
        for i in range(active_components_count, len(self._item_components_pool)):
            component = self._item_components_pool[i]
            if component.visible:
                 logger.debug(f"Hiding unused pool component {i}")
                 component.hide()
                 # Remove from Panel's list to stop drawing/event handling
                 if component in self.components:
                      self.remove_component(component)
                      logger.debug(f"Removing component {i} from Panel list")

        logger.debug(f"Update complete. Active components in Panel: {len(self.components)}")

    def _handle_item_click(self, item_data: Any):
        """处理列表项点击事件，调用回调"""
        logger.debug(f"List item clicked: Data={item_data}")
        if self._on_item_clicked_callback:
            try:
                self._on_item_clicked_callback(item_data)
            except Exception as e:
                logger.error(f"Error in list item click callback: {e}", exc_info=True)
        else:
            logger.warning("List item clicked, but no callback set.")

    def handle_event(self, event) -> bool:
        """处理列表事件 (滚动和子项点击)"""
        if not self.visible: return False

        # 1. Let active child components (Buttons) handle events first.
        # Panel's handle_event checks its self.components list. We manage this list in _update_visible_components.
        if super().handle_event(event): # Checks components in self.components (reverse order)
             return True # Event handled by a child button

        # 2. If no child handled it, handle list's own scrolling events.
        mouse_pos = pygame.mouse.get_pos()
        collided = self.rect.collidepoint(mouse_pos)

        # Mouse Wheel Scrolling
        if event.type == pygame.MOUSEWHEEL and collided:
            scroll_change = -event.y # Standard wheel direction
            new_offset = max(0, min(self.scroll_offset + scroll_change, self._get_max_scroll_offset()))
            if new_offset != self.scroll_offset:
                self.scroll_offset = new_offset
                self._update_visible_components()
                return True # Consumed scroll event

        # Drag Scrolling (only if clicking the list background, not a button)
        elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1 and collided:
             # This click was not handled by a child button (checked by super().handle_event above)
             # So, it must be on the list background - start dragging.
             self.dragging = True
             self.drag_start_y = mouse_pos[1]
             self.drag_start_scroll_offset = self.scroll_offset
             logger.debug("Started dragging scroll list background")
             return True # Start drag

        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
             if self.dragging:
                 self.dragging = False
                 logger.debug("Stopped dragging scroll list background")
                 return True # End drag

        elif event.type == pygame.MOUSEMOTION and self.dragging:
             pixels_dragged = mouse_pos[1] - self.drag_start_y
             # Map pixel drag to item scroll (adjust sensitivity here if needed)
             items_to_scroll = pixels_dragged // self.item_height

             # Calculate new offset based on drag start and item distance
             # Dragging down scrolls content up -> increases offset -> need negative items_to_scroll
             new_offset = max(0, min(self.drag_start_scroll_offset - items_to_scroll, self._get_max_scroll_offset()))

             if new_offset != self.scroll_offset:
                 self.scroll_offset = new_offset
                 self._update_visible_components()
             return True # Consuming motion event while dragging list

        return False

    def set_item_height(self, height: int):
        """设置列表项高度并重新计算布局"""
        new_height = max(1, height)
        if new_height != self.item_height:
            self.item_height = new_height
            self._recalculate_max_visible()
            # Pool components rects will be updated in the next call
            self._update_visible_components()

    def set_item_color(self, index: int, colors: Dict[str, Tuple[int, int, int]]):
        """为特定索引的项设置颜色 (如果当前可见)"""
        if 0 <= index < len(self.items):
             if index in self.visible_item_indices:
                 try:
                     # Find the pool component currently displaying this item index
                     pool_idx = self.visible_item_indices.index(index)
                     if 0 <= pool_idx < len(self._item_components_pool):
                         component = self._item_components_pool[pool_idx]
                         component.colors.update(colors)
                         component._render_text_surface() # Re-render text for color change
                 except (ValueError, IndexError) as e:
                     logger.error(f"Error finding component for visible item index {index}: {e}")
             else:
                 logger.debug(f"Cannot set color for item {index}, not currently visible.")
        else:
             logger.warning(f"Invalid item index {index} for set_item_color.")

class ProgressBar(UIComponent):
    """进度条组件"""

    def __init__(self, rect: pygame.Rect,
                 value: float = 0.0,
                 max_value: float = 100.0,
                 colors: Dict[str, Tuple[int, int, int]] = None,
                 visible: bool = True):
        """
        初始化进度条

        参数:
            rect: 进度条矩形区域
            value: 当前值
            max_value: 最大值
            colors: 颜色配置，包含 'background', 'fill', 'border'
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.value = value
        self.max_value = max_value
        self.colors = colors or {
            "background": (50, 50, 50),
            "fill": (0, 255, 0),
            "border": (200, 200, 200)
        }

    def draw(self, surface: pygame.Surface):
        """绘制进度条"""
        if not self.visible:
            return

        # 绘制背景
        pygame.draw.rect(surface, self.colors["background"], self.rect, border_radius=3)

        # 计算填充宽度
        fill_width = int((self.value / self.max_value) * self.rect.width)
        if fill_width > 0:
            fill_rect = pygame.Rect(self.rect.x, self.rect.y, fill_width, self.rect.height)
            pygame.draw.rect(surface, self.colors["fill"], fill_rect, border_radius=3)

        # 绘制边框
        pygame.draw.rect(surface, self.colors["border"], self.rect, width=1, border_radius=3)

    def set_value(self, value: float):
        """设置当前值"""
        self.value = max(0, min(value, self.max_value))

    def set_max_value(self, max_value: float):
        """设置最大值"""
        if max_value > 0:
            self.max_value = max_value
            self.value = min(self.value, max_value)

class ImageButton(Button):
    """图像按钮组件"""

    def __init__(self, rect: pygame.Rect,
                 image_path: str,
                 hover_image_path: Optional[str] = None,
                 pressed_image_path: Optional[str] = None,
                 action: Callable[[], Any] = None,
                 visible: bool = True):
        """
        初始化图像按钮

        参数:
            rect: 按钮矩形区域
            image_path: 正常状态图像路径
            hover_image_path: 悬停状态图像路径，为None时使用正常图像
            pressed_image_path: 按下状态图像路径，为None时使用悬停图像
            action: 点击按钮执行的动作
            visible: 按钮是否可见
        """
        super().__init__(rect, "", {}, None, action, visible)

        # 加载图像
        self.normal_image = resources.load_image(image_path)
        self.hover_image = resources.load_image(hover_image_path) if hover_image_path else self.normal_image
        self.pressed_image = resources.load_image(pressed_image_path) if pressed_image_path else self.hover_image

        # 调整图像大小
        if self.normal_image:
            self.normal_image = pygame.transform.scale(self.normal_image, (rect.width, rect.height))
        if self.hover_image:
            self.hover_image = pygame.transform.scale(self.hover_image, (rect.width, rect.height))
        if self.pressed_image:
            self.pressed_image = pygame.transform.scale(self.pressed_image, (rect.width, rect.height))

    def draw(self, surface: pygame.Surface):
        """绘制图像按钮"""
        if not self.visible:
            return

        # 根据状态选择图像
        if self.pressed and self.pressed_image:
            image = self.pressed_image
        elif self.hovered and self.hover_image:
            image = self.hover_image
        elif self.normal_image:
            image = self.normal_image
        else:
            # 如果没有图像，fallback到普通按钮绘制
            super().draw(surface)
            return

        # 绘制图像
        surface.blit(image, self.rect.topleft)

class Tooltip(UIComponent):
    """工具提示组件"""

    def __init__(self, text: str,
                 font: pygame.font.Font,
                 padding: int = 5,
                 colors: Dict[str, Tuple[int, int, int]] = None,
                 visible: bool = False):
        """
        初始化工具提示

        参数:
            text: 提示文本
            font: 字体
            padding: 内边距
            colors: 颜色配置，包含 'background', 'text', 'border'
            visible: 是否可见
        """
        # 初始化一个临时矩形，稍后更新
        super().__init__(pygame.Rect(0, 0, 10, 10), visible)

        self.text = text
        self.font = font
        self.padding = padding
        self.colors = colors or {
            "background": (240, 240, 200),
            "text": (0, 0, 0),
            "border": (100, 100, 100)
        }

        # 计算正确的尺寸
        self._update_size()

    def _update_size(self):
        """更新工具提示尺寸"""
        text_surf = self.font.render(self.text, True, self.colors["text"])
        text_size = text_surf.get_size()

        self.rect.width = text_size[0] + self.padding * 2
        self.rect.height = text_size[1] + self.padding * 2

    def draw(self, surface: pygame.Surface):
        """绘制工具提示"""
        if not self.visible:
            return

        # 绘制背景
        pygame.draw.rect(surface, self.colors["background"], self.rect, border_radius=3)

        # 绘制边框
        pygame.draw.rect(surface, self.colors["border"], self.rect, width=1, border_radius=3)

        # 绘制文本
        text_surf = self.font.render(self.text, True, self.colors["text"])
        text_rect = text_surf.get_rect(center=self.rect.center)
        surface.blit(text_surf, text_rect)

    def set_text(self, text: str):
        """设置提示文本"""
        if self.text != text:
            self.text = text
            self._update_size()

    def set_position(self, x: int, y: int):
        """设置工具提示位置"""
        self.rect.x = x
        self.rect.y = y

        # 确保工具提示在屏幕内
        screen_size = pygame.display.get_surface().get_size()
        if self.rect.right > screen_size[0]:
            self.rect.right = screen_size[0]
        if self.rect.bottom > screen_size[1]:
            self.rect.bottom = screen_size[1]

class InputBox(UIComponent):
    """文本输入框组件"""

    def __init__(self, rect: pygame.Rect,
                 initial_text: str = "",
                 callback: Callable[[str], None] = None,
                 max_length: int = 20,
                 font: pygame.font.Font = None,
                 text_color: Tuple[int, int, int] = (255, 255, 255),
                 active_color: Tuple[int, int, int] = (100, 100, 200),
                 inactive_color: Tuple[int, int, int] = (70, 70, 70),
                 background_color: Tuple[int, int, int] = (30, 30, 30),
                 visible: bool = True):
        """
        初始化文本输入框

        参数:
            rect: 输入框矩形区域
            initial_text: 初始文本
            callback: 文本变化时的回调函数
            max_length: 最大文本长度
            font: 字体对象
            text_color: 文本颜色
            active_color: 激活状态边框颜色
            inactive_color: 未激活状态边框颜色
            background_color: 背景颜色
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.text = initial_text
        self.callback = callback
        self.max_length = max_length
        self.font = font
        self.text_color = text_color
        self.active_color = active_color
        self.inactive_color = inactive_color
        self.background_color = background_color

        self.active = False
        self.cursor_visible = True
        self.cursor_timer = 0
        self.cursor_blink_time = 0.5  # 光标闪烁间隔(秒)

    def draw(self, surface: pygame.Surface):
        """绘制输入框"""
        if not self.visible:
            return

        # 绘制输入框背景
        pygame.draw.rect(surface, self.background_color, self.rect, border_radius=3)

        # 绘制输入框边框
        border_color = self.active_color if self.active else self.inactive_color
        pygame.draw.rect(surface, border_color, self.rect, width=2, border_radius=3)

        # 绘制文本
        text_surface = self.font.render(self.text, True, self.text_color)
        # 确保文本不超出输入框
        text_width = min(text_surface.get_width(), self.rect.width - 10)
        text_height = min(text_surface.get_height(), self.rect.height - 6)
        # 创建裁剪后的文本表面
        clipped_text = pygame.Surface((text_width, text_height), pygame.SRCALPHA)
        clipped_text.blit(text_surface, (0, 0), (0, 0, text_width, text_height))
        # 绘制文本
        surface.blit(clipped_text, (self.rect.x + 5, self.rect.y + (self.rect.height - text_height) // 2))

        # 如果激活且光标可见，绘制光标
        if self.active and self.cursor_visible:
            cursor_pos = self.rect.x + 5 + text_width
            pygame.draw.line(
                surface,
                self.text_color,
                (cursor_pos, self.rect.y + 5),
                (cursor_pos, self.rect.y + self.rect.height - 5),
                2
            )

    def update(self, dt: float):
        """更新输入框状态"""
        if self.active:
            # 更新光标闪烁
            self.cursor_timer += dt
            if self.cursor_timer >= self.cursor_blink_time:
                self.cursor_timer = 0
                self.cursor_visible = not self.cursor_visible

    def handle_event(self, event) -> bool:
        """处理输入框事件"""
        if not self.visible:
            return False

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            # 点击事件
            if self.rect.collidepoint(event.pos):
                self.active = True
                return True
            else:
                self.active = False
                return False

        # 处理文本输入事件（用于中文等复杂输入法）
        if event.type == pygame.TEXTINPUT and self.active:
            # 检查是否已达到最大长度
            if len(self.text) < self.max_length:
                self.text += event.text
                if self.callback:
                    self.callback(self.text)
                return True
            return False

        if event.type == pygame.KEYDOWN and self.active:
            if event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                # 回车键，确认输入
                self.active = False
                if self.callback:
                    self.callback(self.text)
                return True

            elif event.key == pygame.K_BACKSPACE:
                # 退格键，删除一个字符
                self.text = self.text[:-1]
                if self.callback:
                    self.callback(self.text)
                return True

            elif event.key == pygame.K_ESCAPE:
                # ESC键，取消输入
                self.active = False
                return True

            # 注意：以下部分只处理非IME输入法的单字符输入
            # 中文等复杂输入法通过TEXTINPUT事件处理
            elif event.unicode and event.unicode.isprintable():
                # 普通字符输入
                # 检查是否已达到最大长度
                if len(self.text) < self.max_length:
                    # 获取用户输入的字符
                    char = event.unicode

                    # 添加字符
                    self.text += char
                    if self.callback:
                        self.callback(self.text)
                    return True

        return False

    def set_text(self, text: str):
        """设置输入框文本"""
        self.text = text[:self.max_length]  # 确保不超过最大长度
        if self.callback:
            self.callback(self.text)

    def get_text(self) -> str:
        """获取输入框文本"""
        return self.text

    def clear(self):
        """清空输入框"""
        self.text = ""
        if self.callback:
            self.callback(self.text)

class Dialog(UIComponent):
    """对话框组件，显示一个模态窗口"""

    # Removed game_manager from __init__ parameters
    def __init__(self, rect: pygame.Rect, title: str,
                 background_color: Tuple[int, int, int, int] = (30, 30, 50, 255),
                 title_bar_color: Tuple[int, int, int, int] = (50, 50, 80, 255),
                 title_color: Tuple[int, int, int] = (220, 220, 240),
                 border_color: Tuple[int, int, int, int] = (100, 100, 150, 255),
                 close_button_color: Tuple[int, int, int] = (200, 100, 100),
                 border_radius: int = BORDER_RADIUS_LARGE,
                 border_width: int = BORDER_WIDTH_NORMAL,
                 title_font: Optional[pygame.font.Font] = None):
        """初始化对话框"""
        super().__init__(rect, visible=False) # Usually start hidden
        # self.game_manager = game_manager # REMOVED THIS LINE
        self.title = title
        self.background_color = background_color
        # ... (rest of __init__ remains the same) ...
        self.title_bar_color = title_bar_color
        self.title_color = title_color
        self.border_color = border_color
        self.close_button_color = close_button_color
        self.border_radius = border_radius
        self.border_width = border_width
        # Get font path from resources and create font object
        default_title_font_path = resources.get_font_path(DEFAULT_FONT_NAME)
        if default_title_font_path:
            self.title_font = title_font or pygame.font.Font(default_title_font_path, DEFAULT_FONT_SIZE_MEDIUM)
        else:
             logger.warning(f"Default font '{DEFAULT_FONT_NAME}' path not found for Dialog title, using system default.")
             self.title_font = title_font or pygame.font.SysFont(None, DEFAULT_FONT_SIZE_MEDIUM)

        self.active = False
        self.elements: List[UIComponent] = []

        self.title_bar_height = DIALOG_TITLE_HEIGHT
        self.title_bar_rect = pygame.Rect(0, 0, rect.width, self.title_bar_height)

        self.content_offset_y = self.title_bar_height
        self.content_rect = pygame.Rect(0, self.content_offset_y,
                                         rect.width, rect.height - self.content_offset_y)
        self.content_surface = pygame.Surface(self.content_rect.size, pygame.SRCALPHA)

        close_btn_y = (self.title_bar_height - DIALOG_CLOSE_BUTTON_SIZE) // 2
        self.close_button_rect = pygame.Rect(
            rect.width - DIALOG_CLOSE_BUTTON_SIZE - DIALOG_CLOSE_BUTTON_MARGIN,
            close_btn_y,
            DIALOG_CLOSE_BUTTON_SIZE, DIALOG_CLOSE_BUTTON_SIZE
        )

        self.dragging = False
        self.drag_offset = (0, 0)
        self.name = f"dialog_{id(self)}"

    # ... (Rest of Dialog class methods: add_element, draw, handle_event, update, show, close, get_title remain unchanged) ...
    def add_element(self, element: UIComponent, relative_pos: Optional[Tuple[int, int]] = None):
        """添加UI元素到对话框内容区域 (相对内容区左上角)"""
        element.parent = self
        if relative_pos:
             element.rect.topleft = relative_pos # Store relative position
        self.elements.append(element)

    def draw(self, surface: pygame.Surface):
        """绘制对话框"""
        if not self.visible: return

        # Frame
        if self.background_color: pygame.draw.rect(surface, self.background_color, self.rect, border_radius=self.border_radius)
        if self.border_color and self.border_width > 0: pygame.draw.rect(surface, self.border_color, self.rect, width=self.border_width, border_radius=self.border_radius)

        # Title Bar
        title_bar_abs_rect = self.title_bar_rect.move(self.rect.topleft)
        if self.title_bar_color: pygame.draw.rect(surface, self.title_bar_color, title_bar_abs_rect, border_top_left_radius=self.border_radius, border_top_right_radius=self.border_radius)

        # Title Text
        if self.title and self.title_font:
            title_surf = self.title_font.render(self.title, True, self.title_color)
            title_text_rect = title_surf.get_rect(center=title_bar_abs_rect.center)
            max_right = self.close_button_rect.left - PADDING_SMALL
            title_text_rect.right = min(title_text_rect.right, self.rect.x + max_right)
            surface.blit(title_surf, title_text_rect)

        # Close Button
        close_abs_rect = self.close_button_rect.move(self.rect.topleft)
        pygame.draw.rect(surface, self.close_button_color, close_abs_rect, border_radius=BORDER_RADIUS_NORMAL)
        cross_pad = DIALOG_CLOSE_BUTTON_PADDING
        pygame.draw.line(surface, (255, 255, 255),(close_abs_rect.left + cross_pad, close_abs_rect.top + cross_pad),(close_abs_rect.right - cross_pad, close_abs_rect.bottom - cross_pad), BORDER_WIDTH_NORMAL)
        pygame.draw.line(surface, (255, 255, 255),(close_abs_rect.right - cross_pad, close_abs_rect.top + cross_pad),(close_abs_rect.left + cross_pad, close_abs_rect.bottom - cross_pad), BORDER_WIDTH_NORMAL)

        # Content
        self.content_surface.fill((0, 0, 0, 0))
        content_origin_abs = (self.rect.x + self.content_rect.x, self.rect.y + self.content_rect.y)
        for element in self.elements:
            element.draw(self.content_surface)
        surface.blit(self.content_surface, content_origin_abs)

    def handle_event(self, event):
        """处理事件 (模态: 消耗内部事件)"""
        if not self.visible or not self.active: return False
        mouse_pos = getattr(event, 'pos', None)

        # 1. Close Button
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1 and mouse_pos:
            close_abs_rect = self.close_button_rect.move(self.rect.topleft)
            if close_abs_rect.collidepoint(mouse_pos):
                self.close()
                return True

        # 2. Child Events
        content_origin_abs = (self.rect.x + self.content_rect.x, self.rect.y + self.content_rect.y)
        original_rects = {}
        event_consumed_by_child = False
        try:
            for element in self.elements: # Shift to absolute for event check
                original_rects[element] = element.rect.copy()
                element.rect.move_ip(content_origin_abs)
            for element in reversed(self.elements):
                if element.handle_event(event):
                    event_consumed_by_child = True
                    break
        finally:
            for element, orig_rect in original_rects.items(): # Restore relative
                element.rect = orig_rect
        if event_consumed_by_child: return True

        # 3. Dragging Title Bar
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1 and mouse_pos:
            title_bar_abs_rect = self.title_bar_rect.move(self.rect.topleft)
            close_abs_rect = self.close_button_rect.move(self.rect.topleft)
            if title_bar_abs_rect.collidepoint(mouse_pos) and not close_abs_rect.collidepoint(mouse_pos):
                self.dragging = True
                self.drag_offset = (mouse_pos[0] - self.rect.x, mouse_pos[1] - self.rect.y)
                return True
        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
            if self.dragging:
                self.dragging = False
                return True
        elif event.type == pygame.MOUSEMOTION and self.dragging and mouse_pos:
            self.rect.x = mouse_pos[0] - self.drag_offset[0]
            self.rect.y = mouse_pos[1] - self.drag_offset[1]
            screen_rect = pygame.display.get_surface().get_rect()
            self.rect.clamp_ip(screen_rect)
            return True

        # 4. Modal: Consume events inside bounds
        if event.type in (pygame.MOUSEBUTTONDOWN, pygame.MOUSEWHEEL) and mouse_pos:
             if self.rect.collidepoint(mouse_pos):
                 return True
        return False

    def update(self, dt):
        """更新对话框及其子元素"""
        if not self.visible or not self.active: return
        for element in self.elements: element.update(dt)

    def show(self):
        """显示并激活对话框"""
        self.visible = True
        self.active = True

    def close(self):
        """隐藏并停用对话框"""
        self.visible = False
        self.active = False
        self.dragging = False

    def get_title(self):
        """获取对话框标题"""
        return self.title

class Toggle(UIComponent):
    """开关组件，可以切换状态"""

    def __init__(self, rect: pygame.Rect, is_on: bool = False,
                 on_color: Tuple[int, int, int] = (0, 200, 0),
                 off_color: Tuple[int, int, int] = (200, 0, 0),
                 knob_color: Tuple[int, int, int] = (240, 240, 240),
                 border_color: Tuple[int, int, int] = (100, 100, 100),
                 visible: bool = True):
        """
        初始化开关组件

        参数:
            rect: 组件矩形区域
            is_on: 是否处于开启状态
            on_color: 开启状态的颜色
            off_color: 关闭状态的颜色
            knob_color: 开关旋钮的颜色
            border_color: 边框颜色
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.is_on = is_on
        self.on_color = on_color
        self.off_color = off_color
        self.knob_color = knob_color
        self.border_color = border_color
        self.animation_progress = 1.0 if is_on else 0.0
        self.is_animating = False
        self.animation_speed = 5.0  # 动画速度

    def draw(self, surface: pygame.Surface):
        """绘制开关"""
        if not self.visible:
            return

        # 计算旋钮位置
        knob_radius = self.rect.height // 2 - 4
        knob_pos_x = int(self.rect.x + 4 + (self.rect.width - 2 * knob_radius - 8) * self.animation_progress)
        knob_pos_y = self.rect.y + self.rect.height // 2

        # 绘制背景 - 使用当前动画状态混合颜色
        bg_color = (
            int(self.off_color[0] * (1 - self.animation_progress) + self.on_color[0] * self.animation_progress),
            int(self.off_color[1] * (1 - self.animation_progress) + self.on_color[1] * self.animation_progress),
            int(self.off_color[2] * (1 - self.animation_progress) + self.on_color[2] * self.animation_progress)
        )
        pygame.draw.rect(surface, bg_color, self.rect, border_radius=self.rect.height // 2)

        # 绘制边框
        pygame.draw.rect(surface, self.border_color, self.rect, width=1, border_radius=self.rect.height // 2)

        # 绘制旋钮
        pygame.draw.circle(surface, self.knob_color, (knob_pos_x, knob_pos_y), knob_radius)
        pygame.draw.circle(surface, self.border_color, (knob_pos_x, knob_pos_y), knob_radius, width=1)

    def update(self, dt: float):
        """更新开关状态"""
        if self.is_animating:
            target = 1.0 if self.is_on else 0.0
            diff = target - self.animation_progress

            # 使用简单的线性插值进行动画
            move = self.animation_speed * dt

            if abs(diff) <= move:
                # 到达目标位置，结束动画
                self.animation_progress = target
                self.is_animating = False
            else:
                # 继续动画
                self.animation_progress += move if diff > 0 else -move

    def handle_event(self, event) -> bool:
        """处理事件"""
        if not self.visible:
            return False

        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.rect.collidepoint(event.pos):
                self.toggle()
                return True

        return False

    def toggle(self):
        """切换开关状态"""
        self.is_on = not self.is_on
        self.is_animating = True
        logger.debug(f"开关状态切换为: {self.is_on}")

    def set_state(self, is_on: bool):
        """设置开关状态"""
        if self.is_on != is_on:
            self.is_on = is_on
            self.is_animating = True
            self.animation_progress = 1.0 if is_on else 0.0
            logger.debug(f"开关状态设置为: {self.is_on}")


class Dropdown(UIComponent):
    """下拉菜单组件"""

    # 全局下拉菜单列表，用于追踪所有打开的下拉菜单
    open_dropdowns = []

    def __init__(self, rect: pygame.Rect, options: List[str],
                 selected_index: int = 0, max_visible: int = 5,
                 colors: Dict[str, Tuple[int, int, int]] = None,
                 font: pygame.font.Font = None,
                 visible: bool = True):
        """
        初始化下拉菜单

        参数:
            rect: 组件矩形区域
            options: 选项列表
            selected_index: 默认选中项的索引
            max_visible: 最大可见选项数
            colors: 颜色配置
            font: 字体
            visible: 是否可见
        """
        super().__init__(rect, visible)
        self.options = options
        self.selected_index = selected_index if 0 <= selected_index < len(options) else 0
        self.max_visible = max_visible
        self.font = font or pygame.font.SysFont(None, 22)

        # 设置默认颜色
        self.colors = {
            "normal": (240, 240, 240),
            "hover": (220, 220, 250),
            "selected": (200, 200, 230),
            "text": (0, 0, 0),
            "border": (100, 100, 100),
            "arrow": (50, 50, 50)
        }

        # 更新颜色
        if colors:
            self.colors.update(colors)

        # 状态
        self.is_open = False
        self.hover_index = -1
        self.scroll_offset = 0

        # 计算选项高度和下拉区域
        self.option_height = self.font.get_height() + 10
        self.dropdown_rect = pygame.Rect(
            rect.x, rect.y + rect.height,
            rect.width, min(len(options), max_visible) * self.option_height
        )

    def draw(self, surface: pygame.Surface):
        """绘制下拉菜单"""
        if not self.visible:
            return

        # 绘制主按钮
        pygame.draw.rect(surface, self.colors["normal"], self.rect, border_radius=3)
        pygame.draw.rect(surface, self.colors["border"], self.rect, width=1, border_radius=3)

        # 绘制选中项文本
        if 0 <= self.selected_index < len(self.options):
            text = self.options[self.selected_index]
            text_surf = self.font.render(text, True, self.colors["text"])
            text_rect = text_surf.get_rect(midleft=(self.rect.x + 10, self.rect.centery))
            surface.blit(text_surf, text_rect)

        # 绘制下拉箭头
        arrow_size = 8
        arrow_x = self.rect.right - arrow_size - 10
        arrow_y = self.rect.centery - arrow_size // 2

        if self.is_open:
            # 向上箭头
            pygame.draw.polygon(surface, self.colors["arrow"], [
                (arrow_x, arrow_y + arrow_size),
                (arrow_x + arrow_size, arrow_y + arrow_size),
                (arrow_x + arrow_size // 2, arrow_y)
            ])
        else:
            # 向下箭头
            pygame.draw.polygon(surface, self.colors["arrow"], [
                (arrow_x, arrow_y),
                (arrow_x + arrow_size, arrow_y),
                (arrow_x + arrow_size // 2, arrow_y + arrow_size)
            ])

        # 注意：下拉选项不在这里绘制，而是由Screen或UIManager在最后绘制
        # 以确保它们总是显示在最上层，不会被其他元素遮挡

    def draw_dropdown_options(self, surface: pygame.Surface):
        """绘制下拉选项(在最顶层绘制)"""
        if not self.visible or not self.is_open:
            return

        # 调整下拉菜单位置和大小
        visible_count = min(len(self.options), self.max_visible)
        self.dropdown_rect.height = visible_count * self.option_height

        # 绘制下拉背景
        pygame.draw.rect(surface, self.colors["normal"], self.dropdown_rect, border_radius=3)
        pygame.draw.rect(surface, self.colors["border"], self.dropdown_rect, width=1, border_radius=3)

        # 计算可见选项的开始和结束索引
        start_idx = self.scroll_offset
        end_idx = min(start_idx + self.max_visible, len(self.options))

        # 绘制每个选项
        for i in range(start_idx, end_idx):
            option_idx = i - start_idx
            option_rect = pygame.Rect(
                self.dropdown_rect.x,
                self.dropdown_rect.y + option_idx * self.option_height,
                self.dropdown_rect.width,
                self.option_height
            )

            # 选择背景色
            if i == self.selected_index:
                bg_color = self.colors["selected"]
            elif i == self.hover_index:
                bg_color = self.colors["hover"]
            else:
                bg_color = self.colors["normal"]

            # 绘制选项背景
            pygame.draw.rect(surface, bg_color, option_rect)

            # 绘制选项文本
            text_surf = self.font.render(self.options[i], True, self.colors["text"])
            text_rect = text_surf.get_rect(midleft=(option_rect.x + 10, option_rect.centery))
            surface.blit(text_surf, text_rect)

    def handle_event(self, event) -> bool:
        """处理事件"""
        if not self.visible:
            return False

        # 处理点击事件
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            # 点击主按钮
            if self.rect.collidepoint(event.pos):
                # 如果菜单是关闭的，打开它并将其添加到全局列表
                if not self.is_open:
                    # 关闭其他所有下拉菜单
                    for dropdown in Dropdown.open_dropdowns:
                        if dropdown != self:
                            dropdown.is_open = False
                    Dropdown.open_dropdowns = [self]
                    self.is_open = True
                else:
                    # 关闭菜单并从全局列表中移除
                    self.is_open = False
                    if self in Dropdown.open_dropdowns:
                        Dropdown.open_dropdowns.remove(self)
                return True

            # 点击下拉选项
            if self.is_open and self.dropdown_rect.collidepoint(event.pos):
                # 计算点击的选项索引
                rel_y = event.pos[1] - self.dropdown_rect.y
                option_idx = rel_y // self.option_height + self.scroll_offset

                if 0 <= option_idx < len(self.options):
                    self.selected_index = option_idx
                    self.is_open = False
                    # 从全局列表中移除
                    if self in Dropdown.open_dropdowns:
                        Dropdown.open_dropdowns.remove(self)
                    # 调用选择回调
                    if hasattr(self, 'on_option_select'):
                        self.on_option_select(option_idx)
                    logger.debug(f"选择下拉选项: {self.options[option_idx]}")
                return True

            # 点击其他区域，关闭下拉菜单
            if self.is_open:
                self.is_open = False
                # 从全局列表中移除
                if self in Dropdown.open_dropdowns:
                    Dropdown.open_dropdowns.remove(self)
                return True

        # 处理鼠标移动事件
        elif event.type == pygame.MOUSEMOTION:
            if self.is_open and self.dropdown_rect.collidepoint(event.pos):
                # 计算悬停的选项索引
                rel_y = event.pos[1] - self.dropdown_rect.y
                hover_idx = rel_y // self.option_height + self.scroll_offset

                if 0 <= hover_idx < len(self.options):
                    self.hover_index = hover_idx
                else:
                    self.hover_index = -1
            else:
                self.hover_index = -1

        # 处理滚轮事件（滚动选项）
        elif event.type == pygame.MOUSEWHEEL and self.is_open and self.dropdown_rect.collidepoint(pygame.mouse.get_pos()):
            # 向上滚动为正值，向下滚动为负值
            self.scroll_offset = max(0, min(len(self.options) - self.max_visible,
                                           self.scroll_offset - event.y))
            return True

        return False

    def get_selected(self) -> str:
        """获取当前选中项的文本"""
        if 0 <= self.selected_index < len(self.options):
            return self.options[self.selected_index]
        return ""

    def get_selected_index(self) -> int:
        """获取当前选中项的索引"""
        return self.selected_index

    def set_selected_index(self, index: int):
        """设置选中项索引"""
        if 0 <= index < len(self.options):
            self.selected_index = index

    def set_selected(self, option: str):
        """设置选中项"""
        if option in self.options:
            self.selected_index = self.options.index(option)

    def set_options(self, options: List[str]):
        """更新下拉菜单的选项列表

        参数:
            options: 新的选项列表
        """
        if not options:  # 确保选项列表不为空
            options = ["无选项"]

        self.options = options

        # 调整选中索引，确保在有效范围内
        if self.selected_index >= len(options):
            self.selected_index = 0 if options else -1

        # 重新计算下拉区域
        self.dropdown_rect = pygame.Rect(
            self.rect.x, self.rect.y + self.rect.height,
            self.rect.width, min(len(options), self.max_visible) * self.option_height
        )

        # 重置滚动偏移和悬停索引
        self.scroll_offset = 0
        self.hover_index = -1

        # 如果下拉菜单是打开的，关闭它
        self.is_open = False

class DraggableSkillIcon(UIComponent):
    """可拖动的技能图标组件，显示技能图片 (解耦版)"""

    # Added on_drop_callback parameter
    def __init__(self, rect: pygame.Rect,
                 font: pygame.font.Font,
                 slot_idx: int = -1,
                 skill_id: Optional[str] = None,
                 skill_name: Optional[str] = None,
                 colors: Optional[Dict[str, Any]] = None,
                 on_drop_callback: Optional[Callable[['DraggableSkillIcon', Tuple[int, int]], bool]] = None, # Callback added
                 border_radius: int = BORDER_RADIUS_NORMAL,
                 border_width: int = BORDER_WIDTH_THIN,
                 visible: bool = True):
        """
        初始化可拖动技能图标 (解耦版)
        """
        # Use the provided font or load default
        # Get font path from resources and create font object
        actual_font = font # Use provided font if available
        if not actual_font:
            default_font_path = resources.get_font_path(DEFAULT_FONT_NAME)
            if default_font_path:
                actual_font = pygame.font.Font(default_font_path, DEFAULT_FONT_SIZE_NORMAL)
            else:
                logger.warning(f"Default font '{DEFAULT_FONT_NAME}' path not found for DraggableSkillIcon, using system default.")
                actual_font = pygame.font.SysFont(None, DEFAULT_FONT_SIZE_NORMAL)

        super().__init__(rect, visible)
        self.font = actual_font
        self.slot_idx = slot_idx
        self.skill_id = skill_id
        self.on_drop_callback = on_drop_callback # Store the callback
        self.border_radius = border_radius
        self.border_width = border_width

        # State
        self.is_dragging = False
        self.pressed = False
        self.hovered = False
        self.active = True
        self.disabled = False

        # Dragging internals
        self.drag_offset = (0, 0)
        self.original_pos = rect.topleft
        self.press_mouse_pos = None

        # Image/Display
        self.image: Optional[pygame.Surface] = None
        self._current_skill_name_for_image: Optional[str] = None
        self._fallback_text: str = str(slot_idx) if slot_idx >= 0 else ""
        self._fallback_text_surface: Optional[pygame.Surface] = None

        # Colors (using constants where applicable, keeping original keys for now)
        self.colors = {
            "normal_bg": colors.get("normal", (40, 40, 70)) if colors else (40, 40, 70),
            "hover_bg": colors.get("hover", (60, 60, 100)) if colors else (60, 60, 100),
            "dragging_bg": colors.get("dragging", (90, 90, 130)) if colors else (90, 90, 130),
            "disabled_overlay": colors.get("disabled", (50, 50, 50, 180)) if colors else (50, 50, 50, 180),
            "text_color": colors.get("text", (200, 200, 220)) if colors else (200, 200, 220),
            "disabled_text_color": colors.get("disabled_text", (160, 160, 160)) if colors else (160, 160, 160),
            "border_color": colors.get("border", (80, 80, 120)) if colors else (80, 80, 120),
            "dragging_border_color": colors.get("dragging_border", (150, 150, 180)) if colors else (150, 150, 180)
        }
        # Allow direct color overrides if needed
        if colors: self.colors.update(colors)

        # Initial setup
        self.set_skill_display(skill_id, skill_name)
        self._render_fallback_text_surface()

    # ... (_render_fallback_text_surface, set_skill_display, set_fallback_text, set_active, set_disabled, reset_position, _reset_drag_state, draw - remain the same as in previous multi-edit attempt) ...
    def _render_fallback_text_surface(self):
        if not self.font: return
        text_color = self.colors.get("disabled_text_color") if self.disabled else self.colors.get("text_color")
        self.fallback_text_surface = self.font.render(self._fallback_text, True, text_color)

    def set_skill_display(self, skill_id: Optional[str], skill_name: Optional[str]):
        needs_image_update = (skill_name != self._current_skill_name_for_image)
        self.skill_id = skill_id
        if needs_image_update:
            self._current_skill_name_for_image = skill_name
            self.image = None
            if skill_name:
                image_path = f"assets/images/skill/{skill_name}.png"
                try:
                    loaded_image = resources.load_image(image_path)
                    if loaded_image:
                        img_w, img_h = loaded_image.get_size()
                        scale = min(self.rect.width / img_w, self.rect.height / img_h) * DRAGGABLE_ICON_IMAGE_SCALE_FACTOR
                        new_size = (int(img_w * scale), int(img_h * scale))
                        if new_size[0] > 0 and new_size[1] > 0:
                            self.image = pygame.transform.smoothscale(loaded_image, new_size)
                        else: self.image = None
                    else: self.image = None
                except Exception as e:
                    logger.warning(f"Failed to load skill icon '{image_path}': {e}")
                    self.image = None
            else: self.image = None
        self._render_fallback_text_surface()

    def set_fallback_text(self, text: str):
        if self._fallback_text != text:
            self._fallback_text = text
            self._render_fallback_text_surface()

    def set_active(self, active: bool):
        if self.active != active:
            self.active = active
            if not active and self.is_dragging:
                self._reset_drag_state()
                self.reset_position()

    def set_disabled(self, disabled: bool):
        if self.disabled != disabled:
            self.disabled = disabled
            self._render_fallback_text_surface()

    def reset_position(self):
        logger.debug(f"Icon Slot {self.slot_idx}: Resetting position")
        self.rect.topleft = self.original_pos
        self._reset_drag_state()

    def _reset_drag_state(self):
         self.is_dragging = False
         self.pressed = False
         self.press_mouse_pos = None
         self.drag_offset = (0, 0)

    def draw(self, surface: pygame.Surface):
        if not self.visible: return
        bg_color = self.colors.get("normal_bg")
        border_color = self.colors.get("border_color")
        if self.is_dragging:
            bg_color = self.colors.get("dragging_bg", bg_color)
            border_color = self.colors.get("dragging_border_color", border_color)
        elif self.hovered and self.active:
            bg_color = self.colors.get("hover_bg", bg_color)

        pygame.draw.rect(surface, bg_color, self.rect, border_radius=self.border_radius)
        if self.border_width > 0: pygame.draw.rect(surface, border_color, self.rect, width=self.border_width, border_radius=self.border_radius)

        if self.image:
            img_rect = self.image.get_rect(centerx=self.rect.centerx, centery=self.rect.centery + DRAGGABLE_ICON_IMAGE_V_OFFSET)
            surface.blit(self.image, img_rect)
        elif self.fallback_text_surface:
            text_rect = self.fallback_text_surface.get_rect(center=self.rect.center)
            surface.blit(self.fallback_text_surface, text_rect)

        if self.disabled:
            overlay_color = self.colors.get("disabled_overlay")
            overlay_surf = pygame.Surface(self.rect.size, pygame.SRCALPHA)
            overlay_surf.fill(overlay_color)
            surface.blit(overlay_surf, self.rect.topleft)

    # Updated handle_event with callback logic
    def handle_event(self, event) -> bool:
        """处理鼠标事件 (点击、拖动、释放) - 使用回调解耦"""
        if not self.visible: return False

        mouse_pos = getattr(event, 'pos', None)
        if not mouse_pos: return False # Need position

        collided = self.rect.collidepoint(mouse_pos)

        # --- Mouse Down ---
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if collided and self.active:
                 self.pressed = True
                 self.press_mouse_pos = mouse_pos
                 self.drag_offset = (mouse_pos[0] - self.rect.x, mouse_pos[1] - self.rect.y)
                 self.original_pos = self.rect.topleft
                 return True # Consume
            return False

        # --- Mouse Motion ---
        elif event.type == pygame.MOUSEMOTION:
            self.hovered = collided and self.active # Update visual hover state
            if self.pressed and self.active:
                 if not self.is_dragging and self.press_mouse_pos:
                     dx = abs(mouse_pos[0] - self.press_mouse_pos[0])
                     dy = abs(mouse_pos[1] - self.press_mouse_pos[1])
                     if dx > DRAGGABLE_ICON_DRAG_THRESHOLD or dy > DRAGGABLE_ICON_DRAG_THRESHOLD:
                         self.is_dragging = True
                         logger.debug(f"Icon Slot {self.slot_idx}: Started dragging")

                 if self.is_dragging:
                     self.rect.x = mouse_pos[0] - self.drag_offset[0]
                     self.rect.y = mouse_pos[1] - self.drag_offset[1]

                 return True # Consume motion while pressed (dragging or not)
            return False # Don't consume if not pressed

        # --- Mouse Up ---
        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
            if not self.pressed: return False # Ignore release if not initially pressed on this icon

            was_dragging = self.is_dragging
            self._reset_drag_state() # Reset flags regardless of drop validity

            if was_dragging:
                logger.debug(f"Icon Slot {self.slot_idx}: Drag ended at {mouse_pos}")
                drop_handled = False
                # Call the callback if it exists
                if self.on_drop_callback:
                    try:
                        # Callback determines if the drop was valid/handled
                        drop_handled = self.on_drop_callback(self, mouse_pos)
                    except Exception as e:
                        logger.error(f"Error in DraggableSkillIcon on_drop_callback: {e}", exc_info=True)
                        drop_handled = False # Treat callback error as invalid drop

                # If drop was not handled by callback, reset position
                if not drop_handled:
                    logger.debug(f"Icon Slot {self.slot_idx}: Drop not handled by callback, resetting position.")
                    self.reset_position()
                # Else: Callback handled it, position might have been updated by the callback

                return True # Consume the mouse up event because a drag finished

            else: # Was a click (pressed but not dragged)
                 logger.debug(f"Icon Slot {self.slot_idx}: Clicked.")
                 # Consume the click event only if released inside the button
                 return collided

        return False

# ... (Rest of file remains unchanged) ...