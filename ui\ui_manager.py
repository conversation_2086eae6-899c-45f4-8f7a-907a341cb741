import pygame
from typing import Dict, List, Optional, Callable, Any, Tuple, TYPE_CHECKING

from ui.components import DraggableSkillIcon, UIComponent, Button, Panel, Text, ProgressBar, InputBox, Dropdown
from utils.logger import logger
from utils.resource_manager import resources

# 类型提示导入
if TYPE_CHECKING:
    from core.game import Game
    from core.types import GameType

class Screen:
    """屏幕/界面类，代表游戏中的一个完整界面"""

    def __init__(self, name: str):
        """
        初始化界面

        参数:
            name: 界面名称
        """
        self.name = name
        self.components = []
        self.visible = False
        self.active = False

    def add_component(self, component: UIComponent):
        """添加组件到界面"""
        self.components.append(component)

    def remove_component(self, component: UIComponent):
        """从界面移除组件"""
        if component in self.components:
            self.components.remove(component)

    def clear_components(self):
        """清除所有组件"""
        self.components.clear()

    def draw(self, surface: pygame.Surface):
        """绘制界面的所有组件"""
        if not self.visible:
            return

        # 先绘制所有普通组件
        for component in self.components:
            component.draw(surface)

    def update(self, dt: float):
        """更新界面的所有组件"""
        if not self.active:
            return

        for component in self.components:
            component.update(dt)

        # 确保所有组件可见
        for component in self.components:
            component.visible = True

        logger.info(f"界面 {self.name} 显示完成")

    def handle_event(self, event) -> bool:
        """处理界面的事件"""
        if not self.active:
            return False

        # 从上到下处理事件（后添加的组件先处理）
        for component in reversed(self.components):
            if component.handle_event(event):
                return True

        return False

    def show(self):
        """显示界面"""
        logger.info(f"显示界面: {self.name}, 组件数量: {len(self.components)}")
        self.visible = True
        self.active = True

        # 确保所有组件可见
        for component in self.components:
            component.visible = True

        logger.info(f"界面 {self.name} 显示完成")

    def hide(self):
        """隐藏界面"""
        logger.info(f"隐藏界面: {self.name}")
        self.visible = False
        self.active = False

        # 确保所有组件隐藏
        for component in self.components:
            component.visible = False

        logger.info(f"界面 {self.name} 隐藏完成")

    def activate(self):
        """激活界面，但不改变可见性"""
        self.active = True

    def deactivate(self):
        """停用界面，但不改变可见性"""
        self.active = False

class Dialog(Screen):
    """对话框类，代表一个模态对话框"""

    def __init__(self, name: str, title: str, size: Tuple[int, int], position: Optional[Tuple[int, int]] = None):
        """
        初始化对话框

        参数:
            name: 对话框名称
            title: 对话框标题
            size: 对话框大小 (宽, 高)
            position: 对话框位置 (可选，默认为屏幕中央)
        """
        super().__init__(name)

        # 计算对话框位置（默认居中）
        screen_size = pygame.display.get_surface().get_size()
        if position is None:
            position = (
                (screen_size[0] - size[0]) // 2,
                (screen_size[1] - size[1]) // 2
            )

        # 创建背景面板
        self.panel = Panel(
            pygame.Rect(position[0], position[1], size[0], size[1]),
            color=(240, 240, 240),
            border_color=(100, 100, 100),
            border_width=2
        )
        self.add_component(self.panel)

        # 创建标题文本
        font = pygame.font.SysFont(None, 28)
        title_height = 40

        self.title_bar = Panel(
            pygame.Rect(position[0], position[1], size[0], title_height),
            color=(180, 180, 200),
            border_color=(100, 100, 100),
            border_width=0
        )
        self.add_component(self.title_bar)

        self.title_text = Text(
            pygame.Rect(position[0] + 10, position[1], size[0] - 60, title_height),
            title,
            font,
            color=(0, 0, 0),
            align="left"
        )
        self.add_component(self.title_text)

        # 创建关闭按钮
        close_button_size = 30
        self.close_button = Button(
            pygame.Rect(
                position[0] + size[0] - close_button_size - 5,
                position[1] + 5,
                close_button_size,
                close_button_size
            ),
            "X",
            {
                "normal": (200, 100, 100),
                "hover": (220, 120, 120),
                "pressed": (180, 80, 80),
                "text": (255, 255, 255),
                "border": (150, 50, 50)
            },
            pygame.font.SysFont(None, 24),
            self.hide
        )
        self.add_component(self.close_button)

        # 内容区域
        self.content_rect = pygame.Rect(
            position[0] + 10,
            position[1] + title_height + 10,
            size[0] - 20,
            size[1] - title_height - 20
        )

    def add_content(self, component: UIComponent):
        """添加组件到内容区域"""
        self.add_component(component)

class Timer:
    """简单的倒计时定时器"""
    def __init__(self, rect, duration_ms=1000):
        self.rect = rect
        self.duration_ms = duration_ms
        self.elapsed_ms = 0
        self.active = False
        self.on_timer_end = None
        self.visible = False

    def start(self, duration_ms=None):
        """启动计时器"""
        if duration_ms is not None:
            self.duration_ms = duration_ms
        self.elapsed_ms = 0
        self.active = True
        self.visible = True

    def update(self, dt_ms):
        """更新计时器"""
        if not self.active:
            return

        self.elapsed_ms += dt_ms
        if self.elapsed_ms >= self.duration_ms:
            self.active = False
            self.visible = False
            if self.on_timer_end:
                self.on_timer_end()

    def reset(self):
        """重置计时器"""
        self.elapsed_ms = 0

    def stop(self):
        """停止计时器"""
        self.active = False
        self.visible = False

    def get_progress(self):
        """获取进度(0-1)"""
        return min(1.0, self.elapsed_ms / self.duration_ms)

    def get_remaining(self):
        """获取剩余时间(毫秒)"""
        return max(0, self.duration_ms - self.elapsed_ms)

class UIManager:
    """UI管理器，负责管理所有UI界面和组件"""

    def __init__(self):
        """初始化UI管理器"""
        self.screens: Dict[str, Screen] = {}
        self.active_screen: Optional[str] = None
        self.dialogs: Dict[str, Dialog] = {}
        self.fonts: Dict[str, pygame.font.Font] = {}
        self.tooltip = None

        # 全局UI状态
        self.is_dragging = False
        self.drag_component = None
        self.drag_offset = (0, 0)

        # 通用颜色定义
        self.colors = {
            "button_normal": (200, 200, 200),
            "button_hover": (220, 220, 220),
            "button_pressed": (180, 180, 180),
            "button_text": (0, 0, 0),
            "panel_bg": (240, 240, 240),
            "panel_border": (120, 120, 120),
            "text": (0, 0, 0),
            "text_highlight": (0, 0, 150),
            "warning": (200, 50, 50),
            "success": (50, 180, 50),
            "hp_bar": (220, 50, 50),
            "mp_bar": (50, 50, 220),
            "exp_bar": (50, 200, 50),
            "transparent_bg": (0, 0, 0, 180)
        }

        self.quality_colors = {
            "普通": pygame.Color(200, 200, 200),  # 灰色
            "精良": pygame.Color(100, 180, 255),  # 蓝色
            "稀有": pygame.Color(200, 100, 255),  # 紫色
            "史诗": pygame.Color(255, 165, 0),   # 橙色
            "传说": pygame.Color(255, 69, 0)     # 红色/暗橙
        }

    def initialize(self, game_manager=None):
        """初始化UI系统，加载字体和资源"""
        # 保存游戏管理器引用
        self.game_manager = game_manager

        # 加载常用字体
        try:
            self.fonts["small"] = pygame.font.SysFont(None, 16)
            self.fonts["normal"] = pygame.font.SysFont(None, 22)
            self.fonts["large"] = pygame.font.SysFont(None, 28)
            self.fonts["title"] = pygame.font.SysFont(None, 36)

            # 尝试加载中文字体
            try:
                chinese_font_path = resources.get_font_path("chinese")
                if chinese_font_path:
                    self.fonts["chinese_small"] = pygame.font.Font(chinese_font_path, 14)
                    self.fonts["chinese_normal"] = pygame.font.Font(chinese_font_path, 18)
                    self.fonts["chinese_large"] = pygame.font.Font(chinese_font_path, 24)
                    self.fonts["chinese_title"] = pygame.font.Font(chinese_font_path, 32)
            except Exception as e:
                logger.warning(f"无法加载中文字体: {e}")
                # 回退到系统字体
                self.fonts["chinese_small"] = self.fonts["small"]
                self.fonts["chinese_normal"] = self.fonts["normal"]
                self.fonts["chinese_large"] = self.fonts["large"]
                self.fonts["chinese_title"] = self.fonts["title"]

            logger.info("UI字体加载成功")
        except pygame.error as e:
            logger.error(f"加载系统字体失败: {e}")
            raise
        except FileNotFoundError as e:
            logger.error(f"找不到字体文件: {e}")
            raise
        except Exception as e:
            logger.error(f"UI字体加载时发生未知错误: {e}")
            raise

    def create_screen(self, name: str) -> Screen:
        """创建新界面"""
        screen = Screen(name)
        self.screens[name] = screen
        return screen

    def create_dialog(self, name: str, title: str, size: Tuple[int, int],
                       position: Optional[Tuple[int, int]] = None) -> Dialog:
        """创建对话框"""
        dialog = Dialog(name, title, size, position)
        self.dialogs[name] = dialog
        return dialog

    def show_screen(self, name: str):
        """显示指定的界面，隐藏其他界面"""
        logger.info(f"准备显示界面: {name}")

        if name not in self.screens:
            logger.error(f"尝试显示不存在的界面: {name}")
            logger.info(f"当前已注册的界面: {list(self.screens.keys())}")
            return

        # 输出调试信息
        logger.info(f"当前界面: {self.active_screen}, 切换到: {name}")
        logger.info(f"目标界面实例类型: {type(self.screens[name]).__name__}")

        # 隐藏当前活动的界面
        if self.active_screen and self.active_screen in self.screens:
            logger.info(f"隐藏当前界面: {self.active_screen}")
            self.screens[self.active_screen].hide()

        # 显示新界面
        logger.info(f"正在显示界面: {name}")
        self.screens[name].show()
        self.active_screen = name
        logger.info(f"当前活动界面已更新为: {name}")

    def show_dialog(self, name: str):
        """显示指定的已存在对话框 (Simplified - Fixes Issue 2)

        注意: 此方法不再自动创建对话框。
        要显示简单的消息或确认框，请使用 `show_message` 或 `show_confirmation`。

        参数:
            name: 要显示的对话框的名称 (必须已通过 create_dialog 创建)
        """
        if name in self.dialogs:
            if self.dialogs[name].visible:
                logger.debug(f"对话框 '{name}' 已经可见。")
            else:
                logger.info(f"显示已存在的对话框: {name}")
                self.dialogs[name].show()
        else:
            logger.warning(f"尝试显示不存在或未注册的对话框: {name}")
            logger.debug(f"当前已注册的对话框: {list(self.dialogs.keys())}")
            # 不再尝试基于消息创建对话框

    def hide_dialog(self, name: str):
        """隐藏指定的对话框"""
        if name not in self.dialogs:
            logger.warning(f"尝试隐藏不存在的对话框: {name}")
            return

        logger.info(f"隐藏对话框: {name}")
        self.dialogs[name].hide()

    def update(self, dt: float):
        """更新所有活动的UI界面"""
        # 更新当前活动的界面
        if self.active_screen and self.active_screen in self.screens:
            self.screens[self.active_screen].update(dt)

        # 更新所有活动的对话框
        for dialog_name, dialog in self.dialogs.items():
            if dialog.active:
                dialog.update(dt)

    def draw(self, surface: pygame.Surface):
        """绘制所有可见的UI界面"""
        # 绘制当前可见的界面
        if self.active_screen and self.active_screen in self.screens:
            self.screens[self.active_screen].draw(surface)

        # 绘制所有可见的对话框
        for dialog_name, dialog in self.dialogs.items():
            if dialog.visible:
                dialog.draw(surface)

        # 绘制工具提示（如果有）
        if self.tooltip and self.tooltip.visible:
            self.tooltip.draw(surface)

        # --- 新增：绘制所有打开的下拉菜单选项 ---
        try:
            from .components import Dropdown # 确保导入
            for dropdown in Dropdown.open_dropdowns:
                if dropdown.visible:
                    dropdown.draw_dropdown_options(surface)
        except Exception as e:
            logger.error(f"Error drawing dropdown options: {e}")
        # --- 结束新增 ---

    def handle_event(self, event) -> bool:
        """处理UI事件"""
        # TODO: 需要处理打开的下拉菜单的事件 (可能需要UIManager维护列表)

        # 首先处理对话框事件（对话框有最高优先级）
        for dialog_name, dialog in reversed(list(self.dialogs.items())):
            if dialog.active and dialog.handle_event(event):
                return True

        # 处理当前活动界面的事件
        if self.active_screen and self.active_screen in self.screens:
            if self.screens[self.active_screen].handle_event(event):
                return True

        return False

    def create_button(self, rect: pygame.Rect, text: str,
                       callback: Callable[[], Any],
                       font_name: str = "chinese_normal") -> Button:
        """
        创建按钮的便捷方法

        参数:
            rect: 按钮区域
            text: 按钮文本
            callback: 点击回调
            font_name: 字体名称
        """
        return Button(
            rect,
            text,
            {
                "normal": self.colors["button_normal"],
                "hover": self.colors["button_hover"],
                "pressed": self.colors["button_pressed"],
                "text": self.colors["button_text"],
                "border": self.colors["panel_border"]
            },
            self.fonts.get(font_name, self.fonts["normal"]),
            callback
        )

    def create_panel(self, rect: pygame.Rect,
                     color: Optional[Tuple[int, int, int, int]] = None,
                     border_color: Optional[Tuple[int, int, int, int]] = None,
                     border_width: int = 1,
                     alpha: int = 255) -> Panel:
        """
        创建面板的便捷方法

        参数:
            rect: 面板矩形区域
            color: 背景颜色，默认使用UI管理器默认颜色
            border_color: 边框颜色，默认使用UI管理器默认颜色
            border_width: 边框宽度
            alpha: 透明度，0-255（0为完全透明）
        """
        # 使用默认颜色或指定颜色
        bg_color = color if color is not None else self.colors["panel_bg"]
        bd_color = border_color if border_color is not None else self.colors["panel_border"]

        # 如果颜色没有透明度通道，添加透明度
        if len(bg_color) == 3:
            bg_color = bg_color + (alpha,)
        if len(bd_color) == 3:
            bd_color = bd_color + (alpha,)

        return Panel(
            rect,
            color=bg_color,
            border_color=bd_color,
            border_width=border_width
        )

    def create_text(self, rect: pygame.Rect, text: str,
                     font_name: str = "chinese_normal",
                     color: Optional[Tuple[int, int, int]] = None,
                     align: str = "left") -> Text:
        """创建文本的便捷方法"""
        if color is None:
            color = self.colors["text"]

        return Text(
            rect,
            text,
            self.fonts.get(font_name, self.fonts["normal"]),
            color,
            align
        )

    def create_hp_bar(self, rect: pygame.Rect, value: float, max_value: float) -> ProgressBar:
        """创建生命值条的便捷方法"""
        return ProgressBar(
            rect,
            value,
            max_value,
            {
                "background": (50, 50, 50),
                "fill": self.colors["hp_bar"],
                "border": (100, 100, 100)
            }
        )

    def create_mp_bar(self, rect: pygame.Rect, value: float, max_value: float) -> ProgressBar:
        """创建魔法值条的便捷方法"""
        return ProgressBar(
            rect,
            value,
            max_value,
            {
                "background": (50, 50, 50),
                "fill": self.colors["mp_bar"],
                "border": (100, 100, 100)
            }
        )

    def create_exp_bar(self, rect: pygame.Rect, value: float, max_value: float) -> ProgressBar:
        """创建经验条组件"""
        colors = {
            "background": (50, 50, 70),
            "fill": (200, 200, 120),
            "border": (80, 80, 100)
        }
        return ProgressBar(rect, value, max_value, colors)

    def create_progress_bar(self, rect: pygame.Rect, value: float, max_value: float,
                           colors: Optional[Dict[str, Tuple[int, int, int]]] = None) -> ProgressBar:
        """创建通用进度条组件

        参数:
            rect: 进度条矩形区域
            value: 当前值
            max_value: 最大值
            colors: 颜色配置，包含 'background', 'fill', 'border'
        """
        if colors is None:
            colors = {
                "background": (50, 50, 70),
                "fill": (100, 100, 255),
                "border": (80, 80, 120)
            }
        return ProgressBar(rect, value, max_value, colors)

    def create_input_box(self, rect: pygame.Rect, initial_text: str = "",
                        callback: Callable[[str], None] = None,
                        max_length: int = 20,
                        font_name: str = "normal") -> InputBox:
        """创建输入框组件

        参数:
            rect: 输入框矩形区域
            initial_text: 初始文本
            callback: 文本变化回调函数
            max_length: 最大文本长度
            font_name: 字体名称

        返回:
            InputBox: 创建的输入框组件
        """
        font = self.get_font(font_name)
        return InputBox(
            rect,
            initial_text,
            callback,
            max_length,
            font,
            text_color=(255, 255, 255),
            active_color=(100, 100, 200),
            inactive_color=(70, 70, 120),
            background_color=(30, 30, 50)
        )

    def get_font(self, name: str) -> pygame.font.Font:
        """获取指定名称的字体"""
        return self.fonts.get(name, self.fonts["normal"])

    def get_quality_color(self, quality: str) -> pygame.Color:
        """根据物品品质返回对应的颜色"""
        return self.quality_colors.get(quality, self.quality_colors["普通"]) # 默认为普通品质颜色

    def create_confirmation_dialog(self, title: str, message: str,
                                    on_confirm: Callable[[], Any],
                                    on_cancel: Optional[Callable[[], Any]] = None) -> Dialog:
        """
        创建确认对话框

        参数:
            title: 对话框标题
            message: 对话框消息
            on_confirm: 确认回调
            on_cancel: 取消回调（可选）
        """
        dialog_name = f"confirm_{len(self.dialogs)}"
        dialog = self.create_dialog(dialog_name, title, (300, 150))

        # 添加消息文本
        message_text = self.create_text(
            pygame.Rect(
                dialog.content_rect.x,
                dialog.content_rect.y,
                dialog.content_rect.width,
                50
            ),
            message,
            "chinese_normal",
            align="center"
        )
        dialog.add_content(message_text)

        # 添加确认和取消按钮
        button_width = 100
        button_height = 30
        button_y = dialog.content_rect.y + dialog.content_rect.height - button_height - 10

        # 确认按钮
        confirm_button = self.create_button(
            pygame.Rect(
                dialog.content_rect.x + dialog.content_rect.width // 2 - button_width - 10,
                button_y,
                button_width,
                button_height
            ),
            "确认",
            lambda: self._handle_confirm(dialog_name, on_confirm)
        )
        dialog.add_content(confirm_button)

        # 取消按钮
        cancel_action = lambda: self._handle_cancel(dialog_name, on_cancel)
        cancel_button = self.create_button(
            pygame.Rect(
                dialog.content_rect.x + dialog.content_rect.width // 2 + 10,
                button_y,
                button_width,
                button_height
            ),
            "取消",
            cancel_action
        )
        dialog.add_content(cancel_button)

        return dialog

    def _handle_confirm(self, dialog_name: str, callback: Callable[[], Any]):
        """处理确认操作"""
        self.hide_dialog(dialog_name)
        if callback:
            callback()

    def _handle_cancel(self, dialog_name: str, callback: Optional[Callable[[], Any]]):
        """处理取消操作"""
        self.hide_dialog(dialog_name)
        if callback:
            callback()

    def create_message_dialog(self, title: str, message: str,
                               on_close: Optional[Callable[[], Any]] = None) -> Dialog:
        """
        创建消息对话框

        参数:
            title: 对话框标题
            message: 对话框消息
            on_close: 关闭回调（可选）
        """
        try:
            # 确保参数有效
            if title is None:
                title = "信息"
            if message is None:
                message = ""

            dialog_name = f"message_{len(self.dialogs)}"
            dialog = self.create_dialog(dialog_name, title, (350, 180))

            # 添加消息文本
            message_text = self.create_text(
                pygame.Rect(
                    dialog.content_rect.x,
                    dialog.content_rect.y,
                    dialog.content_rect.width,
                    80
                ),
                message,
                "chinese_normal",
                align="center"
            )
            dialog.add_content(message_text)

            # 添加确定按钮
            button_width = 100
            button_height = 30
            button_y = dialog.content_rect.y + dialog.content_rect.height - button_height - 10

            # 确定按钮
            ok_action = lambda: self._handle_message_close(dialog_name, on_close)
            ok_button = self.create_button(
                pygame.Rect(
                    dialog.content_rect.x + (dialog.content_rect.width - button_width) // 2,
                    button_y,
                    button_width,
                    button_height
                ),
                "确定",
                ok_action
            )
            dialog.add_content(ok_button)

            # 覆盖默认关闭按钮行为
            dialog.close_button.action = ok_action

            logger.debug(f"成功创建消息对话框: {dialog_name}")
            return dialog

        except Exception as e:
            logger.error(f"创建消息对话框失败: {e.__class__.__name__}: {e}", exc_info=True)
            return None

    def _handle_message_close(self, dialog_name: str, callback: Optional[Callable[[], Any]]):
        """处理消息对话框关闭"""
        self.hide_dialog(dialog_name)
        if callback:
            callback()

    def show_confirmation(self, title: str, message: str,
                           on_confirm: Callable[[], Any],
                           on_cancel: Optional[Callable[[], Any]] = None):
        """显示确认对话框"""
        dialog = self.create_confirmation_dialog(title, message, on_confirm, on_cancel)
        self.show_dialog(dialog.name)

    def show_message(self, title: str, message: str,
                       on_close: Optional[Callable[[], Any]] = None):
        """显示消息对话框"""
        try:
            # 确保title和message不为None
            if title is None:
                title = "信息"
            if message is None:
                message = ""

            logger.info(f"显示消息对话框 - 标题: '{title}', 消息: '{message}'")
            dialog = self.create_message_dialog(title, message, on_close)
            if dialog:
                self.show_dialog(dialog.name)
                logger.info(f"消息对话框已显示: {dialog.name}")
            else:
                logger.error(f"创建消息对话框失败: 对话框对象为None")
        except Exception as e:
            logger.error(f"显示消息对话框失败: {e.__class__.__name__}: {e}", exc_info=True)
            # 尝试使用简单的方式显示错误信息
            print(f"错误: {title} - {message}")

    def create_timer(self, rect: pygame.Rect, duration_ms: int = 1000) -> Timer:
        """创建计时器的便捷方法"""
        return Timer(rect, duration_ms)

    def add_active_dialog(self, dialog):
        """添加一个自定义的活动对话框"""
        # 将对话框添加到当前活动界面的组件中
        if self.active_screen and self.active_screen in self.screens:
            self.screens[self.active_screen].add_component(dialog)
            logger.info(f"添加自定义对话框到活动界面: {self.active_screen}")
        return dialog

    def create_dropdown(self, rect: pygame.Rect, options: List[str],
                     selected_index: int = 0,
                     callback: Callable[[int], None] = None,
                     font_name: str = "chinese_normal") -> Dropdown:
        """
        创建下拉菜单

        参数:
            rect: 下拉菜单的区域
            options: 选项列表
            selected_index: 默认选中的选项索引
            callback: 选择改变时的回调函数
            font_name: 字体名称

        返回:
            Dropdown: 创建的下拉菜单组件
        """
        # from ui.components import Dropdown # Removed inline import

        # 确保选项列表不为空
        if not options:
            options = ["无选项"]

        # 根据字体名称获取字体
        font = self.get_font(font_name)

        # 设置默认颜色
        colors = {
            "normal": (50, 50, 80),
            "hover": (70, 70, 100),
            "pressed": (40, 40, 70),
            "text": (220, 220, 220),
            "arrow": (180, 180, 180),
            "border": (80, 80, 120),
            "option_bg": (40, 40, 60),
            "option_hover": (60, 60, 90)
        }

        # 创建下拉菜单
        dropdown = Dropdown(rect, options, selected_index, 5, colors, font)

        # 设置回调函数
        if callback:
            dropdown.on_option_select = callback

        return dropdown

    def create_draggable_skill_icon(self, rect: pygame.Rect, text: str,
                        skill_id: Optional[str] = None, slot_idx: int = 0,
                        callback: Optional[Callable[[], Any]] = None) -> 'DraggableSkillIcon':
        """创建可拖动的技能图标

        参数:
            rect: 图标矩形区域
            text: 图标文本
            skill_id: 技能ID
            slot_idx: 槽位索引
            callback: 点击回调函数

        返回:
            创建的技能图标
        """
        # 导入时避免循环引用 (Moved to top - Fixes Issue 1)
        # from ui.components import DraggableSkillIcon

        colors = {
            "normal": (40, 40, 70),
            "hover": (60, 60, 100),
            "dragging": (90, 90, 130),
            "text": (200, 200, 220),
            "border": (80, 80, 120),
        }

        return DraggableSkillIcon(
            rect=rect,
            text=text,
            colors=colors,
            font=self.fonts.get("normal", pygame.font.SysFont(None, 20)),
            skill_id=skill_id,
            slot_idx=slot_idx
        )