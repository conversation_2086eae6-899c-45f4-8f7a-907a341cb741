[2025-05-22 13:11:38] [WARNING] [main.py:105] 游戏启动
[2025-05-22 13:11:38] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 13:11:38] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 13:11:56] [WARNING] [battle.py:867] 战斗日志: 获得了 6 经验值
[2025-05-22 13:11:56] [WARNING] [battle.py:867] 战斗日志: 获得了 120 金币
[2025-05-22 13:12:03] [ERROR] [battle.py:852] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 13:12:03] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 13:12:03] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 859, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

