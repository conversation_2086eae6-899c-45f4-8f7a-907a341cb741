"""
数据库初始化脚本
用于创建必要的集合和索引，以及添加初始数据
"""

import asyncio
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure

from app.config import settings

logger = logging.getLogger(__name__)

async def init_database():
    """初始化数据库"""
    try:
        # 连接到MongoDB
        client = AsyncIOMotorClient(
            settings.MONGO_URI,
            serverSelectionTimeoutMS=settings.MONGO_SERVER_SELECTION_TIMEOUT_MS,
            connectTimeoutMS=settings.MONGO_CONNECT_TIMEOUT_MS
        )
        
        # 测试连接
        await client.admin.command('ping')
        logger.info(f"成功连接到MongoDB: {settings.MONGO_URI}")
        
        # 获取数据库
        db = client[settings.DATABASE_NAME]
        
        # 创建集合（如果不存在）
        if "users" not in await db.list_collection_names():
            await db.create_collection("users")
            logger.info("创建users集合")
        
        if "players" not in await db.list_collection_names():
            await db.create_collection("players")
            logger.info("创建players集合")
        
        if "game_states" not in await db.list_collection_names():
            await db.create_collection("game_states")
            logger.info("创建game_states集合")
        
        # 创建索引
        # 用户集合索引
        await db.users.create_index("username", unique=True)
        await db.users.create_index("email", unique=True)
        logger.info("为users集合创建索引")
        
        # 玩家集合索引
        await db.players.create_index("user_id")
        await db.players.create_index("name")
        logger.info("为players集合创建索引")
        
        # 游戏状态集合索引
        await db.game_states.create_index("player_id", unique=True)
        logger.info("为game_states集合创建索引")
        
        logger.info("数据库初始化完成")
        return True
    except (ServerSelectionTimeoutError, ConnectionFailure) as e:
        logger.error(f"连接MongoDB失败: {e}")
        return False
    except Exception as e:
        logger.error(f"初始化数据库时出错: {e}")
        return False

if __name__ == "__main__":
    # 直接运行此脚本时执行初始化
    asyncio.run(init_database())
