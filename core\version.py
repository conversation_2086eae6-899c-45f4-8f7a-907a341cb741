"""
游戏版本管理模块
"""

# 游戏当前版本号
GAME_VERSION = "1.3.8"

def parse_version(version_str):
    """
    将版本号字符串解析为元组，便于比较

    参数:
        version_str: 版本号字符串，如 "1.2.3"

    返回:
        tuple: 版本号元组，如 (1, 2, 3)
    """
    parts = version_str.split('.')
    return tuple(int(part) for part in parts if part.isdigit())

def compare_versions(version1, version2):
    """
    比较两个版本号

    参数:
        version1: 第一个版本号字符串
        version2: 第二个版本号字符串

    返回:
        -1: 如果version1 < version2
        0: 如果version1 == version2
        1: 如果version1 > version2
    """
    v1_parts = parse_version(version1)
    v2_parts = parse_version(version2)

    # 确保两个版本号元组长度相等，不足的部分补0
    length = max(len(v1_parts), len(v2_parts))
    v1_parts = list(v1_parts) + [0] * (length - len(v1_parts))
    v2_parts = list(v2_parts) + [0] * (length - len(v2_parts))

    # 比较版本号
    for i in range(length):
        if v1_parts[i] < v2_parts[i]:
            return -1
        elif v1_parts[i] > v2_parts[i]:
            return 1

    # 版本号相等
    return 0

def is_compatible(save_version, game_version=GAME_VERSION):
    """
    检查存档版本是否兼容当前游戏版本

    参数:
        save_version: 存档版本号
        game_version: 当前游戏版本号，默认为GAME_VERSION

    返回:
        bool: 如果存档版本低于或等于游戏版本，返回True；否则返回False
    """
    result = compare_versions(save_version, game_version)
    # 存档版本低于或等于游戏版本时兼容
    return result <= 0