('E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\dist\\萝卜放置传奇.exe',
 False,
 False,
 False,
 ['E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets/images/icon.png'],
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
 '(2)\\老版\\build\\build_onefile\\萝卜放置传奇.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\main.py',
   'PYSOURCE'),
  ('pygame\\SDL2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('pygame\\SDL2_image.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_image.dll',
   'BINARY'),
  ('pygame\\SDL2_mixer.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_mixer.dll',
   'BINARY'),
  ('pygame\\SDL2_ttf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_ttf.dll',
   'BINARY'),
  ('pygame\\freetype.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freetype.dll',
   'BINARY'),
  ('pygame\\libjpeg-62.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-62.dll',
   'BINARY'),
  ('pygame\\libjpeg-9.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-9.dll',
   'BINARY'),
  ('pygame\\libmodplug-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libmodplug-1.dll',
   'BINARY'),
  ('pygame\\libogg-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('pygame\\libopus-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('pygame\\libopusfile-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopusfile-0.dll',
   'BINARY'),
  ('pygame\\libpng16-16.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libpng16-16.dll',
   'BINARY'),
  ('pygame\\libtiff-5.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libtiff-5.dll',
   'BINARY'),
  ('pygame\\libwavpack-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwavpack-1.dll',
   'BINARY'),
  ('pygame\\libwebp-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebp-7.dll',
   'BINARY'),
  ('pygame\\libwebpdemux-2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebpdemux-2.dll',
   'BINARY'),
  ('pygame\\libxmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libxmp.dll',
   'BINARY'),
  ('pygame\\portmidi.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\portmidi.dll',
   'BINARY'),
  ('pygame\\zlib1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\zlib1.dll',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('portmidi.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\portmidi.dll',
   'BINARY'),
  ('libxmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libxmp.dll',
   'BINARY'),
  ('libwebpdemux-2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebpdemux-2.dll',
   'BINARY'),
  ('zlib1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\zlib1.dll',
   'BINARY'),
  ('libjpeg-9.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-9.dll',
   'BINARY'),
  ('libwavpack-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwavpack-1.dll',
   'BINARY'),
  ('SDL2_mixer.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_mixer.dll',
   'BINARY'),
  ('SDL2_ttf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_ttf.dll',
   'BINARY'),
  ('SDL2_image.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_image.dll',
   'BINARY'),
  ('libwebp-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebp-7.dll',
   'BINARY'),
  ('libogg-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('libopusfile-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopusfile-0.dll',
   'BINARY'),
  ('SDL2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('freetype.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freetype.dll',
   'BINARY'),
  ('libpng16-16.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libpng16-16.dll',
   'BINARY'),
  ('libopus-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('libmodplug-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libmodplug-1.dll',
   'BINARY'),
  ('libtiff-5.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libtiff-5.dll',
   'BINARY'),
  ('libjpeg-62.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-62.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmorph.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingmorph.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\window.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\window.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\transform.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\transform.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\time.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\time.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pygame\\system.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\system.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surflock.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surflock.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surface.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surface.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\scrap.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\scrap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rwobject.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rwobject.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rect.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rect.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pypm.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pypm.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelcopy.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelcopy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelarray.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelarray.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\newbuffer.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\newbuffer.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mouse.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mouse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer_music.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer_music.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\math.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\math.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mask.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mask.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\key.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\key.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\joystick.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\joystick.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\imageext.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\imageext.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\image.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\image.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\gfxdraw.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\gfxdraw.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\geometry.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\geometry.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\font.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\font.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\event.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\event.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\draw.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\draw.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\display.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\display.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\constants.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\constants.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\color.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\color.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\bufferproxy.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\bufferproxy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\base.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sprite.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sprite.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\video.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\video.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\touch.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\touch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\sdl2.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\sdl2.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\mixer.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\mixer.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\controller_old.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\controller_old.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\controller.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\controller.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\audio.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\audio.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_freetype.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_freetype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_camera.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_camera.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('PIL\\BdfFontFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\BdfFontFile.py',
   'DATA'),
  ('PIL\\BlpImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'DATA'),
  ('PIL\\BmpImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'DATA'),
  ('PIL\\BufrStubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'DATA'),
  ('PIL\\ContainerIO.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ContainerIO.py',
   'DATA'),
  ('PIL\\CurImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'DATA'),
  ('PIL\\DcxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'DATA'),
  ('PIL\\DdsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'DATA'),
  ('PIL\\EpsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'DATA'),
  ('PIL\\ExifTags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ExifTags.py',
   'DATA'),
  ('PIL\\FitsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'DATA'),
  ('PIL\\FliImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'DATA'),
  ('PIL\\FontFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\FontFile.py',
   'DATA'),
  ('PIL\\FpxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'DATA'),
  ('PIL\\FtexImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'DATA'),
  ('PIL\\GbrImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'DATA'),
  ('PIL\\GdImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GdImageFile.py',
   'DATA'),
  ('PIL\\GifImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'DATA'),
  ('PIL\\GimpGradientFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'DATA'),
  ('PIL\\GimpPaletteFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'DATA'),
  ('PIL\\GribStubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'DATA'),
  ('PIL\\Hdf5StubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'DATA'),
  ('PIL\\IcnsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'DATA'),
  ('PIL\\IcoImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'DATA'),
  ('PIL\\ImImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'DATA'),
  ('PIL\\Image.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\Image.py',
   'DATA'),
  ('PIL\\ImageChops.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageChops.py',
   'DATA'),
  ('PIL\\ImageCms.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageCms.py',
   'DATA'),
  ('PIL\\ImageColor.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageColor.py',
   'DATA'),
  ('PIL\\ImageDraw.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'DATA'),
  ('PIL\\ImageDraw2.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'DATA'),
  ('PIL\\ImageEnhance.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageEnhance.py',
   'DATA'),
  ('PIL\\ImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageFile.py',
   'DATA'),
  ('PIL\\ImageFilter.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'DATA'),
  ('PIL\\ImageFont.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageFont.py',
   'DATA'),
  ('PIL\\ImageGrab.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'DATA'),
  ('PIL\\ImageMath.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageMath.py',
   'DATA'),
  ('PIL\\ImageMode.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageMode.py',
   'DATA'),
  ('PIL\\ImageMorph.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageMorph.py',
   'DATA'),
  ('PIL\\ImageOps.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageOps.py',
   'DATA'),
  ('PIL\\ImagePalette.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'DATA'),
  ('PIL\\ImagePath.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImagePath.py',
   'DATA'),
  ('PIL\\ImageQt.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageQt.py',
   'DATA'),
  ('PIL\\ImageSequence.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'DATA'),
  ('PIL\\ImageShow.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageShow.py',
   'DATA'),
  ('PIL\\ImageStat.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageStat.py',
   'DATA'),
  ('PIL\\ImageTk.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageTk.py',
   'DATA'),
  ('PIL\\ImageTransform.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageTransform.py',
   'DATA'),
  ('PIL\\ImageWin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImageWin.py',
   'DATA'),
  ('PIL\\ImtImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'DATA'),
  ('PIL\\IptcImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'DATA'),
  ('PIL\\Jpeg2KImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'DATA'),
  ('PIL\\JpegImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'DATA'),
  ('PIL\\JpegPresets.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'DATA'),
  ('PIL\\McIdasImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'DATA'),
  ('PIL\\MicImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'DATA'),
  ('PIL\\MpegImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'DATA'),
  ('PIL\\MpoImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'DATA'),
  ('PIL\\MspImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'DATA'),
  ('PIL\\PSDraw.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PSDraw.py',
   'DATA'),
  ('PIL\\PaletteFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'DATA'),
  ('PIL\\PalmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'DATA'),
  ('PIL\\PcdImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'DATA'),
  ('PIL\\PcfFontFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PcfFontFile.py',
   'DATA'),
  ('PIL\\PcxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'DATA'),
  ('PIL\\PdfImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'DATA'),
  ('PIL\\PdfParser.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PdfParser.py',
   'DATA'),
  ('PIL\\PixarImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'DATA'),
  ('PIL\\PngImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'DATA'),
  ('PIL\\PpmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'DATA'),
  ('PIL\\PsdImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'DATA'),
  ('PIL\\QoiImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'DATA'),
  ('PIL\\SgiImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'DATA'),
  ('PIL\\SpiderImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'DATA'),
  ('PIL\\SunImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'DATA'),
  ('PIL\\TarIO.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\TarIO.py',
   'DATA'),
  ('PIL\\TgaImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'DATA'),
  ('PIL\\TiffImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'DATA'),
  ('PIL\\TiffTags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\TiffTags.py',
   'DATA'),
  ('PIL\\WalImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\WalImageFile.py',
   'DATA'),
  ('PIL\\WebPImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'DATA'),
  ('PIL\\WmfImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'DATA'),
  ('PIL\\XVThumbImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'DATA'),
  ('PIL\\XbmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'DATA'),
  ('PIL\\XpmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'DATA'),
  ('PIL\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\__init__.py',
   'DATA'),
  ('PIL\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\__main__.py',
   'DATA'),
  ('PIL\\_binary.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_binary.py',
   'DATA'),
  ('PIL\\_deprecate.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_deprecate.py',
   'DATA'),
  ('PIL\\_imaging.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imaging.pyi',
   'DATA'),
  ('PIL\\_imagingcms.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingcms.pyi',
   'DATA'),
  ('PIL\\_imagingft.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingft.pyi',
   'DATA'),
  ('PIL\\_imagingmath.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingmath.pyi',
   'DATA'),
  ('PIL\\_imagingmorph.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingmorph.pyi',
   'DATA'),
  ('PIL\\_imagingtk.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingtk.pyi',
   'DATA'),
  ('PIL\\_tkinter_finder.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_tkinter_finder.py',
   'DATA'),
  ('PIL\\_typing.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_typing.py',
   'DATA'),
  ('PIL\\_util.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_util.py',
   'DATA'),
  ('PIL\\_version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_version.py',
   'DATA'),
  ('PIL\\_webp.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_webp.pyi',
   'DATA'),
  ('PIL\\features.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\features.py',
   'DATA'),
  ('PIL\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\py.typed',
   'DATA'),
  ('PIL\\report.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\report.py',
   'DATA'),
  ('assets\\fonts\\SimHei.ttf',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\fonts\\SimHei.ttf',
   'DATA'),
  ('assets\\fonts\\simsun.ttc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\fonts\\simsun.ttc',
   'DATA'),
  ('assets\\icon.ico',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\icon.ico',
   'DATA'),
  ('assets\\images\\background.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\background.png',
   'DATA'),
  ('assets\\images\\equipment\\勋章\\新手勋章.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\勋章\\新手勋章.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\圣战头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\圣战头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\天尊头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\天尊头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\法神头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\法神头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\祈祷头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\祈祷头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\神秘头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\神秘头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\精灵头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\精灵头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\记忆头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\记忆头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\道士头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\道士头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\青铜头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\青铜头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\骷髅头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\骷髅头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\魔法头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\魔法头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\头盔\\黑铁头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\头盔\\黑铁头盔.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\传送戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\传送戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\六角戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\六角戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\力量戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\力量戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\古铜戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\古铜戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\圣战戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\圣战戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\复活戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\复活戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\天尊戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\天尊戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\护身戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\护身戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\求婚戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\求婚戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\法神戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\法神戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\泰坦戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\泰坦戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\火焰戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\火焰戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\牛角戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\牛角戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\狂风戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\狂风戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\玻璃戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\玻璃戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\珊瑚戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\珊瑚戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\珍珠戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\珍珠戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\生铁戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\生铁戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\祈祷戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\祈祷戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\神秘戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\神秘戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\紫碧螺.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\紫碧螺.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\红宝石戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\红宝石戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\蓝色水晶戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\蓝色水晶戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\虹魔戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\虹魔戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\蛇眼戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\蛇眼戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\记忆戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\记忆戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\超负载戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\超负载戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\道德戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\道德戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\金戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\金戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\铂金戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\铂金戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\防御戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\防御戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\降妖除魔戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\降妖除魔戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\隐身戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\隐身戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\骷髅戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\骷髅戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\魅力戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\魅力戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\魔血戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\魔血戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\麻痹戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\麻痹戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\黑色水晶戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\黑色水晶戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\戒指\\龙之戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\戒指\\龙之戒指.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\三眼手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\三眼手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\圣战手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\圣战手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\坚固手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\坚固手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\夏普儿手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\夏普儿手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\大手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\大手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\天尊手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\天尊手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\小手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\小手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\幽灵手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\幽灵手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\心灵手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\心灵手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\思贝儿手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\思贝儿手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\死神手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\死神手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\法神手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\法神手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\皮制手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\皮制手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\祈祷手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\祈祷手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\神秘腰带.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\神秘腰带.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\虹魔手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\虹魔手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\记忆手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\记忆手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\辟邪手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\辟邪手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\道士手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\道士手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\金手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\金手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\钢手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\钢手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\铁手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\铁手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\银手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\银手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\阎罗手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\阎罗手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\骑士手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\骑士手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\魔力手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\魔力手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\魔法手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\魔法手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\魔血手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\魔血手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\黑檀手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\黑檀手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\黑铁手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\黑铁手套.png',
   'DATA'),
  ('assets\\images\\equipment\\手镯\\龙之手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\手镯\\龙之手镯.png',
   'DATA'),
  ('assets\\images\\equipment\\技能书\\技能书.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\技能书\\技能书.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\乌木剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\乌木剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\井中月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\井中月.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\修罗.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\修罗.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\偃月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\偃月.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\八荒.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\八荒.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\凌风.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\凌风.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\凝霜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\凝霜.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\匕首.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\匕首.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\半月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\半月.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\命运之刃.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\命运之刃.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\嗜魂法杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\嗜魂法杖.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\屠龙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\屠龙.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\怒斩.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\怒斩.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\斩马刀.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\斩马刀.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\无极棍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\无极棍.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\木剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\木剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\海魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\海魂.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\炼狱.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\炼狱.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\短剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\短剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\破魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\破魂.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\祈祷之刃.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\祈祷之刃.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\罗刹.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\罗刹.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\落魄神兵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\落魄神兵.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\血饮.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\血饮.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\裁决之杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\裁决之杖.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\赤血魔剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\赤血魔剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\逍遥扇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\逍遥扇.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\铁剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\铁剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\银蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\银蛇.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\降魔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\降魔.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\青铜剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\青铜剑.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\青铜斧.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\青铜斧.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\骨玉权杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\骨玉权杖.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\魔杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\魔杖.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\鹤嘴锄.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\鹤嘴锄.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\龙牙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\龙牙.png',
   'DATA'),
  ('assets\\images\\equipment\\武器\\龙纹剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\武器\\龙纹剑.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\万年雪霜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\万年雪霜.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\体力强效神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\体力强效神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\修复油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\修复油.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\地牢逃脱卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\地牢逃脱卷.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\太阳水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\太阳水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\强效太阳水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\强效太阳水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\强效金创药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\强效金创药.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\强效魔法药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\强效魔法药.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\彩票.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\彩票.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\战神油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\战神油.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\攻击神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\攻击神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\特殊药水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\特殊药水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\疗伤药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\疗伤药.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\疾风神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\疾风神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\祝福油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\祝福油.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\精神神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\精神神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\苹果.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\苹果.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\行会回城卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\行会回城卷.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\许中医的药1.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\许中医的药1.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\金创药(中量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\金创药(中量).png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\金创药(小量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\金创药(小量).png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\金矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\金矿.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\铁矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\铁矿.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\铜矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\铜矿.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\银矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\银矿.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\随机传送卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\随机传送卷.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\魔力强效神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\魔力强效神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\魔力神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\魔力神水.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\魔法药(中量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\魔法药(中量).png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\魔法药(小量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\魔法药(小量).png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\鹿茸.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\鹿茸.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\鹿血.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\鹿血.png',
   'DATA'),
  ('assets\\images\\equipment\\消耗品\\黑铁矿石.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\消耗品\\黑铁矿石.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\中型盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\中型盔甲(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\中型盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\中型盔甲(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\圣战宝甲.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\圣战宝甲.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\天尊道袍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\天尊道袍.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\天师长袍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\天师长袍.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\天魔神甲.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\天魔神甲.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\布衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\布衣(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\布衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\布衣(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\幽灵战衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\幽灵战衣(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\幽灵战衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\幽灵战衣(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\恶魔长袍(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\恶魔长袍(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\恶魔长袍(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\恶魔长袍(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\战神盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\战神盔甲(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\战神盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\战神盔甲(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\法神披风.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\法神披风.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\灵魂战衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\灵魂战衣(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\灵魂战衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\灵魂战衣(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\轻型盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\轻型盔甲(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\轻型盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\轻型盔甲(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\重盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\重盔甲(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\重盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\重盔甲(男).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\霓裳羽衣.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\霓裳羽衣.png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\魔法长袍(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\魔法长袍(女).png',
   'DATA'),
  ('assets\\images\\equipment\\防具\\魔法长袍(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\防具\\魔法长袍(男).png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\传统项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\传统项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\凤凰明珠.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\凤凰明珠.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\圣战项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\圣战项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\天尊项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\天尊项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\天珠项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\天珠项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\幽灵项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\幽灵项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\恶魔铃铛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\恶魔铃铛.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\技巧项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\技巧项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\探测项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\探测项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\放大镜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\放大镜.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\法神项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\法神项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\灯笼项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\灯笼项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\灵魂项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\灵魂项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\狂风项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\狂风项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\琥珀项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\琥珀项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\生命项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\生命项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\白色虎齿项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\白色虎齿项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\白金项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\白金项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\祈祷项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\祈祷项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\祈福项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\祈福项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\竹笛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\竹笛.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\绿色项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\绿色项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\蓝翡翠项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\蓝翡翠项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\虹魔项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\虹魔项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\记忆项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\记忆项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\躲避手链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\躲避手链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\金项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\金项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\魔血项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\魔血项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\魔鬼项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\魔鬼项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\黄色水晶项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\黄色水晶项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\黑檀项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\黑檀项链.png',
   'DATA'),
  ('assets\\images\\equipment\\项链\\黑色水晶项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\equipment\\项链\\黑色水晶项链.png',
   'DATA'),
  ('assets\\images\\icon.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\images\\icon.png',
   'DATA'),
  ('assets\\images\\monsters\\0095.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\0095.png',
   'DATA'),
  ('assets\\images\\monsters\\0096.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\0096.png',
   'DATA'),
  ('assets\\images\\monsters\\0097.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\0097.png',
   'DATA'),
  ('assets\\images\\monsters\\僵尸.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\僵尸.png',
   'DATA'),
  ('assets\\images\\monsters\\半兽人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\半兽人.png',
   'DATA'),
  ('assets\\images\\monsters\\半兽勇士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\半兽勇士.png',
   'DATA'),
  ('assets\\images\\monsters\\半兽战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\半兽战士.png',
   'DATA'),
  ('assets\\images\\monsters\\多钩猫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\多钩猫.png',
   'DATA'),
  ('assets\\images\\monsters\\天怒魔王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\天怒魔王.png',
   'DATA'),
  ('assets\\images\\monsters\\尸王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\尸王.png',
   'DATA'),
  ('assets\\images\\monsters\\山洞蝙蝠.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\山洞蝙蝠.png',
   'DATA'),
  ('assets\\images\\monsters\\巨型蠕虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\巨型蠕虫.png',
   'DATA'),
  ('assets\\images\\monsters\\掷斧骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\掷斧骷髅.png',
   'DATA'),
  ('assets\\images\\monsters\\断刃战魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\断刃战魂.png',
   'DATA'),
  ('assets\\images\\monsters\\暗黑战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\暗黑战士.png',
   'DATA'),
  ('assets\\images\\monsters\\森林雪人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\森林雪人.png',
   'DATA'),
  ('assets\\images\\monsters\\毒蜘蛛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\毒蜘蛛.png',
   'DATA'),
  ('assets\\images\\monsters\\沃玛勇士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\沃玛勇士.png',
   'DATA'),
  ('assets\\images\\monsters\\沃玛卫士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\沃玛卫士.png',
   'DATA'),
  ('assets\\images\\monsters\\沃玛战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\沃玛战士.png',
   'DATA'),
  ('assets\\images\\monsters\\沃玛战将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\沃玛战将.png',
   'DATA'),
  ('assets\\images\\monsters\\沃玛教主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\沃玛教主.png',
   'DATA'),
  ('assets\\images\\monsters\\洞蛆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\洞蛆.png',
   'DATA'),
  ('assets\\images\\monsters\\火焰沃玛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\火焰沃玛.png',
   'DATA'),
  ('assets\\images\\monsters\\焚炉恶卒.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\焚炉恶卒.png',
   'DATA'),
  ('assets\\images\\monsters\\牛头魔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\牛头魔.png',
   'DATA'),
  ('assets\\images\\monsters\\牛魔将军.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\牛魔将军.png',
   'DATA'),
  ('assets\\images\\monsters\\牛魔法师.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\牛魔法师.png',
   'DATA'),
  ('assets\\images\\monsters\\牛魔王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\牛魔王.png',
   'DATA'),
  ('assets\\images\\monsters\\牛魔祭司.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\牛魔祭司.png',
   'DATA'),
  ('assets\\images\\monsters\\玄铁邪灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\玄铁邪灵.png',
   'DATA'),
  ('assets\\images\\monsters\\玉石魔怪.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\玉石魔怪.png',
   'DATA'),
  ('assets\\images\\monsters\\神匠魂主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\神匠魂主.png',
   'DATA'),
  ('assets\\images\\monsters\\稻草人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\稻草人.png',
   'DATA'),
  ('assets\\images\\monsters\\粪虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\粪虫.png',
   'DATA'),
  ('assets\\images\\monsters\\红蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\红蛇.png',
   'DATA'),
  ('assets\\images\\monsters\\羊.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\羊.png',
   'DATA'),
  ('assets\\images\\monsters\\虎蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\虎蛇.png',
   'DATA'),
  ('assets\\images\\monsters\\虹魔教主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\虹魔教主.png',
   'DATA'),
  ('assets\\images\\monsters\\虹魔猪卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\虹魔猪卫.png',
   'DATA'),
  ('assets\\images\\monsters\\虹魔蝎卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\虹魔蝎卫.png',
   'DATA'),
  ('assets\\images\\monsters\\蚀金魔将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\蚀金魔将.png',
   'DATA'),
  ('assets\\images\\monsters\\蛤蟆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\蛤蟆.png',
   'DATA'),
  ('assets\\images\\monsters\\蜈蚣.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\蜈蚣.png',
   'DATA'),
  ('assets\\images\\monsters\\蝎子.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\蝎子.png',
   'DATA'),
  ('assets\\images\\monsters\\触龙神.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\触龙神.png',
   'DATA'),
  ('assets\\images\\monsters\\贪蟾魔将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\贪蟾魔将.png',
   'DATA'),
  ('assets\\images\\monsters\\赤铜邪灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\赤铜邪灵.png',
   'DATA'),
  ('assets\\images\\monsters\\跳跳蜂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\跳跳蜂.png',
   'DATA'),
  ('assets\\images\\monsters\\邪恶钳虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\邪恶钳虫.png',
   'DATA'),
  ('assets\\images\\monsters\\金甲护卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\金甲护卫.png',
   'DATA'),
  ('assets\\images\\monsters\\钉耙猫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\钉耙猫.png',
   'DATA'),
  ('assets\\images\\monsters\\钳虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\钳虫.png',
   'DATA'),
  ('assets\\images\\monsters\\锻魔督军.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\锻魔督军.png',
   'DATA'),
  ('assets\\images\\monsters\\镇宝龙王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\镇宝龙王.png',
   'DATA'),
  ('assets\\images\\monsters\\食人花.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\食人花.png',
   'DATA'),
  ('assets\\images\\monsters\\骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\骷髅.png',
   'DATA'),
  ('assets\\images\\monsters\\骷髅战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\骷髅战士.png',
   'DATA'),
  ('assets\\images\\monsters\\骷髅战将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\骷髅战将.png',
   'DATA'),
  ('assets\\images\\monsters\\骷髅精灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\骷髅精灵.png',
   'DATA'),
  ('assets\\images\\monsters\\鸡.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\鸡.png',
   'DATA'),
  ('assets\\images\\monsters\\鹿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\鹿.png',
   'DATA'),
  ('assets\\images\\monsters\\黑色恶蛆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\monsters\\黑色恶蛆.png',
   'DATA'),
  ('assets\\images\\skill\\冰咆哮.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\冰咆哮.png',
   'DATA'),
  ('assets\\images\\skill\\半月弯刀.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\半月弯刀.png',
   'DATA'),
  ('assets\\images\\skill\\召唤神兽.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\召唤神兽.png',
   'DATA'),
  ('assets\\images\\skill\\召唤骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\召唤骷髅.png',
   'DATA'),
  ('assets\\images\\skill\\圣灵防御术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\圣灵防御术.png',
   'DATA'),
  ('assets\\images\\skill\\地狱雷光.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\地狱雷光.png',
   'DATA'),
  ('assets\\images\\skill\\抗拒火环.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\抗拒火环.png',
   'DATA'),
  ('assets\\images\\skill\\施毒术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\施毒术.png',
   'DATA'),
  ('assets\\images\\skill\\治愈术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\治愈术.png',
   'DATA'),
  ('assets\\images\\skill\\火墙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\火墙.png',
   'DATA'),
  ('assets\\images\\skill\\火球术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\火球术.png',
   'DATA'),
  ('assets\\images\\skill\\灵魂火符.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\灵魂火符.png',
   'DATA'),
  ('assets\\images\\skill\\烈火剑法.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\烈火剑法.png',
   'DATA'),
  ('assets\\images\\skill\\诱惑之光.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\诱惑之光.png',
   'DATA'),
  ('assets\\images\\skill\\野蛮冲撞.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\野蛮冲撞.png',
   'DATA'),
  ('assets\\images\\skill\\隐身术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\隐身术.png',
   'DATA'),
  ('assets\\images\\skill\\雷电术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\雷电术.png',
   'DATA'),
  ('assets\\images\\skill\\魔法盾.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\skill\\魔法盾.png',
   'DATA'),
  ('assets\\images\\summon\\白骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\summon\\白骷髅.png',
   'DATA'),
  ('assets\\images\\summon\\神兽.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\assets\\images\\summon\\神兽.png',
   'DATA'),
  ('assets\\map\\封魔谷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\封魔谷.png',
   'DATA'),
  ('assets\\map\\毒蛇山谷.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\毒蛇山谷.jpg',
   'DATA'),
  ('assets\\map\\比奇省.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\比奇省.jpg',
   'DATA'),
  ('assets\\map\\比奇矿区.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\比奇矿区.jpg',
   'DATA'),
  ('assets\\map\\沃玛寺庙.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\沃玛寺庙.jpg',
   'DATA'),
  ('assets\\map\\沃玛森林.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\沃玛森林.jpg',
   'DATA'),
  ('assets\\map\\石墓.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\石墓.jpg',
   'DATA'),
  ('assets\\map\\祖玛寺庙.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\祖玛寺庙.jpg',
   'DATA'),
  ('assets\\map\\蜈蚣洞.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\蜈蚣洞.jpg',
   'DATA'),
  ('assets\\map\\骷髅洞.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\assets\\map\\骷髅洞.jpg',
   'DATA'),
  ('core\\__init__.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\__init__.py',
   'DATA'),
  ('core\\__pycache__\\__init__.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\battle.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\battle.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\battle.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\battle.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\class_stats.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\class_stats.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\class_stats.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\class_stats.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\config.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\config.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\config.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\game.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\game.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\game.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\game.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\monster.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\monster.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\monster.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\monster.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\player.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\player.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\player.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\player.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\skill_manager.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\skill_manager.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\skill_manager.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\skill_manager.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\summon.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\summon.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\summon.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\summon.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\version.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\version.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\version.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\core\\__pycache__\\version.cpython-313.pyc',
   'DATA'),
  ('core\\backup\\game.py.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\backup\\game.py.bak',
   'DATA'),
  ('core\\battle.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\battle.py',
   'DATA'),
  ('core\\class_stats.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\class_stats.py',
   'DATA'),
  ('core\\config.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\config.py',
   'DATA'),
  ('core\\game.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\game.py',
   'DATA'),
  ('core\\monster.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\monster.py',
   'DATA'),
  ('core\\player.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\player.py',
   'DATA'),
  ('core\\secure_value.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\secure_value.py',
   'DATA'),
  ('core\\skill_manager.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\skill_manager.py',
   'DATA'),
  ('core\\summon.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\summon.py',
   'DATA'),
  ('core\\types.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\types.py',
   'DATA'),
  ('core\\version.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\core\\version.py',
   'DATA'),
  ('data\\backups\\11_save_20250328_213615.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250328_213615.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_011736.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_011736.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012113.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012113.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012410.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012410.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012507.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012507.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012557.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012557.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012717.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012717.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_012849.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_012849.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_111028.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_111028.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_111237.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_111237.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_111739.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_111739.bak.json',
   'DATA'),
  ('data\\backups\\11_save_20250329_120607.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\11_save_20250329_120607.bak.json',
   'DATA'),
  ('data\\backups\\cesh_save_20250404_084413.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\cesh_save_20250404_084413.bak.json',
   'DATA'),
  ('data\\backups\\cesh_save_20250404_084659.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\cesh_save_20250404_084659.bak.json',
   'DATA'),
  ('data\\backups\\manual_fix\\00_save.json.bak_1744116145',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\00_save.json.bak_1744116145',
   'DATA'),
  ('data\\backups\\manual_fix\\111_save.json.bak_1744105104',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\111_save.json.bak_1744105104',
   'DATA'),
  ('data\\backups\\manual_fix\\pp_save.json.bak_1744111472',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\pp_save.json.bak_1744111472',
   'DATA'),
  ('data\\backups\\manual_fix\\shop_refresh.json.bak_1744942546',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\shop_refresh.json.bak_1744942546',
   'DATA'),
  ('data\\backups\\manual_fix\\shop_refresh.json.bak_1744942548',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\shop_refresh.json.bak_1744942548',
   'DATA'),
  ('data\\backups\\manual_fix\\测试1_save.json.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试1_save.json.bak',
   'DATA'),
  ('data\\backups\\manual_fix\\测试_save.json.bak_1744110773',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试_save.json.bak_1744110773',
   'DATA'),
  ('data\\backups\\manual_fix\\测试_save.json.bak_1744113109',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试_save.json.bak_1744113109',
   'DATA'),
  ('data\\backups\\manual_fix\\测试_save.json.bak_1744113735',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试_save.json.bak_1744113735',
   'DATA'),
  ('data\\backups\\manual_fix\\测试_save.json.bak_1744115955',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试_save.json.bak_1744115955',
   'DATA'),
  ('data\\backups\\manual_fix\\测试存档_save.json.bak_1744113815',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\manual_fix\\测试存档_save.json.bak_1744113815',
   'DATA'),
  ('data\\backups\\monsters_20250416_190932.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\monsters_20250416_190932.json',
   'DATA'),
  ('data\\backups\\save.json_20250327_011223.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_011223.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_011510.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_011510.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_012555.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_012555.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_071847.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_071847.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_071919.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_071919.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_073441.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_073441.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_114148.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_114148.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_115707.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_115707.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_124441.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_124441.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_131737.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_131737.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_135412.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_135412.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_145248.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_145248.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_145538.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_145538.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_150456.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_150456.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_150505.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_150505.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_150533.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_150533.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_150713.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_150713.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_152809.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_152809.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_154317.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_154317.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_180522.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_180522.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_183123.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_183123.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_183718.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_183718.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_183852.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_183852.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_184101.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_184101.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_184335.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_184335.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_185130.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_185130.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_185630.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_185630.bak',
   'DATA'),
  ('data\\backups\\save.json_20250327_185813.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save.json_20250327_185813.bak',
   'DATA'),
  ('data\\backups\\save_20250327_211056.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_211056.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_211107.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_211107.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_211210.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_211210.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_211218.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_211218.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_211358.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_211358.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_214213.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_214213.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_214647.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_214647.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_215520.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_215520.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_215936.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_215936.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_220348.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_220348.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_220637.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_220637.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_220826.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_220826.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_221417.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_221417.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_221752.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_221752.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_222102.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_222102.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_222121.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_222121.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_223324.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_223324.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_223611.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_223611.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_224120.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_224120.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_225008.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_225008.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_225317.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_225317.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_225845.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_225845.bak.json',
   'DATA'),
  ('data\\backups\\save_20250327_231941.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\save_20250327_231941.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_211004.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_211004.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_211243.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_211243.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_211617.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_211617.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_212123.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_212123.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_212450.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_212450.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_212933.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_212933.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_213252.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_213252.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_213507.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_213507.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_213626.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_213626.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_213919.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_213919.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_225627.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_225627.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_233228.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_233228.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_233614.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_233614.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_233837.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_233837.bak.json',
   'DATA'),
  ('data\\backups\\啊_save_20250403_233845.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\啊_save_20250403_233845.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_001332.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_001332.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_002418.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_002418.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_002431.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_002431.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_080303.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_080303.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_082008.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_082008.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_082016.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_082016.bak.json',
   'DATA'),
  ('data\\backups\\测试_save_20250404_093425.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\测试_save_20250404_093425.bak.json',
   'DATA'),
  ('data\\backups\\萝卜_save_20250328_082711.bak.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\backups\\萝卜_save_20250328_082711.bak.json',
   'DATA'),
  ('data\\configs\\consumables.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\consumables.json',
   'DATA'),
  ('data\\configs\\drop_rates.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\drop_rates.json',
   'DATA'),
  ('data\\configs\\equipment.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\equipment.json',
   'DATA'),
  ('data\\configs\\maps.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\data\\configs\\maps.json',
   'DATA'),
  ('data\\configs\\monsters.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\monsters.json',
   'DATA'),
  ('data\\configs\\shop_items.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\shop_items.json',
   'DATA'),
  ('data\\configs\\skillbooks.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\skillbooks.json',
   'DATA'),
  ('data\\configs\\skills.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\skills.json',
   'DATA'),
  ('data\\configs\\summons.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\configs\\summons.json',
   'DATA'),
  ('data\\saves\\signin_records(1).json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\signin_records(1).json',
   'DATA'),
  ('data\\saves\\signin_records.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\signin_records.json',
   'DATA'),
  ('data\\saves\\signin_records_测试11.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\signin_records_测试11.json',
   'DATA'),
  ('data\\saves\\signin_records_测试角色1.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\signin_records_测试角色1.json',
   'DATA'),
  ('data\\saves\\signin_records_测试角色2.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\signin_records_测试角色2.json',
   'DATA'),
  ('data\\saves\\测试11_save.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\测试11_save.json',
   'DATA'),
  ('data\\saves\\测试11_save.json.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\测试11_save.json.bak',
   'DATA'),
  ('data\\saves\\测试赛_save.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\测试赛_save.json',
   'DATA'),
  ('data\\saves\\测试赛_save.json.bak',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\data\\saves\\测试赛_save.json.bak',
   'DATA'),
  ('data\\translations.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\data\\translations.json',
   'DATA'),
  ('pillow-11.1.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('pillow-11.1.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\LICENSE',
   'DATA'),
  ('pillow-11.1.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\METADATA',
   'DATA'),
  ('pillow-11.1.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\RECORD',
   'DATA'),
  ('pillow-11.1.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('pillow-11.1.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\WHEEL',
   'DATA'),
  ('pillow-11.1.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('pillow-11.1.0.dist-info\\zip-safe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pillow-11.1.0.dist-info\\zip-safe',
   'DATA'),
  ('pygame-2.6.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\INSTALLER',
   'DATA'),
  ('pygame-2.6.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\METADATA',
   'DATA'),
  ('pygame-2.6.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\RECORD',
   'DATA'),
  ('pygame-2.6.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\REQUESTED',
   'DATA'),
  ('pygame-2.6.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\WHEEL',
   'DATA'),
  ('pygame-2.6.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\entry_points.txt',
   'DATA'),
  ('pygame-2.6.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame-2.6.1.dist-info\\top_level.txt',
   'DATA'),
  ('pygame\\__briefcase\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__briefcase\\__init__.py',
   'DATA'),
  ('pygame\\__briefcase\\pygame_ce.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__briefcase\\pygame_ce.py',
   'DATA'),
  ('pygame\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__init__.py',
   'DATA'),
  ('pygame\\__init__.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__init__.pyi',
   'DATA'),
  ('pygame\\__pyinstaller\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__pyinstaller\\__init__.py',
   'DATA'),
  ('pygame\\__pyinstaller\\hook-pygame.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\__pyinstaller\\hook-pygame.py',
   'DATA'),
  ('pygame\\_camera_opencv.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_camera_opencv.py',
   'DATA'),
  ('pygame\\_camera_vidcapture.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_camera_vidcapture.py',
   'DATA'),
  ('pygame\\_common.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_common.pyi',
   'DATA'),
  ('pygame\\_data_classes.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_data_classes.py',
   'DATA'),
  ('pygame\\_debug.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_debug.py',
   'DATA'),
  ('pygame\\_debug.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_debug.pyi',
   'DATA'),
  ('pygame\\_sdl2\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\__init__.py',
   'DATA'),
  ('pygame\\_sdl2\\__init__.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\__init__.pyi',
   'DATA'),
  ('pygame\\_sdl2\\audio.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\audio.pyi',
   'DATA'),
  ('pygame\\_sdl2\\controller.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\controller.pyi',
   'DATA'),
  ('pygame\\_sdl2\\controller_old.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\controller_old.pyi',
   'DATA'),
  ('pygame\\_sdl2\\sdl2.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\sdl2.pyi',
   'DATA'),
  ('pygame\\_sdl2\\touch.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\touch.pyi',
   'DATA'),
  ('pygame\\_sdl2\\video.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\video.pyi',
   'DATA'),
  ('pygame\\_sdl2\\window.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\window.py',
   'DATA'),
  ('pygame\\_sdl2\\window.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\window.pyi',
   'DATA'),
  ('pygame\\_sprite.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sprite.py',
   'DATA'),
  ('pygame\\base.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\base.pyi',
   'DATA'),
  ('pygame\\bufferproxy.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\bufferproxy.pyi',
   'DATA'),
  ('pygame\\camera.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\camera.py',
   'DATA'),
  ('pygame\\camera.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\camera.pyi',
   'DATA'),
  ('pygame\\color.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\color.pyi',
   'DATA'),
  ('pygame\\colordict.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\colordict.py',
   'DATA'),
  ('pygame\\constants.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\constants.pyi',
   'DATA'),
  ('pygame\\cursors.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\cursors.py',
   'DATA'),
  ('pygame\\cursors.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\cursors.pyi',
   'DATA'),
  ('pygame\\display.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\display.pyi',
   'DATA'),
  ('pygame\\docs\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\__init__.py',
   'DATA'),
  ('pygame\\docs\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\__main__.py',
   'DATA'),
  ('pygame\\docs\\generated\\LGPL.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\LGPL.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput1.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput11.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput11.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput2.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput2.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput21.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput21.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput3.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput3.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput31.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput31.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput4.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput4.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput41.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput41.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput5.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput5.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedInputOutput51.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedInputOutput51.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha1.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha11.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha11.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha2.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha2.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha21.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha21.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha3.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha3.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputAlpha31.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputAlpha31.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess1.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess11.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess11.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess2.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess2.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess21.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess21.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess3.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess3.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess31.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess31.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess4.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess4.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess41.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess41.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess5.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess5.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess51.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess51.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess6.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess6.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\AdvancedOutputProcess61.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\AdvancedOutputProcess61.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-INPUT-resultscreen.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-INPUT-resultscreen.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-INPUT-resultscreen1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-INPUT-resultscreen1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-INPUT-sourcecode.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-INPUT-sourcecode.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-INPUT-sourcecode1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-INPUT-sourcecode1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-PROCESS-resultscreen.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-PROCESS-resultscreen.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-PROCESS-resultscreen1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-PROCESS-resultscreen1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-PROCESS-sourcecode.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-PROCESS-sourcecode.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-PROCESS-sourcecode1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-PROCESS-sourcecode1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-ouput-result-screen.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-ouput-result-screen.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Bagic-ouput-result-screen1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Bagic-ouput-result-screen1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Basic-ouput-sourcecode.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Basic-ouput-sourcecode.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\Basic-ouput-sourcecode1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\Basic-ouput-sourcecode1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\angle_to.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\angle_to.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_average.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_average.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_background.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_background.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_green.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_green.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_hsv.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_hsv.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_mask.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_mask.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_rgb.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_rgb.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_thresh.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_thresh.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_thresholded.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_thresholded.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\camera_yuv.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\camera_yuv.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\chimpshot.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\chimpshot.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\draw_module_example.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\draw_module_example.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\intro_ball.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\intro_ball.gif',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\intro_blade.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\intro_blade.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\intro_freedom.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\intro_freedom.jpg',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-Battleship.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-Battleship.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-Battleship1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-Battleship1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-PuyoPuyo.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-PuyoPuyo.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-PuyoPuyo1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-PuyoPuyo1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-TPS.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-TPS.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\introduction-TPS1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\introduction-TPS1.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\joystick_calls.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\joystick_calls.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\premultiplied_alpha_composition.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\premultiplied_alpha_composition.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_ce_lofi.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_ce_lofi.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_ce_logo.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_ce_logo.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_ce_powered.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_ce_powered.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_ce_powered_lowres.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_ce_powered_lowres.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_ce_tiny.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_ce_tiny.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_lofi.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_lofi.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_logo.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_logo.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_powered.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_powered.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_powered_lowres.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_powered_lowres.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\pygame_tiny.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\pygame_tiny.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\straight_alpha_composition.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\straight_alpha_composition.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_allblack.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_allblack.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_flipped.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_flipped.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_redimg.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_redimg.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_rgbarray.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_rgbarray.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_scaledown.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_scaledown.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_scaleup.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_scaleup.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_soften.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_soften.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_striped.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_striped.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\surfarray_xfade.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\surfarray_xfade.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\tom_basic.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\tom_basic.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\tom_event-flowchart.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\tom_event-flowchart.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\tom_formulae.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\tom_formulae.png',
   'DATA'),
  ('pygame\\docs\\generated\\_images\\tom_radians.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_images\\tom_radians.png',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\c_api.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\c_api.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\filepaths.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\filepaths.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\index.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\index.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\logos.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\logos.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\bufferproxy.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\bufferproxy.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\camera.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\camera.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\cdrom.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\cdrom.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\color.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\color.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\color_list.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\color_list.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\cursors.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\cursors.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\display.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\display.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\draw.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\draw.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\event.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\event.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\examples.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\examples.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\fastevent.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\fastevent.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\font.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\font.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\freetype.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\freetype.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\geometry.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\geometry.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\gfxdraw.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\gfxdraw.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\image.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\image.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\joystick.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\joystick.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\key.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\key.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\locals.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\locals.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\mask.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\mask.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\math.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\math.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\midi.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\midi.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\mixer.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\mixer.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\mouse.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\mouse.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\music.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\music.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\overlay.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\overlay.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\pixelarray.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\pixelarray.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\pixelcopy.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\pixelcopy.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\pygame.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\pygame.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\rect.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\rect.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\scrap.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\scrap.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\sdl2_controller.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\sdl2_controller.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\sdl2_video.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\sdl2_video.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\sndarray.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\sndarray.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\special_flags_list.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\special_flags_list.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\sprite.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\sprite.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\surface.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\surface.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\surfarray.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\surfarray.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\system.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\system.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\tests.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\tests.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\time.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\time.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\touch.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\touch.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\transform.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\transform.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\typing.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\typing.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_sources\\ref\\window.rst.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_sources\\ref\\window.rst.txt',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\basic.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\basic.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\dark-theme-icon.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\dark-theme-icon.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\doctools.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\doctools.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\documentation_options.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\documentation_options.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\file.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\file.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\graphviz.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\graphviz.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\language_data.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\language_data.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\legacy_logos.zip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\legacy_logos.zip',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\light-theme-icon.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\light-theme-icon.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\minus.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\minus.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\plus.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\plus.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame.ico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame.ico',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_lofi.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_lofi.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_lofi.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_lofi.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_logo.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_logo.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_logo.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_logo.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_powered.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_powered.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_powered.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_powered.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_powered_lowres.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_powered_lowres.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_ce_tiny.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_ce_tiny.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_lofi.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_lofi.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_lofi.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_lofi.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_logo.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_logo.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_logo.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_logo.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_powered.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_powered.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_powered.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_powered.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_powered_lowres.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_powered_lowres.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygame_tiny.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygame_tiny.png',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\pygments.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\pygments.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\reset.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\reset.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\script.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\script.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\searchbar-icon.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\searchbar-icon.svg',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\searchtools.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\searchtools.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\sphinx_highlight.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\sphinx_highlight.js',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\tooltip.css',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\tooltip.css',
   'DATA'),
  ('pygame\\docs\\generated\\_static\\upstream_logos.zip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\_static\\upstream_logos.zip',
   'DATA'),
  ('pygame\\docs\\generated\\c_api.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\base.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\base.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\bufferproxy.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\bufferproxy.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\color.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\color.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\display.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\display.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\event.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\event.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\freetype.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\freetype.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\joystick.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\joystick.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\mixer.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\mixer.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\rect.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\rect.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\rwobject.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\rwobject.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\slots.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\slots.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\surface.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\surface.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\surflock.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\surflock.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\version.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\version.html',
   'DATA'),
  ('pygame\\docs\\generated\\c_api\\window.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\c_api\\window.html',
   'DATA'),
  ('pygame\\docs\\generated\\filepaths.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\filepaths.html',
   'DATA'),
  ('pygame\\docs\\generated\\genindex.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\genindex.html',
   'DATA'),
  ('pygame\\docs\\generated\\index.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\index.html',
   'DATA'),
  ('pygame\\docs\\generated\\logos.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\logos.html',
   'DATA'),
  ('pygame\\docs\\generated\\py-modindex.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\py-modindex.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\bufferproxy.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\bufferproxy.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\camera.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\camera.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\cdrom.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\cdrom.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\color.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\color.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\color_list.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\color_list.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\cursors.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\cursors.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\display.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\display.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\draw.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\draw.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\event.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\event.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\examples.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\examples.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\fastevent.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\fastevent.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\font.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\font.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\freetype.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\freetype.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\geometry.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\geometry.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\gfxdraw.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\gfxdraw.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\image.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\image.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\joystick.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\joystick.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\key.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\key.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\locals.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\locals.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\mask.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\mask.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\math.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\math.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\midi.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\midi.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\mixer.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\mixer.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\mouse.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\mouse.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\music.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\music.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\overlay.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\overlay.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\pixelarray.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\pixelarray.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\pixelcopy.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\pixelcopy.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\pygame.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\pygame.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\rect.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\rect.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\scrap.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\scrap.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\sdl2_controller.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\sdl2_controller.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\sdl2_video.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\sdl2_video.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\sndarray.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\sndarray.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\special_flags_list.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\special_flags_list.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\sprite.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\sprite.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\surface.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\surface.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\surfarray.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\surfarray.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\system.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\system.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\tests.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\tests.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\time.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\time.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\touch.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\touch.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\transform.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\transform.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\typing.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\typing.html',
   'DATA'),
  ('pygame\\docs\\generated\\ref\\window.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\ref\\window.html',
   'DATA'),
  ('pygame\\docs\\generated\\search.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\search.html',
   'DATA'),
  ('pygame\\docs\\generated\\searchindex.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\searchindex.js',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\CameraIntro.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\CameraIntro.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\ChimpLineByLine.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\ChimpLineByLine.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\DisplayModes.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\DisplayModes.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\ImportInit.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\ImportInit.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\MakeGames.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\MakeGames.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\MoveIt.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\MoveIt.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\PygameIntro.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\PygameIntro.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\SpriteIntro.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\SpriteIntro.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\SurfarrayIntro.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\SurfarrayIntro.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\chimp.py.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\chimp.py.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\newbieguide.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\newbieguide.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\tom_games2.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\tom_games2.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\tom_games3.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\tom_games3.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\tom_games4.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\tom_games4.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\tom_games5.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\tom_games5.html',
   'DATA'),
  ('pygame\\docs\\generated\\tut\\tom_games6.html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\generated\\tut\\tom_games6.html',
   'DATA'),
  ('pygame\\docs\\serve.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\serve.py',
   'DATA'),
  ('pygame\\docs\\static.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\docs\\static.py',
   'DATA'),
  ('pygame\\draw.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\draw.pyi',
   'DATA'),
  ('pygame\\draw_py.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\draw_py.py',
   'DATA'),
  ('pygame\\event.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\event.pyi',
   'DATA'),
  ('pygame\\examples\\README.rst',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\README.rst',
   'DATA'),
  ('pygame\\examples\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\__init__.py',
   'DATA'),
  ('pygame\\examples\\aacircle.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\aacircle.py',
   'DATA'),
  ('pygame\\examples\\aliens.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\aliens.py',
   'DATA'),
  ('pygame\\examples\\arraydemo.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\arraydemo.py',
   'DATA'),
  ('pygame\\examples\\audiocapture.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\audiocapture.py',
   'DATA'),
  ('pygame\\examples\\blend_fill.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\blend_fill.py',
   'DATA'),
  ('pygame\\examples\\blit_blends.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\blit_blends.py',
   'DATA'),
  ('pygame\\examples\\camera.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\camera.py',
   'DATA'),
  ('pygame\\examples\\chimp.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\chimp.py',
   'DATA'),
  ('pygame\\examples\\cursors.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\cursors.py',
   'DATA'),
  ('pygame\\examples\\data\\BGR.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\BGR.png',
   'DATA'),
  ('pygame\\examples\\data\\MIDI_sample.mid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\MIDI_sample.mid',
   'DATA'),
  ('pygame\\examples\\data\\alien1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien1.gif',
   'DATA'),
  ('pygame\\examples\\data\\alien1.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien1.jpg',
   'DATA'),
  ('pygame\\examples\\data\\alien1.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien1.png',
   'DATA'),
  ('pygame\\examples\\data\\alien2.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien2.gif',
   'DATA'),
  ('pygame\\examples\\data\\alien2.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien2.png',
   'DATA'),
  ('pygame\\examples\\data\\alien3.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien3.gif',
   'DATA'),
  ('pygame\\examples\\data\\alien3.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\alien3.png',
   'DATA'),
  ('pygame\\examples\\data\\arraydemo.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\arraydemo.bmp',
   'DATA'),
  ('pygame\\examples\\data\\asprite.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\asprite.bmp',
   'DATA'),
  ('pygame\\examples\\data\\attributions.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\attributions.txt',
   'DATA'),
  ('pygame\\examples\\data\\background.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\background.gif',
   'DATA'),
  ('pygame\\examples\\data\\black.ppm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\black.ppm',
   'DATA'),
  ('pygame\\examples\\data\\blue.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\blue.gif',
   'DATA'),
  ('pygame\\examples\\data\\blue.mpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\blue.mpg',
   'DATA'),
  ('pygame\\examples\\data\\bomb.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\bomb.gif',
   'DATA'),
  ('pygame\\examples\\data\\boom.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\boom.wav',
   'DATA'),
  ('pygame\\examples\\data\\brick.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\brick.png',
   'DATA'),
  ('pygame\\examples\\data\\car_door.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\car_door.wav',
   'DATA'),
  ('pygame\\examples\\data\\chimp.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\chimp.png',
   'DATA'),
  ('pygame\\examples\\data\\city.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\city.png',
   'DATA'),
  ('pygame\\examples\\data\\crimson.pnm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\crimson.pnm',
   'DATA'),
  ('pygame\\examples\\data\\cursor.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\cursor.png',
   'DATA'),
  ('pygame\\examples\\data\\danger.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\danger.gif',
   'DATA'),
  ('pygame\\examples\\data\\explosion1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\explosion1.gif',
   'DATA'),
  ('pygame\\examples\\data\\fist.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\fist.png',
   'DATA'),
  ('pygame\\examples\\data\\frame.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\frame.png',
   'DATA'),
  ('pygame\\examples\\data\\green.pcx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\green.pcx',
   'DATA'),
  ('pygame\\examples\\data\\grey.pgm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\grey.pgm',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.flac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.flac',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.mp3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.mp3',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.ogg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.ogg',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.opus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.opus',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.wav',
   'DATA'),
  ('pygame\\examples\\data\\house_lo.wv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\house_lo.wv',
   'DATA'),
  ('pygame\\examples\\data\\laplacian.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\laplacian.png',
   'DATA'),
  ('pygame\\examples\\data\\liquid.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\liquid.bmp',
   'DATA'),
  ('pygame\\examples\\data\\magenta.lbm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\magenta.lbm',
   'DATA'),
  ('pygame\\examples\\data\\metadata.mp3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\metadata.mp3',
   'DATA'),
  ('pygame\\examples\\data\\midikeys.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\midikeys.png',
   'DATA'),
  ('pygame\\examples\\data\\peppers3.tif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\peppers3.tif',
   'DATA'),
  ('pygame\\examples\\data\\player1.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\player1.gif',
   'DATA'),
  ('pygame\\examples\\data\\punch.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\punch.wav',
   'DATA'),
  ('pygame\\examples\\data\\purple.qoi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\purple.qoi',
   'DATA'),
  ('pygame\\examples\\data\\purple.xpm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\purple.xpm',
   'DATA'),
  ('pygame\\examples\\data\\red.jpg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\red.jpg',
   'DATA'),
  ('pygame\\examples\\data\\sans.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\sans.ttf',
   'DATA'),
  ('pygame\\examples\\data\\scarlet.webp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\scarlet.webp',
   'DATA'),
  ('pygame\\examples\\data\\secosmic_lo.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\secosmic_lo.wav',
   'DATA'),
  ('pygame\\examples\\data\\shot.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\shot.gif',
   'DATA'),
  ('pygame\\examples\\data\\static.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\static.png',
   'DATA'),
  ('pygame\\examples\\data\\surfonasinewave.xm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\surfonasinewave.xm',
   'DATA'),
  ('pygame\\examples\\data\\teal.svg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\teal.svg',
   'DATA'),
  ('pygame\\examples\\data\\tomato.xcf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\tomato.xcf',
   'DATA'),
  ('pygame\\examples\\data\\turquoise.tif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\turquoise.tif',
   'DATA'),
  ('pygame\\examples\\data\\whiff.wav',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\whiff.wav',
   'DATA'),
  ('pygame\\examples\\data\\white.pbm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\white.pbm',
   'DATA'),
  ('pygame\\examples\\data\\yellow.tga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\data\\yellow.tga',
   'DATA'),
  ('pygame\\examples\\dropevent.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\dropevent.py',
   'DATA'),
  ('pygame\\examples\\eventlist.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\eventlist.py',
   'DATA'),
  ('pygame\\examples\\font_viewer.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\font_viewer.py',
   'DATA'),
  ('pygame\\examples\\fonty.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\fonty.py',
   'DATA'),
  ('pygame\\examples\\freetype_misc.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\freetype_misc.py',
   'DATA'),
  ('pygame\\examples\\glcube.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\glcube.py',
   'DATA'),
  ('pygame\\examples\\go_over_there.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\go_over_there.py',
   'DATA'),
  ('pygame\\examples\\grid.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\grid.py',
   'DATA'),
  ('pygame\\examples\\headless_no_windows_needed.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\headless_no_windows_needed.py',
   'DATA'),
  ('pygame\\examples\\joystick.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\joystick.py',
   'DATA'),
  ('pygame\\examples\\liquid.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\liquid.py',
   'DATA'),
  ('pygame\\examples\\mask.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\mask.py',
   'DATA'),
  ('pygame\\examples\\midi.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\midi.py',
   'DATA'),
  ('pygame\\examples\\moveit.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\moveit.py',
   'DATA'),
  ('pygame\\examples\\multiplayer_joystick.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\multiplayer_joystick.py',
   'DATA'),
  ('pygame\\examples\\music_drop_fade.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\music_drop_fade.py',
   'DATA'),
  ('pygame\\examples\\ninepatch.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\ninepatch.py',
   'DATA'),
  ('pygame\\examples\\pixelarray.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\pixelarray.py',
   'DATA'),
  ('pygame\\examples\\playmus.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\playmus.py',
   'DATA'),
  ('pygame\\examples\\prevent_display_stretching.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\prevent_display_stretching.py',
   'DATA'),
  ('pygame\\examples\\resizing_new.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\resizing_new.py',
   'DATA'),
  ('pygame\\examples\\retro_scaling.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\retro_scaling.py',
   'DATA'),
  ('pygame\\examples\\scaletest.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\scaletest.py',
   'DATA'),
  ('pygame\\examples\\scrap_clipboard.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\scrap_clipboard.py',
   'DATA'),
  ('pygame\\examples\\scroll.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\scroll.py',
   'DATA'),
  ('pygame\\examples\\setmodescale.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\setmodescale.py',
   'DATA'),
  ('pygame\\examples\\sound.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\sound.py',
   'DATA'),
  ('pygame\\examples\\sound_array_demos.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\sound_array_demos.py',
   'DATA'),
  ('pygame\\examples\\sprite_texture.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\sprite_texture.py',
   'DATA'),
  ('pygame\\examples\\stars.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\stars.py',
   'DATA'),
  ('pygame\\examples\\testsprite.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\testsprite.py',
   'DATA'),
  ('pygame\\examples\\textinput.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\textinput.py',
   'DATA'),
  ('pygame\\examples\\vgrade.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\vgrade.py',
   'DATA'),
  ('pygame\\examples\\video.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\video.py',
   'DATA'),
  ('pygame\\examples\\window_opengl.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\examples\\window_opengl.py',
   'DATA'),
  ('pygame\\fastevent.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\fastevent.py',
   'DATA'),
  ('pygame\\fastevent.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\fastevent.pyi',
   'DATA'),
  ('pygame\\font.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\font.pyi',
   'DATA'),
  ('pygame\\freesansbold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freesansbold.ttf',
   'DATA'),
  ('pygame\\freetype.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freetype.py',
   'DATA'),
  ('pygame\\freetype.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freetype.pyi',
   'DATA'),
  ('pygame\\ftfont.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\ftfont.py',
   'DATA'),
  ('pygame\\geometry.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\geometry.pyi',
   'DATA'),
  ('pygame\\gfxdraw.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\gfxdraw.pyi',
   'DATA'),
  ('pygame\\image.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\image.pyi',
   'DATA'),
  ('pygame\\joystick.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\joystick.pyi',
   'DATA'),
  ('pygame\\key.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\key.pyi',
   'DATA'),
  ('pygame\\locals.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\locals.py',
   'DATA'),
  ('pygame\\locals.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\locals.pyi',
   'DATA'),
  ('pygame\\macosx.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\macosx.py',
   'DATA'),
  ('pygame\\mask.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mask.pyi',
   'DATA'),
  ('pygame\\math.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\math.pyi',
   'DATA'),
  ('pygame\\midi.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\midi.py',
   'DATA'),
  ('pygame\\midi.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\midi.pyi',
   'DATA'),
  ('pygame\\mixer.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer.pyi',
   'DATA'),
  ('pygame\\mixer_music.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer_music.pyi',
   'DATA'),
  ('pygame\\mouse.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mouse.pyi',
   'DATA'),
  ('pygame\\pixelarray.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelarray.pyi',
   'DATA'),
  ('pygame\\pixelcopy.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelcopy.pyi',
   'DATA'),
  ('pygame\\pkgdata.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pkgdata.py',
   'DATA'),
  ('pygame\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\py.typed',
   'DATA'),
  ('pygame\\pygame.ico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pygame.ico',
   'DATA'),
  ('pygame\\pygame_icon.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pygame_icon.bmp',
   'DATA'),
  ('pygame\\pygame_icon.icns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pygame_icon.icns',
   'DATA'),
  ('pygame\\pygame_icon_mac.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pygame_icon_mac.bmp',
   'DATA'),
  ('pygame\\rect.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rect.pyi',
   'DATA'),
  ('pygame\\rwobject.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rwobject.pyi',
   'DATA'),
  ('pygame\\scrap.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\scrap.pyi',
   'DATA'),
  ('pygame\\sndarray.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\sndarray.py',
   'DATA'),
  ('pygame\\sndarray.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\sndarray.pyi',
   'DATA'),
  ('pygame\\sprite.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\sprite.py',
   'DATA'),
  ('pygame\\sprite.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\sprite.pyi',
   'DATA'),
  ('pygame\\surface.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surface.pyi',
   'DATA'),
  ('pygame\\surfarray.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surfarray.py',
   'DATA'),
  ('pygame\\surfarray.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surfarray.pyi',
   'DATA'),
  ('pygame\\surflock.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surflock.pyi',
   'DATA'),
  ('pygame\\sysfont.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\sysfont.py',
   'DATA'),
  ('pygame\\system.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\system.pyi',
   'DATA'),
  ('pygame\\tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\__init__.py',
   'DATA'),
  ('pygame\\tests\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\__main__.py',
   'DATA'),
  ('pygame\\tests\\base_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\base_test.py',
   'DATA'),
  ('pygame\\tests\\blit_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\blit_test.py',
   'DATA'),
  ('pygame\\tests\\bufferproxy_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\bufferproxy_test.py',
   'DATA'),
  ('pygame\\tests\\camera_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\camera_test.py',
   'DATA'),
  ('pygame\\tests\\color_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\color_test.py',
   'DATA'),
  ('pygame\\tests\\constants_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\constants_test.py',
   'DATA'),
  ('pygame\\tests\\controller_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\controller_test.py',
   'DATA'),
  ('pygame\\tests\\cursors_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\cursors_test.py',
   'DATA'),
  ('pygame\\tests\\debug_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\debug_test.py',
   'DATA'),
  ('pygame\\tests\\display_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\display_test.py',
   'DATA'),
  ('pygame\\tests\\docs_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\docs_test.py',
   'DATA'),
  ('pygame\\tests\\draw_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\draw_test.py',
   'DATA'),
  ('pygame\\tests\\event_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\event_test.py',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\A_PyGameMono-8.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\A_PyGameMono-8.png',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PlayfairDisplaySemibold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PlayfairDisplaySemibold.ttf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PyGameMono-18-100dpi.bdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PyGameMono-18-100dpi.bdf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PyGameMono-18-75dpi.bdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PyGameMono-18-75dpi.bdf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PyGameMono-8.bdf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PyGameMono-8.bdf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PyGameMono.otf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PyGameMono.otf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\PyGameMono.sfd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\PyGameMono.sfd',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\test_fixed.otf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\test_fixed.otf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\test_sans.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\test_sans.ttf',
   'DATA'),
  ('pygame\\tests\\fixtures\\fonts\\u13079_PyGameMono-8.png',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\fonts\\u13079_PyGameMono-8.png',
   'DATA'),
  ('pygame\\tests\\fixtures\\xbm_cursors\\white_sizing.xbm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\xbm_cursors\\white_sizing.xbm',
   'DATA'),
  ('pygame\\tests\\fixtures\\xbm_cursors\\white_sizing_mask.xbm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\fixtures\\xbm_cursors\\white_sizing_mask.xbm',
   'DATA'),
  ('pygame\\tests\\font_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\font_test.py',
   'DATA'),
  ('pygame\\tests\\freetype_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\freetype_tags.py',
   'DATA'),
  ('pygame\\tests\\freetype_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\freetype_test.py',
   'DATA'),
  ('pygame\\tests\\ftfont_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\ftfont_tags.py',
   'DATA'),
  ('pygame\\tests\\ftfont_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\ftfont_test.py',
   'DATA'),
  ('pygame\\tests\\geometry_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\geometry_test.py',
   'DATA'),
  ('pygame\\tests\\gfxdraw_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\gfxdraw_test.py',
   'DATA'),
  ('pygame\\tests\\image__save_gl_surface_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\image__save_gl_surface_test.py',
   'DATA'),
  ('pygame\\tests\\image_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\image_tags.py',
   'DATA'),
  ('pygame\\tests\\image_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\image_test.py',
   'DATA'),
  ('pygame\\tests\\imageext_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\imageext_tags.py',
   'DATA'),
  ('pygame\\tests\\imageext_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\imageext_test.py',
   'DATA'),
  ('pygame\\tests\\joystick_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\joystick_test.py',
   'DATA'),
  ('pygame\\tests\\key_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\key_test.py',
   'DATA'),
  ('pygame\\tests\\locals_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\locals_test.py',
   'DATA'),
  ('pygame\\tests\\mask_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mask_test.py',
   'DATA'),
  ('pygame\\tests\\math_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\math_test.py',
   'DATA'),
  ('pygame\\tests\\midi_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\midi_test.py',
   'DATA'),
  ('pygame\\tests\\mixer_music_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mixer_music_tags.py',
   'DATA'),
  ('pygame\\tests\\mixer_music_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mixer_music_test.py',
   'DATA'),
  ('pygame\\tests\\mixer_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mixer_tags.py',
   'DATA'),
  ('pygame\\tests\\mixer_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mixer_test.py',
   'DATA'),
  ('pygame\\tests\\mouse_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\mouse_test.py',
   'DATA'),
  ('pygame\\tests\\pixelarray_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\pixelarray_test.py',
   'DATA'),
  ('pygame\\tests\\pixelcopy_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\pixelcopy_test.py',
   'DATA'),
  ('pygame\\tests\\rect_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\rect_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\fake_4_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\fake_4_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\fake_5_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\fake_5_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\fake_6_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\fake_6_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\no_assertions__ret_code_of_1__test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\no_assertions__ret_code_of_1__test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\all_ok\\zero_tests_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\all_ok\\zero_tests_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\everything\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\everything\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\everything\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\everything\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\everything\\incomplete_todo_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\everything\\incomplete_todo_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\everything\\magic_tag_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\everything\\magic_tag_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\everything\\sleep_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\everything\\sleep_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\exclude\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\exclude\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\exclude\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\exclude\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\exclude\\invisible_tag_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\exclude\\invisible_tag_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\exclude\\magic_tag_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\exclude\\magic_tag_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\failures1\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\failures1\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\failures1\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\failures1\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\failures1\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\failures1\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\failures1\\fake_4_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\failures1\\fake_4_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete_todo\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete_todo\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete_todo\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete_todo\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\incomplete_todo\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\incomplete_todo\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\infinite_loop\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\infinite_loop\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\infinite_loop\\fake_1_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\infinite_loop\\fake_1_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\infinite_loop\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\infinite_loop\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stderr\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stderr\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stderr\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stderr\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stderr\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stderr\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stderr\\fake_4_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stderr\\fake_4_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stdout\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stdout\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stdout\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stdout\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stdout\\fake_3_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stdout\\fake_3_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\print_stdout\\fake_4_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\print_stdout\\fake_4_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\run_tests__test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\run_tests__test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\timeout\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\timeout\\__init__.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\timeout\\fake_2_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\timeout\\fake_2_test.py',
   'DATA'),
  ('pygame\\tests\\run_tests__tests\\timeout\\sleep_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\run_tests__tests\\timeout\\sleep_test.py',
   'DATA'),
  ('pygame\\tests\\rwobject_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\rwobject_test.py',
   'DATA'),
  ('pygame\\tests\\scrap_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\scrap_tags.py',
   'DATA'),
  ('pygame\\tests\\scrap_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\scrap_test.py',
   'DATA'),
  ('pygame\\tests\\sndarray_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\sndarray_tags.py',
   'DATA'),
  ('pygame\\tests\\sndarray_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\sndarray_test.py',
   'DATA'),
  ('pygame\\tests\\sprite_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\sprite_test.py',
   'DATA'),
  ('pygame\\tests\\surface_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\surface_test.py',
   'DATA'),
  ('pygame\\tests\\surfarray_tags.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\surfarray_tags.py',
   'DATA'),
  ('pygame\\tests\\surfarray_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\surfarray_test.py',
   'DATA'),
  ('pygame\\tests\\surflock_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\surflock_test.py',
   'DATA'),
  ('pygame\\tests\\sysfont_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\sysfont_test.py',
   'DATA'),
  ('pygame\\tests\\system_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\system_test.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\__init__.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\arrinter.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\arrinter.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\async_sub.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\async_sub.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\buftools.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\buftools.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\endian.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\endian.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\png.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\png.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\run_tests.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\run_tests.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\test_machinery.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\test_machinery.py',
   'DATA'),
  ('pygame\\tests\\test_utils\\test_runner.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\test_utils\\test_runner.py',
   'DATA'),
  ('pygame\\tests\\threads_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\threads_test.py',
   'DATA'),
  ('pygame\\tests\\time_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\time_test.py',
   'DATA'),
  ('pygame\\tests\\touch_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\touch_test.py',
   'DATA'),
  ('pygame\\tests\\transform_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\transform_test.py',
   'DATA'),
  ('pygame\\tests\\typing_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\typing_test.py',
   'DATA'),
  ('pygame\\tests\\version_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\version_test.py',
   'DATA'),
  ('pygame\\tests\\video_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\video_test.py',
   'DATA'),
  ('pygame\\tests\\window_test.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\tests\\window_test.py',
   'DATA'),
  ('pygame\\threads\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\threads\\__init__.py',
   'DATA'),
  ('pygame\\time.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\time.pyi',
   'DATA'),
  ('pygame\\transform.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\transform.pyi',
   'DATA'),
  ('pygame\\typing.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\typing.py',
   'DATA'),
  ('pygame\\typing.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\typing.pyi',
   'DATA'),
  ('pygame\\version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\version.py',
   'DATA'),
  ('pygame\\version.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\version.pyi',
   'DATA'),
  ('pygame\\window.pyi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\window.pyi',
   'DATA'),
  ('pygame_ce-2.5.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame_ce-2.5.3.dist-info\\INSTALLER',
   'DATA'),
  ('pygame_ce-2.5.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame_ce-2.5.3.dist-info\\METADATA',
   'DATA'),
  ('pygame_ce-2.5.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame_ce-2.5.3.dist-info\\RECORD',
   'DATA'),
  ('pygame_ce-2.5.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame_ce-2.5.3.dist-info\\WHEEL',
   'DATA'),
  ('pygame_ce-2.5.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame_ce-2.5.3.dist-info\\entry_points.txt',
   'DATA'),
  ('pystray-0.19.5.dist-info\\COPYING',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\COPYING',
   'DATA'),
  ('pystray-0.19.5.dist-info\\COPYING.LGPL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\COPYING.LGPL',
   'DATA'),
  ('pystray-0.19.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\INSTALLER',
   'DATA'),
  ('pystray-0.19.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\METADATA',
   'DATA'),
  ('pystray-0.19.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\RECORD',
   'DATA'),
  ('pystray-0.19.5.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\REQUESTED',
   'DATA'),
  ('pystray-0.19.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\WHEEL',
   'DATA'),
  ('pystray-0.19.5.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\top_level.txt',
   'DATA'),
  ('pystray-0.19.5.dist-info\\zip-safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray-0.19.5.dist-info\\zip-safe',
   'DATA'),
  ('pystray\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\__init__.py',
   'DATA'),
  ('pystray\\_appindicator.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_appindicator.py',
   'DATA'),
  ('pystray\\_base.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_base.py',
   'DATA'),
  ('pystray\\_darwin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_darwin.py',
   'DATA'),
  ('pystray\\_dummy.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_dummy.py',
   'DATA'),
  ('pystray\\_gtk.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_gtk.py',
   'DATA'),
  ('pystray\\_info.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_info.py',
   'DATA'),
  ('pystray\\_util\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_util\\__init__.py',
   'DATA'),
  ('pystray\\_util\\gtk.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_util\\gtk.py',
   'DATA'),
  ('pystray\\_util\\notify_dbus.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_util\\notify_dbus.py',
   'DATA'),
  ('pystray\\_util\\win32.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_util\\win32.py',
   'DATA'),
  ('pystray\\_win32.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_win32.py',
   'DATA'),
  ('pystray\\_xorg.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\pystray\\_xorg.py',
   'DATA'),
  ('ui\\__pycache__\\components.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\__pycache__\\components.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\components.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\__pycache__\\components.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\ui_manager.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\__pycache__\\ui_manager.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\ui_manager.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\__pycache__\\ui_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\components.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\ui\\components.py',
   'DATA'),
  ('ui\\screens\\__pycache__\\achievements_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\achievements_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\achievements_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\achievements_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\battle_stats_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\battle_stats_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\battle_stats_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\battle_stats_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\character_creation.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\character_creation.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\character_creation.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\character_creation.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\character_creation_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\character_creation_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\character_creation_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\character_creation_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\dungeon_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\dungeon_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\dungeon_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\dungeon_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\equipment_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\equipment_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\equipment_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\equipment_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\game_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\game_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\game_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\game_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\inventory_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\inventory_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\inventory_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\inventory_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\load_game.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\load_game.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\load_game.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\load_game.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\login_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\login_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\login_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\login_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\main_menu.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\main_menu.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\main_menu.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\main_menu.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\map_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\map_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\map_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\map_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\settings.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\settings.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\settings.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\settings.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\shop_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\shop_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\shop_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\shop_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\skills_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\skills_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\__pycache__\\skills_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\__pycache__\\skills_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\achievements_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\achievements_screen.py',
   'DATA'),
  ('ui\\screens\\battle_stats_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\battle_stats_screen.py',
   'DATA'),
  ('ui\\screens\\character_creation.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\character_creation.py',
   'DATA'),
  ('ui\\screens\\character_creation_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\character_creation_screen.py',
   'DATA'),
  ('ui\\screens\\equipment_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\equipment_screen.py',
   'DATA'),
  ('ui\\screens\\game_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\game_screen.py',
   'DATA'),
  ('ui\\screens\\inventory_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\inventory_screen.py',
   'DATA'),
  ('ui\\screens\\load_game.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\ui\\screens\\load_game.py',
   'DATA'),
  ('ui\\screens\\login_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\login_screen.py',
   'DATA'),
  ('ui\\screens\\main_menu.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\ui\\screens\\main_menu.py',
   'DATA'),
  ('ui\\screens\\map_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\map_screen.py',
   'DATA'),
  ('ui\\screens\\recharge\\vip_level_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\recharge\\vip_level_screen.py',
   'DATA'),
  ('ui\\screens\\settings.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\ui\\screens\\settings.py',
   'DATA'),
  ('ui\\screens\\shop_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\shop_screen.py',
   'DATA'),
  ('ui\\screens\\signin\\__pycache__\\signin_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\signin\\__pycache__\\signin_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\signin\\__pycache__\\signin_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\signin\\__pycache__\\signin_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\signin\\signin_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\signin\\signin_screen.py',
   'DATA'),
  ('ui\\screens\\skills_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\skills_screen.py',
   'DATA'),
  ('ui\\screens\\vip\\__pycache__\\vip_screen.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\vip\\__pycache__\\vip_screen.cpython-312.pyc',
   'DATA'),
  ('ui\\screens\\vip\\__pycache__\\vip_screen.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\vip\\__pycache__\\vip_screen.cpython-313.pyc',
   'DATA'),
  ('ui\\screens\\vip\\vip_screen.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\ui\\screens\\vip\\vip_screen.py',
   'DATA'),
  ('ui\\ui_manager.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\ui\\ui_manager.py',
   'DATA'),
  ('utils\\__init__.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\auto_potion.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\auto_potion.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\auto_potion.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\auto_potion.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\backup_system.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\backup_system.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\backup_system.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\backup_system.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\logger.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\logger.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\logger.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\logger.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\resource_manager.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\resource_manager.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\resource_manager.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\resource_manager.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\safe_file.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\safe_file.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\safe_file.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\safe_file.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\security.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\security.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\security.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\security.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\utils.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\utils.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\utils.cpython-313.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\__pycache__\\utils.cpython-313.pyc',
   'DATA'),
  ('utils\\auto_potion.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\auto_potion.py',
   'DATA'),
  ('utils\\backup_system.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\backup_system.py',
   'DATA'),
  ('utils\\fix_save_encoding.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\fix_save_encoding.py',
   'DATA'),
  ('utils\\logger.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\logger.py',
   'DATA'),
  ('utils\\resource_manager.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\resource_manager.py',
   'DATA'),
  ('utils\\resource_path.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\resource_path.py',
   'DATA'),
  ('utils\\safe_file.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\safe_file.py',
   'DATA'),
  ('utils\\save_manager.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\save_manager.py',
   'DATA'),
  ('utils\\security.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\security.py',
   'DATA'),
  ('utils\\translation_manager.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\utils\\translation_manager.py',
   'DATA'),
  ('utils\\utils.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 (2)\\老版\\utils\\utils.py',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\LICENSE',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\zip-safe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\zip-safe',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\METADATA',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('altgraph-0.17.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\altgraph-0.17.4.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\老版 '
   '(2)\\老版\\build\\build_onefile\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1748063373,
 [('runw.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll')
