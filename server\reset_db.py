"""
数据库重置脚本
用于清空数据库中的所有集合
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到控制台
        logging.FileHandler("reset_db.log")  # 输出到文件
    ]
)
logger = logging.getLogger(__name__)

# MongoDB连接信息
MONGO_URI = "mongodb://localhost:27017"
DATABASE_NAME = "game_db"

async def reset_database():
    """重置数据库"""
    print("开始重置数据库...")
    try:
        # 连接到MongoDB
        print(f"正在连接到MongoDB: {MONGO_URI}")
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[DATABASE_NAME]

        # 获取所有集合
        print("正在获取集合列表...")
        collections = await db.list_collection_names()
        print(f"找到以下集合: {collections}")
        logger.info(f"找到以下集合: {collections}")

        # 清空所有集合
        for collection_name in collections:
            print(f"正在清空集合: {collection_name}")
            logger.info(f"正在清空集合: {collection_name}")
            collection = db[collection_name]
            result = await collection.delete_many({})
            print(f"已删除 {result.deleted_count} 条记录")
            logger.info(f"已删除 {result.deleted_count} 条记录")

        print("数据库重置完成")
        logger.info("数据库重置完成")
    except Exception as e:
        print(f"重置数据库时发生错误: {e}")
        logger.error(f"重置数据库时发生错误: {e}")
        raise

if __name__ == "__main__":
    # 运行异步函数
    asyncio.run(reset_database())
