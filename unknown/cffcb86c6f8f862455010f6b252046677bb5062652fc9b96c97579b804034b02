#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI Screens 自动清理脚本
安全删除缓存文件和确认的重复文件
"""

import os
import shutil
from datetime import datetime

def cleanup_cache_files():
    """清理缓存文件"""
    print("清理Python缓存文件...")
    
    deleted_count = 0
    deleted_size = 0
    
    for root, dirs, files in os.walk("ui/screens"):
        if "__pycache__" in dirs:
            cache_dir = os.path.join(root, "__pycache__")
            try:
                # 计算大小
                for file in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, file)
                    if os.path.isfile(file_path):
                        deleted_size += os.path.getsize(file_path)
                        deleted_count += 1
                
                # 删除目录
                shutil.rmtree(cache_dir)
                print(f"✅ 已删除: {cache_dir}")
            except Exception as e:
                print(f"❌ 删除失败: {cache_dir} - {e}")
    
    print(f"📊 共删除 {deleted_count} 个缓存文件")
    print(f"📊 释放空间: {deleted_size / 1024:.2f} KB")

def main():
    """主函数"""
    print("开始UI Screens清理...")
    print("=" * 50)
    
    # 清理缓存文件
    cleanup_cache_files()
    
    print("=" * 50)
    print("清理完成！")

if __name__ == "__main__":
    main()
