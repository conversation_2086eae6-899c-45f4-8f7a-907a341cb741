# -*- coding: utf-8 -*-
"""
游戏主界面 - 重构版
整合各个UI面板，提供统一的游戏界面管理
"""

import traceback
import pygame
import time
import math
import random
import os
import json
from typing import Dict, List, Any, Tuple, Optional, Callable, Union, cast, TYPE_CHECKING
from ui.ui_manager import Screen
from ui.components import Panel, Button, Text, ProgressBar, ScrollableList, Dialog, Toggle, Dropdown, DraggableSkillIcon
from ui.screens.game_ui_base import GameUIPanel, GameUIConstants, calculate_layout_positions
from ui.screens.player_panel import PlayerPanel
from ui.screens.monster_panel import MonsterPanel
from ui.screens.battle_log_panel import BattleLogPanel
from utils.logger import logger
from utils.resource_manager import resources
from core.class_stats import get_class_stats, get_level_stats, CLASS_STATS
from core.config import GameConfig
from core.summon import SummonFactory
from core.skill_manager import SkillManager  # 导入新的技能管理器
import sys

# 类型提示导入
if TYPE_CHECKING:
    from core.game import Game
    from core.types import GameType

# 定义一些常量
BATTLE_UPDATE_INTERVAL = 1.0  # 战斗更新间隔，秒
AUTO_BATTLE_INTERVAL = 2.0  # 自动战斗间隔，秒

class GameScreen(Screen):
    """游戏主界面"""

    def __init__(self, ui_manager, game_manager):
        """初始化游戏主界面

        Args:
            ui_manager: UI管理器实例
            game_manager: 游戏管理器实例
        """
        super().__init__("game")
        self.ui_manager = ui_manager
        self.game_manager: 'Game' = game_manager  # 使用类型提示

        # 组件映射字典，用于快速访问特定组件
        self.components_map = {}

        # 战斗日志滚动相关
        self.full_battle_logs = []  # 完整的战斗日志列表
        self.log_scroll_offset = 0  # 滚动偏移量
        self.log_display_count = 8  # 显示的日志行数

        # 获取屏幕尺寸以供后续计算
        screen_size = pygame.display.get_surface().get_size()
        left_panel_w = 250
        right_panel_w = 250 # monster_panel 宽度

        # central_panel 的 x 和 width
        central_panel_x = left_panel_w + 20
        central_panel_width = screen_size[0] - left_panel_w - right_panel_w - 30

        # central_panel 的 top 和 height
        central_panel_top = 60
        central_panel_height = screen_size[1] - 160

        # 寻怪界面尺寸 - 横跨中央区域
        hunting_area_width = central_panel_width - 40  # 中央区域宽度减去左右边距
        hunting_area_height = 200  # 固定高度

        # 将寻怪区域放在中央区域上方
        hunting_area_left = central_panel_x + 20  # 中央区域左边界加上20像素边距
        hunting_area_top = central_panel_top + 20  # 中央区域顶部加上20像素边距

        # 最终的 self.hunting_area 定义
        self.hunting_area = pygame.Rect(
            hunting_area_left,
            hunting_area_top,
            hunting_area_width,
            hunting_area_height
        )

        # 添加寻怪区域标题
        hunting_title_rect = pygame.Rect(hunting_area_left, hunting_area_top - 25, hunting_area_width, 20)
        hunting_title = self.ui_manager.create_text(
            hunting_title_rect,
            "寻怪区域",
            "chinese_normal",
            (180, 220, 180),  # 绿色调标题
            "center"
        )
        self.add_component(hunting_title)
        self.components_map["hunting_title"] = hunting_title
        logger.info(f"寻怪界面区域 (hunting_area) 设置为: {self.hunting_area}")

        self.player_pos = [20, 20]  # 玩家在寻怪区域内的相对位置
        self.monsters = []  # 寻怪模式下的怪物列表 (UI侧)
        self.last_hunting_update = 0 # 上次寻怪逻辑更新时间
        self.hunting_visible = True # 寻怪界面是否可见，默认固定显示

        # 资源管理器
        from utils.resource_manager import ResourceManager
        self.resource_manager = ResourceManager()

        # UI组件映射
        self.components_map = {}

        # 玩家属性组件
        self.player_stats_components = {}

        # 怪物组件
        self.monster_components = {}

        # 装备栏位
        self.equipment_slots = {}

        # 战斗日志
        self.battle_logs = []
        self.max_logs = 100

        # 自动战斗状态
        self.is_auto_battle = False
        self.is_battle_active = False
        self.last_auto_battle_time = time.time()

        # 初始化技能管理器
        self.skill_manager = None  # 将在_init_skill_slots中初始化

        # 技能槽位字典（旧版本兼容）
        self.skill_slots = {}

        # 自动释放技能状态（旧版本兼容，将逐步迁移到skill_manager）
        self.auto_cast_skills = {}  # {slot_idx: last_cast_time}

        # 导入全局战斗速度修正并设置战斗间隔
        base_interval = random.uniform(3, 5)  # 基础寻怪间隔3-5秒
        # 应用速度修正，数值越大间隔越长（乘以而不是除以）
        self.auto_battle_interval = base_interval * GameConfig.BATTLE_SPEED_MODIFIER

        # 死亡面板相关
        self.death_panel = None
        self.death_message = None
        self.death_timer = None
        self.show_death_panel = False

        # 副本倒计时相关
        self.dungeon_timer = None
        self.last_dungeon_time_check = 0

        # 添加UI更新控制
        self.last_stats_update_time = 0
        self.stats_update_interval = 2.0  # 玩家属性更新间隔，单位秒
        self.is_map_ui_updated = False

        # 怪物图片
        self.monster_image = None

        # 技能按钮日志标志
        self._logged_no_skills = False

        # 添加图片缓存
        self.summon_image_cache: Dict[str, Optional[pygame.Surface]] = {}
        self.monster_image_cache: Dict[str, Optional[pygame.Surface]] = {}
        self.skill_icon_cache: Dict[str, Optional[pygame.Surface]] = {}

        # 创建界面组件
        self._create_components()

    def _create_components(self):
        """创建界面组件"""
        screen_size = pygame.display.get_surface().get_size()

        # 创建背景
        self.background = Panel(
            pygame.Rect(0, 0, screen_size[0], screen_size[1]),
            color=(30, 30, 40),
            border_width=0
        )
        self.add_component(self.background)

        # 创建顶部状态栏
        self._create_top_bar()

        # 创建左侧玩家面板
        self._create_player_panel()

        # 创建中央区域
        self._create_central_area()

        # 创建右侧怪物面板
        self._create_monster_panel()

        # 创建底部控制栏
        self._create_bottom_bar()

        # 创建死亡提示面板（初始为隐藏状态）
        self._create_death_panel()

        # 创建副本倒计时显示
        self._create_dungeon_timer()



    def _create_top_bar(self):
        """创建顶部状态栏"""
        screen_size = pygame.display.get_surface().get_size()

        # 顶部状态栏容器
        top_bar_height = 50
        top_bar = Panel(
            pygame.Rect(0, 0, screen_size[0], top_bar_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(top_bar)
        self.components_map["top_bar"] = top_bar

        # 游戏区域标题
        area_title = self.ui_manager.create_text(
            pygame.Rect(20, 10, 200, 30),
            "荒野",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(area_title)
        self.components_map["area_title"] = area_title

        # 菜单按钮
        menu_button = self.ui_manager.create_button(
            pygame.Rect(screen_size[0] - 80, 10, 60, 30),
            "菜单",
            self._on_menu_click,
            "chinese_normal"
        )
        self.add_component(menu_button)

    def _create_player_panel(self):
        """创建左侧玩家面板"""
        screen_size = pygame.display.get_surface().get_size()

        panel_width = 250
        panel_height = screen_size[1] - 100  # 减去顶部和底部的高度

        # 玩家面板容器
        player_panel = Panel(
            pygame.Rect(10, 60, panel_width, panel_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(player_panel)
        self.components_map["player_panel"] = player_panel

        # 获取默认战士职业的属性
        default_class = "战士"
        class_stats = get_class_stats(default_class)
        level_stats = get_level_stats(default_class, 1)

        # 解析基础属性
        base_hp = level_stats[0]
        base_mp = level_stats[1]
        base_attack = level_stats[2]
        base_defense = level_stats[4] if len(level_stats) > 4 else 0

        # 玩家名称和等级
        name_rect = pygame.Rect(20, 70, panel_width - 20, 30)
        name_text = self.ui_manager.create_text(
            name_rect,
            f"未命名 {default_class} Lv.1",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(name_text)
        self.player_stats_components["name"] = name_text

        # 生命值条
        hp_bar_rect = pygame.Rect(20, 110, panel_width - 30, 20)
        hp_bar = self.ui_manager.create_hp_bar(hp_bar_rect, base_hp, base_hp)
        self.add_component(hp_bar)
        self.player_stats_components["hp_bar"] = hp_bar

        # 生命值文本
        hp_text_rect = pygame.Rect(20, 130, panel_width - 30, 20)
        hp_text = self.ui_manager.create_text(
            hp_text_rect,
            f"HP: {base_hp}/{base_hp}",
            "chinese_small",
            (220, 220, 220),
            "left"
        )
        self.add_component(hp_text)
        self.player_stats_components["hp_text"] = hp_text

        # 魔法值条
        mp_bar_rect = pygame.Rect(20, 160, panel_width - 30, 20)
        mp_bar = self.ui_manager.create_mp_bar(mp_bar_rect, base_mp, base_mp)
        self.add_component(mp_bar)
        self.player_stats_components["mp_bar"] = mp_bar

        # 魔法值文本
        mp_text_rect = pygame.Rect(20, 180, panel_width - 30, 20)
        mp_text = self.ui_manager.create_text(
            mp_text_rect,
            f"MP: {base_mp}/{base_mp}",
            "chinese_small",
            (220, 220, 220),
            "left"
        )
        self.add_component(mp_text)
        self.player_stats_components["mp_text"] = mp_text

        # 经验条
        initial_required_exp = 100  # 一级所需经验
        exp_bar_rect = pygame.Rect(20, 210, panel_width - 30, 20)
        exp_bar = self.ui_manager.create_exp_bar(exp_bar_rect, 0, initial_required_exp)
        self.add_component(exp_bar)
        self.player_stats_components["exp_bar"] = exp_bar

        # 经验值文本
        exp_text_rect = pygame.Rect(20, 230, panel_width - 30, 20)
        exp_text = self.ui_manager.create_text(
            exp_text_rect,
            f"经验: 0/{initial_required_exp}",
            "chinese_small",
            (220, 220, 220),
            "left"
        )
        self.add_component(exp_text)
        self.player_stats_components["exp_text"] = exp_text

        # 属性标题
        stats_title_rect = pygame.Rect(20, 270, panel_width - 30, 25)
        stats_title = self.ui_manager.create_text(
            stats_title_rect,
            "属性",
            "chinese_normal",
            (180, 180, 220),
            "left"
        )
        self.add_component(stats_title)

        # 玩家属性列表
        stats_y = 300
        stats_spacing = 25

        # 创建更详细的属性列表
        stats_info = [
            ("攻击", f"{base_attack}"),
            ("防御", f"{base_defense}"),
            ("魔法攻击", f"{0}"),
            ("道术攻击", f"{0}"),
            ("魔法防御", f"{class_stats['magic_defense']}"),
            ("攻击速度", f"{1.0}"),
            ("幸运", "0"),
            ("准确", f"{class_stats.get('accuracy', 0)}"),
            ("敏捷", f"{class_stats.get('agility', 0)}")
        ]

        for i, (stat_name, stat_value) in enumerate(stats_info):
            # 属性名
            stat_name_rect = pygame.Rect(30, stats_y + i * stats_spacing, 100, 20)
            stat_name_text = self.ui_manager.create_text(
                stat_name_rect,
                f"{stat_name}:",
                "chinese_small",
                (200, 200, 200),
                "left"
            )
            self.add_component(stat_name_text)

            # 属性值
            stat_value_rect = pygame.Rect(140, stats_y + i * stats_spacing, 80, 20)
            stat_value_text = self.ui_manager.create_text(
                stat_value_rect,
                f"{stat_value}",
                "chinese_small",
                (220, 220, 220),
                "right"
            )
            self.add_component(stat_value_text)
            self.player_stats_components[f"{stat_name}_text"] = stat_value_text

        # 在敏捷属性后面计算新组件的位置
        last_stat_y = stats_y + (len(stats_info) -1) * stats_spacing
        buff_y = last_stat_y + stats_spacing -5 # 增加一些垂直间距

        # 创建 Buff 显示文本组件
        buff_rect = pygame.Rect(20, buff_y, panel_width - 40, 10) # 增加高度以容纳多行
        buff_text = self.ui_manager.create_text(
            buff_rect,
            "状态: 无",
            "chinese_small",
            (200, 200, 255), # 用淡蓝色区分
            "left"
        )
        self.add_component(buff_text)
        self.player_stats_components["buff_text"] = buff_text # 添加到组件字典

        # 装备栏标题和装备栏位已移除 - 这是多余的显示

    def _create_central_area(self):
        """创建中央区域"""
        screen_size = pygame.display.get_surface().get_size()

        # 计算位置：居中，且在左右面板之间
        left_panel_width = 250
        right_panel_width = 250

        central_width = screen_size[0] - left_panel_width - right_panel_width
        central_x = left_panel_width


        # 中央区域尺寸和位置
        left_panel_width = 250
        right_panel_width = 250
        central_width = screen_size[0] - left_panel_width - right_panel_width - 30
        central_height = screen_size[1] - 160  # 减去顶部和底部的高度以及间隔

        # 中央区域容器
        central_panel = Panel(
            pygame.Rect(left_panel_width + 20, 60, central_width, central_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(central_panel)
        self.components_map["central_panel"] = central_panel

        # 战斗日志区域 - 移至中央区域底部
        log_panel_height = 200  # 适合显示8行日志
        log_panel_y = central_panel.rect.bottom - log_panel_height - 10  # 距离中央区域底部10像素

        log_panel = Panel(
            pygame.Rect(left_panel_width + 30, log_panel_y, central_width - 20, log_panel_height),
            color=(30, 30, 50),
            border_color=(50, 50, 70),
            border_width=1
        )
        self.add_component(log_panel)
        self.components_map["log_panel"] = log_panel

        # 战斗日志标题
        log_title_rect = pygame.Rect(left_panel_width + 40, log_panel_y + 10, 200, 25)
        log_title = self.ui_manager.create_text(
            log_title_rect,
            "战斗日志",
            "chinese_normal",
            (180, 180, 220),
            "left"
        )
        self.add_component(log_title)

        # 添加滚动提示
        scroll_hint_rect = pygame.Rect(log_panel.rect.right - 150, log_panel_y + 10, 140, 25)
        scroll_hint = self.ui_manager.create_text(
            scroll_hint_rect,
            "使用滚轮查看更多",
            "chinese_small",
            (150, 150, 180),
            "right"
        )
        self.add_component(scroll_hint)

        # 战斗日志条目（固定显示8行）
        log_spacing = 20  # 行间距
        self.log_display_count = 8  # 显示的日志行数
        self.log_scroll_offset = 0  # 滚动偏移量

        for i in range(self.log_display_count):
            log_entry_rect = pygame.Rect(
                left_panel_width + 40,
                log_panel_y + 40 + i * log_spacing,  # 标题下方40像素开始
                central_width - 60,
                18
            )
            log_entry = self.ui_manager.create_text(
                log_entry_rect,
                "",
                "chinese_small",
                (200, 200, 200),
                "left"
            )
            self.add_component(log_entry)
            self.battle_logs.append(log_entry)

    def _create_monster_panel(self):
        """创建右侧怪物面板"""
        screen_size = pygame.display.get_surface().get_size()

        panel_width = 250
        panel_height = screen_size[1] - 100  # 减去顶部和底部的高度

        # 怪物面板容器
        monster_panel = Panel(
            pygame.Rect(screen_size[0] - panel_width - 10, 60, panel_width, panel_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(monster_panel)
        self.components_map["monster_panel"] = monster_panel

        # 怪物名称
        name_rect = pygame.Rect(screen_size[0] - panel_width + 10, 70, panel_width - 20, 30)
        name_text = self.ui_manager.create_text(
            name_rect,
            "无怪物",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(name_text)
        self.monster_components["name"] = name_text

        # 怪物等级
        level_rect = pygame.Rect(screen_size[0] - panel_width + 10, 100, panel_width - 20, 30)
        level_text = self.ui_manager.create_text(
            level_rect,
            "等级: -",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(level_text)
        self.monster_components["level"] = level_text

        # 怪物生命值条
        hp_bar_rect = pygame.Rect(screen_size[0] - panel_width + 10, 135, panel_width - 30, 20)
        hp_bar = self.ui_manager.create_hp_bar(hp_bar_rect, 0, 100)
        self.add_component(hp_bar)
        self.monster_components["hp_bar"] = hp_bar

        # 怪物生命值文本
        hp_text_rect = pygame.Rect(screen_size[0] - panel_width + 10, 155, panel_width - 30, 20)
        hp_text = self.ui_manager.create_text(
            hp_text_rect,
            "HP: 0/0",
            "chinese_small",
            (220, 220, 220),
            "left"
        )
        self.add_component(hp_text)
        self.monster_components["hp_text"] = hp_text

        # 怪物属性标题
        stats_title_rect = pygame.Rect(screen_size[0] - panel_width + 10, 190, panel_width - 30, 25)
        stats_title = self.ui_manager.create_text(
            stats_title_rect,
            "怪物属性",
            "chinese_normal",
            (180, 180, 220),
            "left"
        )
        self.add_component(stats_title)

        # 怪物属性列表
        stats = [
            ("攻击", "攻击: -"),
            ("防御", "防御: -"),
            ("魔法防御", "魔防: -"), # 添加这一行
            ("经验", "经验: -"),
        ]

        stats_y = 225
        stats_spacing = 30

        for i, (stat_id, stat_display) in enumerate(stats):
            stat_rect = pygame.Rect(
                screen_size[0] - panel_width + 20,
                stats_y + i * stats_spacing,
                panel_width - 40,
                25
            )
            stat_text = self.ui_manager.create_text(
                stat_rect,
                stat_display,
                "chinese_small",
                (200, 200, 200),
                "left"
            )
            self.add_component(stat_text)
            # 这里会根据stat_id创建对应的键，包括"魔法防御"
            self.monster_components[stat_id] = stat_text

        # 添加怪物图片区域标题
        monster_img_title_rect = pygame.Rect(screen_size[0] - panel_width + 10, 350, panel_width - 30, 25)
        monster_img_title = self.ui_manager.create_text(
            monster_img_title_rect,
            "怪物图片",
            "chinese_normal",
            (180, 180, 220),
            "left"
        )
        self.add_component(monster_img_title)
        self.monster_components["img_title"] = monster_img_title

        # 添加怪物图片面板区域
        monster_img_panel = Panel(
            pygame.Rect(screen_size[0] - panel_width + 30, 380, panel_width - 60, 150),
            color=(30, 30, 40),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(monster_img_panel)
        self.monster_components["img_panel"] = monster_img_panel

        # 添加自动战斗按钮 - 调整位置和大小，使其更加突出
        auto_battle_button = self.ui_manager.create_button(
            pygame.Rect(screen_size[0] - panel_width + 30, panel_height - 60, panel_width - 60, 50),
            "自动战斗",
            self._on_auto_battle_click,
            "chinese_normal"
        )
        self.add_component(auto_battle_button)
        self.components_map["auto_battle_button"] = auto_battle_button

        # 战斗提示 - 调整位置，放在自动战斗按钮上方
        hint_rect = pygame.Rect(screen_size[0] - panel_width + 10, panel_height - 100, panel_width - 20, 30)
        hint_text = self.ui_manager.create_text(
            hint_rect,
            "点击自动战斗按钮开始战斗",
            "chinese_small",
            (200, 200, 200),
            "center"
        )
        self.add_component(hint_text)
        self.monster_components["hint"] = hint_text

    def _create_bottom_bar(self):
        """创建底部控制栏"""
        screen_size = pygame.display.get_surface().get_size()

        # 底部面板
        bottom_panel = self.ui_manager.create_panel(
            pygame.Rect(0, screen_size[1] - 60, screen_size[0], 60),
            color=(20, 20, 30),
            border_color=(40, 40, 60),
            border_width=1
        )
        self.add_component(bottom_panel)

        # 创建底部按钮
        button_width = 80
        button_height = 40
        button_gap = 10
        start_x = (screen_size[0] - (button_width + button_gap) * 7 + button_gap) // 2  # 增加一个按钮，调整起始位置
        button_y = screen_size[1] - 50

        tab_buttons = [
            ("背包", self._on_inventory_click),
            ("装备", self._on_equipment_click),
            ("技能", self._on_skills_click),
            ("地图", self._on_map_click),
            ("商店", self._on_shop_click),
            ("签到", self._on_signin_click),
            ("战斗统计", self._on_battle_stats_click)  # 添加战斗统计按钮
        ]

        for i, (text, callback) in enumerate(tab_buttons):
            btn_rect = pygame.Rect(
                start_x + (button_width + button_gap) * i,
                button_y,
                button_width,
                button_height
            )
            btn = self.ui_manager.create_button(
                btn_rect,
                text,
                callback,
            "chinese_normal"
        )
            self.add_component(btn)

        # 添加设置按钮（右下角）
        settings_btn = self.ui_manager.create_button(
            pygame.Rect(screen_size[0] - 90, button_y, button_width, button_height),
            "设置",
            self._on_settings_click,
            "chinese_normal"
        )
        self.add_component(settings_btn)

        # 计算战斗信息栏宽度，使技能栏与其一致
        left_panel_width = 250
        right_panel_width = 250
        central_width = screen_size[0] - left_panel_width - right_panel_width - 30

        # 创建美观的技能栏，与战斗信息栏宽度相同
        skill_bar_height = 55  # 减小技能栏高度
        skill_bar_panel = self.ui_manager.create_panel(
            pygame.Rect(left_panel_width + 20, screen_size[1] - 130, central_width, skill_bar_height),
            color=(25, 25, 35, 220),  # 半透明背景
            border_color=(70, 70, 120),
            border_width=2
        )
        self.add_component(skill_bar_panel)

        # 技能栏标题
        skill_title = self.ui_manager.create_text(
            pygame.Rect(left_panel_width + 30, screen_size[1] - 126, 80, 25),
            "技能栏",
            "chinese_small",
            (220, 220, 240),
            "left"
        )
        self.add_component(skill_title)

        # 创建技能按钮
        self.skill_buttons = {}
        skill_button_width = 45  # 更小的按钮尺寸
        skill_button_height = 45
        skill_button_gap = 15  # 按钮间距

        # 计算按钮占用的总宽度（6个常规技能槽 + 1个初始技能槽）
        total_buttons_width = (skill_button_width + skill_button_gap) * 7 - skill_button_gap
        # 计算起始位置，使按钮组在技能栏中居中
        skill_start_x = left_panel_width + 20 + (central_width - total_buttons_width) // 2
        skill_start_y = screen_size[1] - 125

        # 创建全局技能冷却条
        cooldown_bar_width = 200  # 按照需求设置宽度为200像素
        cooldown_bar_height = 2   # 按照需求设置高度为2像素
        cooldown_bar_x = skill_start_x + (total_buttons_width - cooldown_bar_width) // 2  # 居中对齐
        cooldown_bar_y = skill_start_y + skill_button_height + 5  # 技能按钮下方5像素

        # 创建全局技能冷却进度条
        cooldown_bar = self.ui_manager.create_progress_bar(
            pygame.Rect(cooldown_bar_x, cooldown_bar_y, cooldown_bar_width, cooldown_bar_height),
            0,  # 初始值
            1.0,  # 最大值
            {
                "background": (50, 50, 70),  # 深色背景
                "fill": (100, 100, 255),     # 蓝色填充
                "border": (80, 80, 120)      # 边框颜色
            }
        )
        self.add_component(cooldown_bar)
        self.components_map["global_cooldown_bar"] = cooldown_bar

        # 创建初始技能按钮（0号位置）
        initial_skill_btn_rect = pygame.Rect(
            skill_start_x,
            skill_start_y,
            skill_button_width,
            skill_button_height
        )
        # 创建美观的初始技能按钮
        initial_skill_btn = self.ui_manager.create_button(
            initial_skill_btn_rect,
            "初",
            lambda: self._on_use_skill_click(0),  # 使用0作为索引
            "chinese_small"  # 使用小字体
        )
        # 添加右键点击事件处理
        initial_skill_btn.on_right_click = lambda: self._on_skill_right_click(0)  # 使用0作为索引
        # 设置更美观的样式（使用金色边框突出显示）
        initial_skill_btn.colors.update({
            "normal": (40, 40, 70),
            "hover": (60, 60, 100),
            "pressed": (30, 30, 60),
            "text": (200, 200, 220),
            "border": (255, 215, 0)  # 金色边框
        })
        initial_skill_btn.border_width = 2  # 加粗边框

        # 设置按钮为非活动状态（会在更新时由update_skill_buttons激活）
        initial_skill_btn.set_active(False)
        self.add_component(initial_skill_btn)
        self.skill_buttons[0] = initial_skill_btn  # 使用0作为索引

        # 创建6个常规技能按钮位置（1-6号位置）
        for i in range(1, 7):
            skill_btn_rect = pygame.Rect(
                skill_start_x + (skill_button_width + skill_button_gap) * i,
                skill_start_y,
                skill_button_width,
                skill_button_height
            )
            # 创建美观的技能按钮
            skill_btn = self.ui_manager.create_button(
                skill_btn_rect,
                f"{i}",
                lambda idx=i: self._on_use_skill_click(idx),
                "chinese_small"  # 使用小字体
            )
            # 添加右键点击事件处理
            skill_btn.on_right_click = lambda idx=i: self._on_skill_right_click(idx)
            # 设置更美观的样式
            skill_btn.colors.update({
                "normal": (40, 40, 70),
                "hover": (60, 60, 100),
                "pressed": (30, 30, 60),
                "text": (200, 200, 220),
                "border": (80, 80, 120)
            })

            # 设置按钮为非活动状态（会在更新时由update_skill_buttons激活）
            skill_btn.set_active(False)
            self.add_component(skill_btn)
            self.skill_buttons[i] = skill_btn

    def _on_battle_stats_click(self):
        """点击战斗统计按钮的处理函数"""
        logger.info("点击了战斗统计按钮")
        self.ui_manager.show_screen("battle_stats")

    def _create_death_panel(self):
        """创建死亡提示面板 (修改为不创建实际面板，信息在日志显示)"""
        screen_size = pygame.display.get_surface().get_size()

        # # 死亡提示面板容器 (注释掉)
        # death_panel_width = 250
        # death_panel_height = 150
        # death_panel_rect = pygame.Rect(
        #     (screen_size[0] - death_panel_width) // 2,
        #     (screen_size[1] - death_panel_height) // 2,
        #     death_panel_width,
        #     death_panel_height
        # )
        # death_panel = Panel(
        #     death_panel_rect,
        #     color=(40, 40, 60),
        #     border_color=(60, 60, 80),
        #     border_width=1
        # )
        # self.add_component(death_panel)
        # self.components_map["death_panel"] = death_panel
        # death_panel.visible = False # 确保默认隐藏

        # # 死亡消息文本 (注释掉)
        # death_message_rect = pygame.Rect(
        #     death_panel_rect.left + 20,
        #     death_panel_rect.top + 20,
        #     death_panel_width - 40,
        #     death_panel_height - 40
        # )
        # death_message_text = self.ui_manager.create_text(
        #     death_message_rect,
        #     "",
        #     "chinese_normal",
        #     (220, 220, 220),
        #     "center"
        # )
        # self.add_component(death_message_text)
        # self.death_message = death_message_text
        # self.death_message.visible = False # 确保默认隐藏

        # # 死亡计时器 (保留，可能复活逻辑需要？如果不需要可以后续移除)
        # self.death_timer = self.ui_manager.create_timer(pygame.Rect(0,0,0,0), 3000) # Rect 不重要了
        # self.death_timer.on_timer_end = self._on_death_timer_end
        logger.info("死亡提示面板创建逻辑已禁用，信息将显示在日志中")

    def _create_dungeon_timer(self):
        """创建副本倒计时显示"""
        screen_size = pygame.display.get_surface().get_size()

        # 创建副本倒计时组件 (右下角)
        dungeon_timer_panel = Panel(
            pygame.Rect(screen_size[0] - 200, screen_size[1] - 120, 180, 50),
            color=(30, 30, 50, 220),  # 半透明深色背景
            border_color=(100, 100, 150),
            border_width=2
        )
        dungeon_timer_panel.visible = False  # 默认隐藏
        self.add_component(dungeon_timer_panel)
        self.components_map["dungeon_timer_panel"] = dungeon_timer_panel

        # 副本倒计时标题
        dungeon_timer_title = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 195, screen_size[1] - 117, 170, 20),
            "副本剩余时间",
            "chinese_small",
            (220, 220, 255),
            "center"
        )
        dungeon_timer_title.visible = False  # 默认隐藏
        self.add_component(dungeon_timer_title)
        self.components_map["dungeon_timer_title"] = dungeon_timer_title

        # 副本倒计时文本
        dungeon_timer_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 195, screen_size[1] - 95, 170, 25),
            "--:--",
            "chinese_large",
            (255, 200, 100),  # 橙黄色，更醒目
            "center"
        )
        dungeon_timer_text.visible = False  # 默认隐藏
        self.add_component(dungeon_timer_text)
        self.components_map["dungeon_timer_text"] = dungeon_timer_text

    def _update_dungeon_timer(self):
        """更新副本倒计时显示

        副本系统已移除，此方法仅确保相关UI组件保持隐藏状态
        """
        # 获取倒计时组件
        timer_panel = self.components_map.get("dungeon_timer_panel")
        timer_title = self.components_map.get("dungeon_timer_title")
        timer_text = self.components_map.get("dungeon_timer_text")

        if not timer_panel or not timer_title or not timer_text:
            return

        # 副本系统已移除，确保所有相关UI组件都隐藏
        timer_panel.visible = False
        timer_title.visible = False
        timer_text.visible = False

    def update_player_stats(self, player):
        """更新玩家属性显示"""
        if not player:
            return

        # 使用简化的日志记录，不要每次都记录详细属性
        logger.debug(f"更新玩家属性显示")

        # 更新名称和等级
        player_name = player.name if player.name else "未命名"  # 如果没有名字则显示"未命名"
        self.player_stats_components["name"].set_text(f"{player_name} {player.character_class} Lv.{player.level}")

        # 更新生命值
        self.player_stats_components["hp_bar"].set_value(player.hp)
        self.player_stats_components["hp_bar"].set_max_value(player.max_hp)
        self.player_stats_components["hp_text"].set_text(f"HP: {player.hp}/{player.max_hp}")

        # 更新魔法值
        self.player_stats_components["mp_bar"].set_value(player.mp)
        self.player_stats_components["mp_bar"].set_max_value(player.max_mp)
        self.player_stats_components["mp_text"].set_text(f"MP: {player.mp}/{player.max_mp}")

        # 更新经验值
        exp_required = player.required_exp  # 使用required_exp替代calculate_exp_for_next_level
        self.player_stats_components["exp_bar"].set_value(player.exp)
        self.player_stats_components["exp_bar"].set_max_value(exp_required)
        self.player_stats_components["exp_text"].set_text(f"经验: {player.exp}/{exp_required}")

        # 更新基础属性
        try:
            # 更新攻击力（显示总值和装备加成）
            attack_min, attack_max = player.get_damage_range()
            # 尝试获取基础攻击力
            base_attack_min = getattr(player, "base_attack_min", attack_min)
            base_attack_max = getattr(player, "base_attack_max", attack_max)
            # 计算装备加成
            attack_min_bonus = attack_min - base_attack_min
            attack_max_bonus = attack_max - base_attack_max

            # 如果有加成，显示加成部分
            if attack_min_bonus != 0 or attack_max_bonus != 0:
                attack_text = f"{attack_min}-{attack_max}"
                # 如果最小和最大攻击力的加成相同
                if attack_min_bonus == attack_max_bonus and attack_min_bonus > 0:
                    attack_text += f" (+{attack_min_bonus})"
                elif attack_min_bonus == attack_max_bonus and attack_min_bonus < 0:
                    attack_text += f" ({attack_min_bonus})"
                else:
                    # 如果加成不同，分别显示
                    attack_bonus_text = ""
                    if attack_min_bonus > 0:
                        attack_bonus_text += f"+{attack_min_bonus}"
                    elif attack_min_bonus < 0:
                        attack_bonus_text += f"{attack_min_bonus}"

                    attack_bonus_text += "/"

                    if attack_max_bonus > 0:
                        attack_bonus_text += f"+{attack_max_bonus}"
                    elif attack_max_bonus < 0:
                        attack_bonus_text += f"{attack_max_bonus}"

                    attack_text += f" ({attack_bonus_text})"
            else:
                attack_text = f"{attack_min}-{attack_max}"
            self.player_stats_components["攻击_text"].set_text(attack_text)

            # 更新防御力（显示总值和装备加成）
            # 获取基础防御值
            base_defense = getattr(player, "base_defense", 0)

            # 获取防御范围值，假设防御也有范围
            defense_min = getattr(player, "defense_min", player.defense)
            defense_max = getattr(player, "defense_max", int(player.defense * 1.2))  # 如果没有范围值，最大防御默认为基础值的1.2倍

            # 尝试获取基础防御力范围
            base_defense_min = getattr(player, "base_defense_min", base_defense)
            base_defense_max = getattr(player, "base_defense_max", int(base_defense * 1.2))

            # 计算装备加成
            defense_min_bonus = defense_min - base_defense_min
            defense_max_bonus = defense_max - base_defense_max

            # 如果有加成，显示加成部分
            if defense_min_bonus != 0 or defense_max_bonus != 0:
                defense_text = f"{defense_min}-{defense_max}"
                # 如果最小和最大防御的加成相同
                if defense_min_bonus == defense_max_bonus and defense_min_bonus > 0:
                    defense_text += f" (+{defense_min_bonus})"
                elif defense_min_bonus == defense_max_bonus and defense_min_bonus < 0:
                    defense_text += f" ({defense_min_bonus})"
                else:
                    # 如果加成不同，分别显示
                    defense_bonus_text = ""
                    if defense_min_bonus > 0:
                        defense_bonus_text += f"+{defense_min_bonus}"
                    elif defense_min_bonus < 0:
                        defense_bonus_text += f"{defense_min_bonus}"

                    defense_bonus_text += "/"

                    if defense_max_bonus > 0:
                        defense_bonus_text += f"+{defense_max_bonus}"
                    elif defense_max_bonus < 0:
                        defense_bonus_text += f"{defense_max_bonus}"

                    defense_text += f" ({defense_bonus_text})"
            else:
                defense_text = f"{defense_min}-{defense_max}"
            self.player_stats_components["防御_text"].set_text(defense_text)

            # 更新魔法相关属性（显示总值和装备加成）
            try:
                # magic_attack属性返回一个元组(min_magic, max_magic)
                min_magic, max_magic = player.magic_attack

                # 尝试获取基础魔法攻击力
                base_min_magic = getattr(player, "base_min_magic", min_magic)
                base_max_magic = getattr(player, "base_max_magic", max_magic)

                # 计算装备加成
                magic_min_bonus = min_magic - base_min_magic
                magic_max_bonus = max_magic - base_max_magic

                # 如果有加成，显示加成部分
                if magic_min_bonus != 0 or magic_max_bonus != 0:
                    magic_text = f"{min_magic}-{max_magic}"
                    # 如果最小和最大魔法攻击的加成相同
                    if magic_min_bonus == magic_max_bonus and magic_min_bonus > 0:
                        magic_text += f" (+{magic_min_bonus})"
                    elif magic_min_bonus == magic_max_bonus and magic_min_bonus < 0:
                        magic_text += f" ({magic_min_bonus})"
                    else:
                        # 如果加成不同，分别显示
                        magic_bonus_text = ""
                        if magic_min_bonus > 0:
                            magic_bonus_text += f"+{magic_min_bonus}"
                        elif magic_min_bonus < 0:
                            magic_bonus_text += f"{magic_min_bonus}"

                        magic_bonus_text += "/"

                        if magic_max_bonus > 0:
                            magic_bonus_text += f"+{magic_max_bonus}"
                        elif magic_max_bonus < 0:
                            magic_bonus_text += f"{magic_max_bonus}"

                        magic_text += f" ({magic_bonus_text})"
                else:
                    magic_text = f"{min_magic}-{max_magic}"
            except (AttributeError, TypeError, ValueError) as e:
                # 如果获取魔法攻击失败，显示默认值
                print(f"获取魔法攻击范围失败: {e}")
                magic_text = "0-0"

            self.player_stats_components["魔法攻击_text"].set_text(magic_text)

            # 更新道术相关属性（显示总值和装备加成）
            try:
                # taoism_attack属性返回一个元组(min_taoism, max_taoism)
                min_taoism, max_taoism = player.taoism_attack

                # 尝试获取基础道术攻击力
                base_min_taoism = getattr(player, "base_min_taoism", min_taoism)
                base_max_taoism = getattr(player, "base_max_taoism", max_taoism)

                # 计算装备加成
                taoism_min_bonus = min_taoism - base_min_taoism
                taoism_max_bonus = max_taoism - base_max_taoism

                # 如果有加成，显示加成部分
                if taoism_min_bonus != 0 or taoism_max_bonus != 0:
                    taoism_text = f"{min_taoism}-{max_taoism}"
                    # 如果最小和最大道术攻击的加成相同
                    if taoism_min_bonus == taoism_max_bonus and taoism_min_bonus > 0:
                        taoism_text += f" (+{taoism_min_bonus})"
                    elif taoism_min_bonus == taoism_max_bonus and taoism_min_bonus < 0:
                        taoism_text += f" ({taoism_min_bonus})"
                    else:
                        # 如果加成不同，分别显示
                        taoism_bonus_text = ""
                        if taoism_min_bonus > 0:
                            taoism_bonus_text += f"+{taoism_min_bonus}"
                        elif taoism_min_bonus < 0:
                            taoism_bonus_text += f"{taoism_min_bonus}"

                        taoism_bonus_text += "/"

                        if taoism_max_bonus > 0:
                            taoism_bonus_text += f"+{taoism_max_bonus}"
                        elif taoism_max_bonus < 0:
                            taoism_bonus_text += f"{taoism_max_bonus}"

                        taoism_text += f" ({taoism_bonus_text})"
                else:
                    taoism_text = f"{min_taoism}-{max_taoism}"
            except (AttributeError, TypeError, ValueError) as e:
                # 如果获取道术攻击失败，显示默认值
                print(f"获取道术攻击范围失败: {e}")
                taoism_text = "0-0"

            self.player_stats_components["道术攻击_text"].set_text(taoism_text)

            # 更新魔法防御（显示总值和装备加成）
            # 获取基础魔法防御值
            base_magic_defense = getattr(player, "base_magic_defense", 0)

            # 获取魔法防御范围值，假设魔法防御也有范围
            magic_defense_min = getattr(player, "magic_defense_min", player.magic_defense)
            magic_defense_max = getattr(player, "magic_defense_max", int(player.magic_defense * 1.2))  # 如果没有范围值，最大魔防默认为基础值的1.2倍

            # 尝试获取基础魔法防御范围
            base_magic_defense_min = getattr(player, "base_magic_defense_min", base_magic_defense)
            base_magic_defense_max = getattr(player, "base_magic_defense_max", int(base_magic_defense * 1.2))

            # 计算装备加成
            magic_defense_min_bonus = magic_defense_min - base_magic_defense_min
            magic_defense_max_bonus = magic_defense_max - base_magic_defense_max

            # 如果有加成，显示加成部分
            if magic_defense_min_bonus != 0 or magic_defense_max_bonus != 0:
                magic_defense_text = f"{magic_defense_min}-{magic_defense_max}"
                # 如果最小和最大魔法防御的加成相同
                if magic_defense_min_bonus == magic_defense_max_bonus and magic_defense_min_bonus > 0:
                    magic_defense_text += f" (+{magic_defense_min_bonus})"
                elif magic_defense_min_bonus == magic_defense_max_bonus and magic_defense_min_bonus < 0:
                    magic_defense_text += f" ({magic_defense_min_bonus})"
                else:
                    # 如果加成不同，分别显示
                    magic_defense_bonus_text = ""
                    if magic_defense_min_bonus > 0:
                        magic_defense_bonus_text += f"+{magic_defense_min_bonus}"
                    elif magic_defense_min_bonus < 0:
                        magic_defense_bonus_text += f"{magic_defense_min_bonus}"

                    magic_defense_bonus_text += "/"

                    if magic_defense_max_bonus > 0:
                        magic_defense_bonus_text += f"+{magic_defense_max_bonus}"
                    elif magic_defense_max_bonus < 0:
                        magic_defense_bonus_text += f"{magic_defense_max_bonus}"

                    magic_defense_text += f" ({magic_defense_bonus_text})"
            else:
                magic_defense_text = f"{magic_defense_min}-{magic_defense_max}"
            self.player_stats_components["魔法防御_text"].set_text(magic_defense_text)

            # 更新次要属性（显示总值和装备加成）
            # 获取基础攻击速度和装备加成
            base_attack_speed = getattr(player, "base_attack_speed", 1.0)
            equipment_attack_speed = player.attack_speed - base_attack_speed

            # 格式化显示，如果有装备加成则显示加成部分
            if abs(equipment_attack_speed) > 0.01:  # 考虑浮点数精度问题
                attack_speed_text = f"{player.attack_speed:.2f}"
                if equipment_attack_speed > 0:
                    attack_speed_text += f" (+{equipment_attack_speed:.2f})"
                else:
                    attack_speed_text += f" ({equipment_attack_speed:.2f})"
            else:
                attack_speed_text = f"{player.attack_speed:.2f}"
            self.player_stats_components["攻击速度_text"].set_text(attack_speed_text)

            # 获取基础幸运和装备加成
            base_luck = player.base_luck
            total_luck = player.luck
            equipment_luck = total_luck - base_luck

            # 格式化显示，如果有装备加成则显示加成部分
            if equipment_luck != 0:
                luck_text = f"{total_luck}"
                if equipment_luck > 0:
                    luck_text += f" (+{equipment_luck})"
                else:
                    luck_text += f" ({equipment_luck})"
            else:
                luck_text = f"{total_luck}"
            self.player_stats_components["幸运_text"].set_text(luck_text)

            # 更新准确属性（显示总值和装备加成）
            base_accuracy = getattr(player, "base_base_accuracy", 0)  # 原始基础准确度
            equipment_accuracy = player.base_accuracy - base_accuracy

            # 格式化显示准确度
            if equipment_accuracy != 0:
                accuracy_text = f"{player.base_accuracy}"
                if equipment_accuracy > 0:
                    accuracy_text += f" (+{equipment_accuracy})"
                else:
                    accuracy_text += f" ({equipment_accuracy})"
            else:
                accuracy_text = f"{player.base_accuracy}"
            self.player_stats_components["准确_text"].set_text(accuracy_text)

            # 更新敏捷属性（显示总值和装备加成）
            base_agility = getattr(player, "base_agility", 0)  # 原始基础敏捷
            total_agility = getattr(player, "agility", 0)  # 当前总敏捷
            equipment_agility = total_agility - base_agility

            # 格式化显示敏捷
            if equipment_agility != 0:
                agility_text = f"{total_agility}"
                if equipment_agility > 0:
                    agility_text += f" (+{equipment_agility})"
                else:
                    agility_text += f" ({equipment_agility})"
            else:
                agility_text = f"{total_agility}"
            self.player_stats_components["敏捷_text"].set_text(agility_text)

            # 更新攻击速度和暴击信息 (如果UI中有这些组件)
            if "暴击率_text" in self.player_stats_components:
                equipment_bonus = player.get_equipment_bonus()
                crit_rate = equipment_bonus.get("crit_rate", 0) / 100.0 * 100  # 转换为百分比
                self.player_stats_components["暴击率_text"].set_text(f"{crit_rate:.1f}%")

        except Exception as e:
            logger.error(f"更新玩家属性显示出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        logger.debug(f"玩家属性显示更新完成")

        # 显示当前活跃的buff
        buff_display_text = "状态: 无" # 默认文本
        need_recalculate = False  # 标记是否需要重新计算属性

        if hasattr(player, "buffs") and player.buffs:
            current_time = time.time()
            active_buffs = []
            expired_buffs = []  # 收集过期的buff，统一处理

            # 遍历所有 buff
            for buff_id, buff in list(player.buffs.items()): # 使用 list() 避免迭代时修改字典
                remaining_time = buff["duration"] - (current_time - buff["start_time"])
                if remaining_time > 0:
                    remaining_seconds = int(remaining_time)
                    buff_name = buff.get("skill_name", buff_id) # 获取技能名或ID
                    buff_value = buff.get("value", "")
                    buff_type = buff.get("type", "")

                    # 根据类型格式化显示
                    desc = f"{buff_name}"
                    if buff_type == "damage_reduction":
                        reduction = int(buff_value * 100)
                        desc += f"(减伤{reduction}%)"
                    elif buff_type == "defense":
                        desc += f"(+防{buff_value})"
                    elif buff_type == "magic_defense":
                        desc += f"(+魔防{buff_value})"
                    elif buff_type == "agility":
                        desc += f"(+敏{buff_value})"
                    # 可以根据需要添加更多 buff 类型

                    # 添加剩余时间，根据时间长短使用不同颜色
                    if remaining_seconds < 5:
                        # 剩余时间少于5秒，使用红色警告
                        desc += f"(<color=#FF5555>{remaining_seconds}秒</color>)"
                    else:
                        desc += f"({remaining_seconds}秒)"

                    active_buffs.append(desc)
                else:
                    # 收集过期的buff，稍后统一处理
                    expired_buffs.append(buff_id)
                    logger.info(f"检测到过期Buff: '{buff.get('skill_name', buff_id)}'")

            # 统一处理过期的buff
            if expired_buffs:
                for buff_id in expired_buffs:
                    buff_name = player.buffs[buff_id].get("skill_name", buff_id)
                    del player.buffs[buff_id]
                    logger.info(f"Buff '{buff_name}' 已过期并移除")
                    # 标记需要重新计算属性
                    need_recalculate = True
                    # 添加系统消息
                    if hasattr(self.game_manager, 'add_log'):
                        self.game_manager.add_log(f"【{buff_name}】效果已结束")

            # 构建显示文本
            if active_buffs:
                # 使用换行符分隔多个buff，使显示更清晰
                if len(active_buffs) > 1:
                    buff_display_text = "状态:\n" + "\n".join(active_buffs)
                else:
                    buff_display_text = "状态: " + active_buffs[0]

        # 如果有buff过期，重新计算玩家属性
        if need_recalculate and hasattr(player, 'recalculate_stats'):
            logger.info("由于Buff过期，重新计算玩家属性")
            player.recalculate_stats()

        # 更新 Buff 显示 UI 组件
        if "buff_text" in self.player_stats_components:
            self.player_stats_components["buff_text"].set_text(buff_display_text)

    def update_monster_display(self, monster=None):
        """更新怪物显示，如果怪物是None则清空怪物显示"""
        # 如果怪物为空或怪物面板不存在（战斗系统初始化问题）
        if not monster or "monster_panel" not in self.components_map:
            # 如果要清空怪物，则隐藏怪物面板
            if "monster_panel" in self.components_map:
                self.monster_components["name"].set_text("未遭遇怪物")
                self.monster_components["hp_text"].set_text("HP: 0/0")
                self.monster_components["hp_bar"].set_value(0)
                self.monster_components["hp_bar"].set_max_value(100)
                # 确保使用正确的键名清空
                if "攻击" in self.monster_components:
                    self.monster_components["攻击"].set_text("攻击: 0-0") # 最好显示范围
                if "防御" in self.monster_components:
                    self.monster_components["防御"].set_text("防御: 0")
                if "魔法防御" in self.monster_components: # 添加对魔法防御的清空
                    self.monster_components["魔法防御"].set_text("魔防: 0")
                if "经验" in self.monster_components:
                    self.monster_components["经验"].set_text("经验: 0")
                if "hint" in self.monster_components: # 确保使用 "hint"
                    self.monster_components["hint"].set_text("点击自动战斗开始冒险！")
            return

        # 更新怪物信息
        if monster:
            self.monster_components["name"].set_text(monster.name)
            self.monster_components["level"].set_text(f"等级: {monster.level}")
            self.monster_components["hp_text"].set_text(f"HP: {monster.hp}/{monster.max_hp}")
            self.monster_components["hp_bar"].set_value(monster.hp)
            self.monster_components["hp_bar"].set_max_value(monster.max_hp)

            # 处理攻击范围
            min_attack, max_attack = 1, 2  # 默认值
            if hasattr(monster, "attack_range"):
                if isinstance(monster.attack_range, dict):
                    min_attack = monster.attack_range.get("min", 1)
                    max_attack = monster.attack_range.get("max", 2)
                elif isinstance(monster.attack_range, (list, tuple)) and len(monster.attack_range) >= 2:
                    min_attack, max_attack = monster.attack_range[0], monster.attack_range[1]
                elif hasattr(monster, "attack") and monster.attack > 0:
                    min_attack = max_attack = monster.attack

            self.monster_components["攻击"].set_text(f"攻击: {min_attack}-{max_attack}")
            self.monster_components["防御"].set_text(f"防御: {monster.defense}")
            self.monster_components["魔法防御"].set_text(f"魔防: {monster.magic_defense}")
            self.monster_components["hint"].set_text("战斗已开始！")

            # --- 修改：尝试多种路径加载怪物图片 ---
            loaded_image = None
            image_key = monster.name # 使用怪物名字作为 key

            # 检查缓存
            if image_key in self.monster_image_cache:
                loaded_image = self.monster_image_cache[image_key]
            else:
                # 不在缓存中，尝试加载
                try:
                    current_map = self.game_manager.current_map if hasattr(self.game_manager, 'current_map') else None
                    # --- 修改：尝试多种扩展名加载怪物图片 ---
                    monster_base_name = monster.name
                    extensions_to_try = [".png", ".jpg", ".jpeg"]
                    possible_base_paths = []

                    # --- 修改：移除 "Demo/" 前缀，路径应相对于 base_path 下的 assets ---
                    possible_base_paths.append(f"assets/images/monsters/{monster_base_name}")
                    # --- 结束修改 ---

                    logger.debug(f"尝试加载怪物图片 '{monster.name}'，搜索基础路径: {possible_base_paths}")

                    # 遍历基础路径和扩展名组合
                    for base_path in possible_base_paths: # 实际上现在只有一个基础路径
                        for ext in extensions_to_try:
                            full_path = base_path + ext
                            # 使用 resource_manager 加载
                            img = self.resource_manager.load_image(full_path)
                            if img:
                                loaded_image = img
                                logger.info(f"成功加载并缓存怪物图像：{full_path} (Key: {image_key})")
                                break # 找到图片后停止内部循环
                        if loaded_image: # 如果在内层循环找到了图片，也跳出外层循环
                            break
                    # --- 结束修改 ---

                    # 如果循环结束仍未加载到图片
                    if not loaded_image:
                         logger.warning(f"在所有路径和扩展名中均未找到怪物图片: {monster.name}")

                    # 存入缓存 (即使加载失败也存 None)
                    self.monster_image_cache[image_key] = loaded_image
                except Exception as e: # 恢复异常处理块
                    logger.warning(f"加载或处理怪物图像 (Key: {image_key}) 出错: {e}")
                    self.monster_image_cache[image_key] = None # 标记为加载失败
            # --- 图片加载逻辑结束 ---

            # 使用加载或缓存的图片
            if loaded_image:
                # 如果图片太大，缩放到合适尺寸
                img_panel = self.monster_components["img_panel"]
                panel_rect = img_panel.rect

                # 计算适合面板的尺寸（保持原始宽高比）
                max_width = panel_rect.width - 10
                max_height = panel_rect.height - 10

                img_width, img_height = loaded_image.get_size()
                # 防止除零错误
                if img_width > 0 and img_height > 0:
                    scale_factor = min(max_width / img_width, max_height / img_height)
                    new_width = int(img_width * scale_factor)
                    new_height = int(img_height * scale_factor)

                    # 缩放图片
                    scaled_img = pygame.transform.scale(loaded_image, (new_width, new_height))

                    # 保存怪物图片以便在draw方法中绘制
                    self.monster_image = scaled_img
                else:
                    logger.warning(f"怪物图片尺寸无效: {monster.name}, 尺寸: ({img_width}, {img_height})")
                    self.monster_image = None # 尺寸无效则不显示
            else:
                self.monster_image = None # 没有加载到图片
        else:
            # 清空怪物显示
            self.monster_components["name"].set_text("未遭遇怪物")


    def update_battle_log(self, battle_logs):
        """
        更新战斗日志

        参数:
            battle_logs: 战斗日志列表
        """
        # 调试信息
        logger.debug(f"更新战斗日志: 收到{len(battle_logs)}条日志, UI组件数:{len(self.battle_logs)}")

        if not battle_logs or len(battle_logs) == 0:
            # 清空战斗日志
            logger.debug("战斗日志为空，清空显示")
            for log_component in self.battle_logs:
                log_component.set_text("")
            return

        # 保存完整的战斗日志列表
        self.full_battle_logs = battle_logs.copy()

        # 根据滚动偏移量计算要显示的日志
        self._update_visible_logs()

    def _update_visible_logs(self):
        """根据滚动偏移量更新可见的战斗日志"""
        if not self.full_battle_logs:
            return

        # 计算要显示的日志范围
        total_logs = len(self.full_battle_logs)
        start_idx = max(0, total_logs - self.log_display_count - self.log_scroll_offset)
        end_idx = min(total_logs, start_idx + self.log_display_count)

        # 清空所有日志组件
        for log_component in self.battle_logs:
            log_component.set_text("")

        # 填充可见的日志
        for i in range(start_idx, end_idx):
            log_idx = i - start_idx
            if log_idx < len(self.battle_logs):
                self.battle_logs[log_idx].set_text(self.full_battle_logs[i])

    def handle_event(self, event):
        """处理事件"""
        # 先调用父类的事件处理方法
        result = super().handle_event(event)
        if result:
            return True

        # 处理鼠标滚轮事件 - 滚动战斗日志
        if event.type == pygame.MOUSEWHEEL:
            # 获取日志面板区域
            log_panel = self.components_map.get("log_panel")
            if log_panel and log_panel.visible:
                mouse_pos = pygame.mouse.get_pos()
                if log_panel.rect.collidepoint(mouse_pos):
                    # 向上滚动（显示更早的日志）
                    if event.y > 0:
                        max_offset = max(0, len(self.full_battle_logs) - self.log_display_count)
                        self.log_scroll_offset = min(max_offset, self.log_scroll_offset + 1)
                    # 向下滚动（显示更新的日志）
                    elif event.y < 0:
                        self.log_scroll_offset = max(0, self.log_scroll_offset - 1)

                    # 更新显示的日志
                    self._update_visible_logs()
                    return True

        return False

    def set_area_title(self, title):
        """设置区域标题"""
        if "area_title" in self.components_map:
            self.components_map["area_title"].set_text(title)
            logger.info(f"更新区域标题: {title}")

    def _on_auto_battle_click(self):
        """处理自动战斗按钮点击"""
        # 记录当前时间，用于计算下一次自动战斗的时间
        current_time = time.time()

        # 切换自动战斗状态
        self.is_auto_battle = not self.is_auto_battle

        # 同步游戏管理器的自动战斗状态，使用set_auto_battle而不是直接设置属性
        self.game_manager.set_auto_battle(self.is_auto_battle)
        logger.info(f"自动战斗按钮点击，设置状态：{self.is_auto_battle}")

        # 添加游戏模式日志
        logger.info(f"当前游戏模式: {self.game_manager.game_mode}, 战斗状态: {self.game_manager.in_battle}")

        if self.is_auto_battle:
            # 更新UI状态
            self.components_map["auto_battle_button"].set_text("停止自动战斗")
            self.monster_components["hint"].set_text("自动战斗中...")

            # 记录当前时间，用于计算下一次自动战斗的时间
            self.last_auto_battle_time = current_time

            # 如果当前没有怪物，立即开始一场战斗
            if not self.game_manager.in_battle and not self.game_manager.current_enemy:
                logger.info("自动战斗模式开启，尝试立即开始新战斗")
                battle_started = self._start_new_battle()
                logger.info(f"战斗开始结果: {battle_started}, 游戏模式: {self.game_manager.game_mode}")
            else:
                logger.info(f"当前已在战斗中或有怪物，不开始新战斗。in_battle={self.game_manager.in_battle}, current_enemy={self.game_manager.current_enemy}")
            # 不需要 else 分支来重置UI，也不需要在战斗中时立刻关闭自动战斗
            # else:
            # # 更新UI状态
            #    self.components_map["auto_battle_button"].set_text("自动战斗")
            #    self.monster_components["hint"].set_text("点击自动战斗按钮开始战斗")

            # # 如果正在战斗中，停止自动战斗但不结束当前战斗
            # if self.game_manager.in_battle:
            #     logger.info("关闭自动战斗模式，同时关闭战斗系统的自动战斗状态")
            #     # 确保使用set_auto_battle方法
            #     self.game_manager.set_auto_battle(False)
        else: # is_auto_battle is False (关闭自动战斗)
            # 更新UI状态
            self.components_map["auto_battle_button"].set_text("自动战斗")
            self.monster_components["hint"].set_text("点击自动战斗按钮开始战斗")
            # 如果正在战斗中，仅关闭自动战斗，但不结束当前战斗
            if self.game_manager.in_battle:
                 logger.info("关闭自动战斗模式")
            # 注意：set_auto_battle(False) 已经在 _on_auto_battle_click 的开头被调用了

    def _start_new_battle(self):
        """开始一场新战斗"""
        logger.info("尝试开始新战斗")
        logger.info(f"开始前游戏模式: {self.game_manager.game_mode}, 战斗状态: {self.game_manager.in_battle}")

        if self.game_manager.player and self.game_manager.player.is_dead:
            logger.info("玩家已死亡，无法开始新战斗")
            return False

        # 确保当前没有怪物，强制生成新怪物
        if self.game_manager.in_battle:
            logger.info("已在战斗中，结束当前战斗以开始新战斗")
            if hasattr(self.game_manager, "battle_system") and self.game_manager.battle_system:
                self.game_manager.battle_system.end_battle(False)
            self.game_manager.in_battle = False

        # 重要：确保current_enemy被清空，否则可能导致生成怪物失败
        self.game_manager.current_enemy = None
        logger.info("已清空当前怪物，准备生成新怪物")

        # 尝试生成怪物并开始战斗
        battle_result = self.game_manager.start_battle()
        logger.info(f"game_manager.start_battle() 返回结果: {battle_result}")
        logger.info(f"调用后游戏模式: {self.game_manager.game_mode}, 战斗状态: {self.game_manager.in_battle}")

        if battle_result:
            logger.info("成功开始新战斗")
            self.is_battle_active = True

            # 立即更新怪物显示，但显示"准备战斗"倒计时
            if self.game_manager.current_enemy:
                logger.info(f"更新怪物显示: {self.game_manager.current_enemy.name}")
                self.update_monster_display(self.game_manager.current_enemy)

                # 在怪物面板显示准备战斗倒计时
                self.monster_components["hint"].set_text("准备战斗...")

                # 添加战斗日志中的准备提示
                battle_logs = self.game_manager.battle_system.battle_logs if self.game_manager.battle_system else []
                battle_logs = list(battle_logs)  # 创建副本
                battle_logs.append(f"遭遇 {self.game_manager.current_enemy.name}！准备战斗...")
                self.update_battle_log(battle_logs)

            elif self.game_manager.battle_system and self.game_manager.battle_system.monster:
                logger.info(f"从战斗系统更新怪物显示: {self.game_manager.battle_system.monster.name}")
                self.update_monster_display(self.game_manager.battle_system.monster)

            # 主动更新战斗日志
            if self.game_manager.battle_system:
                self.update_battle_log(self.game_manager.battle_system.battle_logs)

            # 如果是自动战斗模式，开启自动战斗
            if self.is_auto_battle:
                logger.info("新战斗已开始，自动战斗模式已开启，设置游戏管理器和战斗系统的自动战斗状态")
                # 确保游戏管理器的自动战斗状态与UI保持一致
                self.game_manager.set_auto_battle(self.is_auto_battle)

            logger.info(f"成功启动新战斗，自动战斗模式：{self.is_auto_battle}, 游戏管理器自动战斗：{self.game_manager.auto_battle}")
            return True

        logger.info("开始新战斗失败")
        return False



    def _on_inventory_click(self):
        """处理背包按钮点击"""
        # --- 添加日志检查 ---
        logger.info("尝试打开背包界面")
        if hasattr(self.ui_manager, 'screens'):
            logger.debug(f"UIManager screens: {list(self.ui_manager.screens.keys())}")
        else:
            logger.error("UIManager instance has no 'screens' attribute!")
            return
        # --- 结束日志检查 ---
        self.ui_manager.show_screen("inventory")

    def _on_equipment_click(self):
        """处理装备按钮点击"""
        self.ui_manager.show_screen("equipment")
        logger.info("尝试打开装备界面")

    def _on_skills_click(self):
        """处理技能按钮点击"""
        logger.info("点击了技能按钮")
        self.ui_manager.show_screen("skills")

    def _on_map_click(self):
        """处理地图按钮点击"""
        logger.info("点击了地图按钮，准备显示地图界面")

        try:
            # 检查地图界面是否存在
            if "map" not in self.ui_manager.screens:
                logger.error("地图界面不存在！")
                self.ui_manager.show_message("错误", "地图界面未注册")
                return

            # 保存当前状态
            if hasattr(self.game_manager, 'save_game'):
                try:
                    self.game_manager.save_game()
                    logger.info("切换到地图界面前已保存游戏状态")
                except Exception as e:
                    logger.warning(f"切换到地图界面前保存游戏状态失败: {e}")

            # 显示地图界面
            logger.info("调用UI管理器显示地图界面")
            self.ui_manager.show_screen("map")
            logger.info("地图界面显示调用完成")
        except Exception as e:
            logger.error(f"显示地图界面时发生错误: {e}", exc_info=True)
            self.ui_manager.show_message("错误", f"无法显示地图界面: {str(e)}")

    def _on_achievements_click(self):
        """处理成就按钮点击"""
        logger.info("点击了成就按钮")
        self.ui_manager.show_screen("achievements")

    def _on_shop_click(self):
        """处理商店按钮点击"""
        logger.info("点击了商店按钮")
        self.ui_manager.show_screen("shop")

    def _on_menu_click(self):
        """处理菜单按钮点击"""
        logger.info("点击了菜单按钮")
        # 创建并显示游戏菜单对话框
        menu_dialog = self.ui_manager.create_dialog("game_menu", "游戏菜单", (250, 350))

        # 添加测试技能按钮（仅用于开发测试）
        test_skills_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - 200) // 2,
            menu_dialog.content_rect.y + 20 + (40 + 15) * 4,
            200,
            40
        )
        test_skills_button = self.ui_manager.create_button(
            test_skills_rect,
            "测试添加技能",
            lambda: self._test_add_skills(),
            "chinese_normal"
        )
        menu_dialog.add_content(test_skills_button)

        # 添加菜单按钮
        button_width = 200
        button_height = 40
        button_spacing = 15

        # 保存游戏按钮
        save_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - button_width) // 2,
            menu_dialog.content_rect.y + 20,
            button_width,
            button_height
        )
        save_button = self.ui_manager.create_button(
            save_rect,
            "保存游戏",
            lambda: self._on_save_click(menu_dialog.name),
            "chinese_normal"
        )
        menu_dialog.add_content(save_button)

        # 加载游戏按钮
        load_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - button_width) // 2,
            menu_dialog.content_rect.y + 20 + button_height + button_spacing,
            button_width,
            button_height
        )
        load_button = self.ui_manager.create_button(
            load_rect,
            "加载游戏",
            lambda: self._on_load_click(menu_dialog.name),
            "chinese_normal"
        )
        menu_dialog.add_content(load_button)

        # 返回主菜单按钮
        main_menu_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - button_width) // 2,
            menu_dialog.content_rect.y + 20 + (button_height + button_spacing) * 2,
            button_width,
            button_height
        )
        main_menu_button = self.ui_manager.create_button(
            main_menu_rect,
            "返回主菜单",
            lambda: self._on_return_to_main_click(menu_dialog.name),
            "chinese_normal"
        )
        menu_dialog.add_content(main_menu_button)

        # 公告按钮
        announcement_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - button_width) // 2,
            menu_dialog.content_rect.y + 20 + (button_height + button_spacing) * 3,
            button_width,
            button_height
        )
        announcement_button = self.ui_manager.create_button(
            announcement_rect,
            "游戏公告",
            lambda: self._on_announcement_click(menu_dialog.name),
            "chinese_normal"
        )
        menu_dialog.add_content(announcement_button)

        # 显示菜单对话框
        self.ui_manager.show_dialog(menu_dialog.name)

    def _on_save_click(self, dialog_name):
        """处理保存游戏按钮点击"""
        self.ui_manager.hide_dialog(dialog_name)

        try:
            logger.info("开始尝试保存游戏")

            # 检查游戏管理器对象
            if not self.game_manager:
                logger.error("游戏管理器对象为空")
                self.ui_manager.show_message("保存失败", "无法访问游戏管理器")
                return

            # 检查player对象是否存在
            if not self.game_manager.player:
                logger.error("玩家对象为空")
                self.ui_manager.show_message("保存失败", "无法获取玩家数据")
                return

            # 尝试手动进行一次player.save_data()
            try:
                player_data = self.game_manager.player.save_data()
                if not player_data:
                    logger.error("无法获取玩家数据")
                    self.ui_manager.show_message("保存失败", "无法获取玩家数据，请检查角色状态")
                    return

                logger.info(f"成功获取玩家数据，包含字段: {', '.join(player_data.keys() if isinstance(player_data, dict) else ['非字典类型'])}")
            except Exception as e:
                logger.error(f"调用player.save_data()时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                self.ui_manager.show_message("保存失败", f"获取玩家数据时出错: {e}")
                return

            # 调用游戏管理器保存功能
            success = self.game_manager.save_game()

            if success:
                logger.info("游戏保存成功")
                self.ui_manager.show_message("保存成功", "游戏已成功保存!")
            else:
                logger.error("游戏保存方法返回失败")
                # 尝试直接保存数据
                self._try_direct_save(player_data)
        except Exception as e:
            logger.error(f"保存游戏过程中出现异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.ui_manager.show_message("保存失败", f"保存游戏时出错: {str(e)}")

    def _try_direct_save(self, player_data):
        """尝试直接保存数据作为备份方案"""
        try:
            logger.info("尝试手动保存玩家数据作为备份")

            # 确保存档目录存在
            save_dir = "data/saves"
            os.makedirs(save_dir, exist_ok=True)

            # 准备基本数据
            save_data = {
                "version": "1.1",
                "player": player_data,
                "current_map": self.game_manager.current_map,
                "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": time.time(),
                "game_state": {
                    "in_battle": getattr(self.game_manager, "in_battle", False),
                    "battle_enabled": getattr(self.game_manager, "battle_enabled", True),
                    "auto_battle": getattr(self.game_manager, "auto_battle", False)
                }
            }

            # 使用玩家名称作为文件名前缀
            player_name = self.game_manager.player.name.strip() if self.game_manager.player.name else "unnamed"
            player_name = ''.join(c if c.isalnum() or c in ' -_' else '_' for c in player_name)
            file_path = os.path.join(save_dir, f"{player_name}_backup_save.json")

            # 写入文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            logger.info(f"成功创建备份存档: {file_path}")
            self.ui_manager.show_message("备份保存", f"正常保存失败，但已创建备份存档:\n{file_path}")

        except Exception as e:
            logger.error(f"创建备份存档失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.ui_manager.show_message("保存失败", f"无法创建备份存档: {str(e)}")

    def _on_load_click(self, dialog_name):
        """处理加载游戏按钮点击"""
        self.ui_manager.hide_dialog(dialog_name)
        self.ui_manager.show_screen("load_game")

    def _on_return_to_main_click(self, dialog_name):
        """处理返回主菜单按钮点击"""
        self.ui_manager.hide_dialog(dialog_name)

        # 显示确认对话框
        self.ui_manager.show_confirmation(
            "返回主菜单",
            "确定要返回主菜单吗？未保存的进度将丢失。",
            self._confirm_return_to_main
        )

    def _on_announcement_click(self, dialog_name):
        """处理公告按钮点击"""
        self.ui_manager.hide_dialog(dialog_name)

        # 显示公告界面，调整大小以适应更多内容
        announcement_dialog = self.ui_manager.create_dialog("announcement", "游戏公告", (1280, 720))

        # 添加公告内容
        content_panel = self.ui_manager.create_panel(
            pygame.Rect(
                announcement_dialog.content_rect.x,
                announcement_dialog.content_rect.y,
                announcement_dialog.content_rect.width,
                announcement_dialog.content_rect.height
            ),
            color=(255, 255, 255, 200),
            border_color=(200, 200, 200),
            border_width=1
        )
        announcement_dialog.add_content(content_panel)

        # 添加标题
        title_rect = pygame.Rect(
            announcement_dialog.content_rect.x + 10,
            announcement_dialog.content_rect.y + 10,
            announcement_dialog.content_rect.width - 20,
            30
        )
        title_text = self.ui_manager.create_text(
            title_rect,
            "游戏最新更新内容和注意事项",
            "chinese_normal",
            color=(0, 0, 0),
            align="center"
        )
        announcement_dialog.add_content(title_text)

        # 游戏公告内容
        announcement_content = [
            "版本1.3.6 更新内容 (2025-05-01):",
            "",
            "1. 平衡性调整：",
            "   - 调整职业成长属性",
            "   - 调整部分怪物属性",
            "",
            "2. 已修复问题：",
            "   - 修复法师大量技能无法使用的问题",
            "   - 修复背包物品无法正确显示属性的问题",
            "   - 修复安全存档的问题",
            "   - 修复其他已知问题",
            "",
            "注意事项：",
            "1. 建议定期保存游戏，防止数据丢失",
            "2. 战斗系统已优化，自动战斗效率提升",
            "3. 新增VIP特权，充值元宝可享受更多福利",
            "4. 如遇游戏问题，请及时反馈，我们将尽快修复，qq544833168"
        ]

        # 创建可滚动列表，替代原来的单独文本元素
        scroll_rect = pygame.Rect(
            announcement_dialog.content_rect.x + 10,
            announcement_dialog.content_rect.y + 50,
            announcement_dialog.content_rect.width - 20,
            announcement_dialog.content_rect.height - 50  # 留出底部空间给关闭按钮
        )

        # 创建一个面板来容纳内容
        scroll_panel = self.ui_manager.create_panel(
            scroll_rect,
            color=(250, 250, 250, 200),
            border_color=(200, 200, 200),
            border_width=1
        )
        announcement_dialog.add_content(scroll_panel)

        # 手动创建文本内容并添加到面板
        y_offset = 100  # 调整文本顶部距离从10增加到30
        line_height = 22  # 增加行高以避免文本重叠
        total_content_height = 100

        # 文本组件列表，用于滚动操作
        text_components = []

        for line in announcement_content:
            text_rect = pygame.Rect(
                50,  # 相对于面板的x位置
                y_offset,
                scroll_rect.width - 30,  # 减少宽度以避免水平溢出
                line_height
            )
            line_text = self.ui_manager.create_text(
                text_rect,
                line,
                "chinese_normal",
                color=(0, 0, 0),
                align="left"
            )
            scroll_panel.add_component(line_text)
            text_components.append(line_text)
            y_offset += line_height + 2  # 添加额外间距
            total_content_height += line_height + 2

        # 添加滚动功能
        scroll_offset = 0
        max_scroll = max(0, total_content_height - scroll_rect.height)

        # 定义滚动事件处理函数
        def handle_scroll(event):
            nonlocal scroll_offset
            if event.type == pygame.MOUSEWHEEL and scroll_rect.collidepoint(pygame.mouse.get_pos()):
                # 向上滚动为正值，向下滚动为负值
                new_offset = scroll_offset - event.y * 20  # 20是滚动速度
                scroll_offset = max(0, min(max_scroll, new_offset))

                # 更新所有文本组件的位置
                for i, component in enumerate(text_components):
                    # 使用计算行高的偏移
                    component.rect.y = 10 + i * (line_height + 2) - scroll_offset
                return True
            return False

        # 替换面板的事件处理函数
        original_handle_event = scroll_panel.handle_event

        def new_handle_event(event):
            if handle_scroll(event):
                return True
            return original_handle_event(event)

        scroll_panel.handle_event = new_handle_event

        # 添加关闭按钮 - 调整位置，确保不与滚动面板重叠
        close_rect = pygame.Rect(
            announcement_dialog.content_rect.x + (announcement_dialog.content_rect.width - 120) // 2,
            announcement_dialog.content_rect.y + announcement_dialog.content_rect.height - 45,
            120,
            35
        )
        close_button = self.ui_manager.create_button(
            close_rect,
            "关闭",
            lambda: self.ui_manager.hide_dialog("announcement"),
            "chinese_normal"
        )
        announcement_dialog.add_content(close_button)

        # 显示公告对话框
        self.ui_manager.show_dialog("announcement")

    def _confirm_return_to_main(self):
        """确认返回主菜单"""
        # 结束当前游戏
        self.game_manager.exit_game()

        # 返回主菜单
        self.ui_manager.show_screen("main_menu")

    def _test_add_skills(self):
        """测试函数：为玩家添加所有职业技能"""
        player = self.game_manager.player
        if not player:
            logger.warning("无法添加技能：找不到玩家数据")
            return

        # 获取玩家职业
        character_class = player.character_class

        # 获取该职业的所有技能
        from core.config import GameConfig
        skills_data = GameConfig.get_skills_for_class(character_class)

        if not skills_data:
            logger.warning(f"无法获取职业 {character_class} 的技能数据")
            return

        # 为玩家添加所有技能
        for skill_id, skill_data in skills_data.items():
            # 如果玩家还没有学习这个技能，添加到技能列表
            if skill_id not in player.skills or player.skills[skill_id] <= 0:
                player.skills[skill_id] = 1
                logger.info(f"为玩家添加技能: {skill_id}, 名称: {skill_data.get('name')}")

                # 如果是主动技能，尝试添加到技能槽
                if skill_data.get("type") == "active":
                    # 查找空闲的技能槽
                    for slot_idx in range(10):  # 支持最多10个技能槽
                        if slot_idx not in self.skill_slots:
                            # 将技能添加到空闲槽位
                            self.skill_slots[slot_idx] = skill_id
                            logger.info(f"将技能 {skill_data.get('name')} 添加到技能槽 {slot_idx+1}")

                            # 同步到玩家对象
                            if hasattr(player, 'skill_slots'):
                                player.skill_slots[slot_idx] = skill_id
                            else:
                                player.skill_slots = {slot_idx: skill_id}

                            # 添加到自动释放字典
                            if hasattr(self, 'auto_cast_skills'):
                                cooldown = skill_data.get("cooldown", 0)
                                if isinstance(cooldown, list):
                                    cooldown = cooldown[0]  # 使用第一级冷却时间
                                self.auto_cast_skills[slot_idx] = {"last_cast_time": 0, "cooldown": cooldown}

                            break

        # 更新技能按钮
        self._update_skill_buttons()
        logger.info("已为玩家添加所有职业技能并更新技能槽")

        # 隐藏菜单对话框
        self.ui_manager.hide_dialog("game_menu")

        # 显示成功消息
        self.ui_manager.show_message("添加技能", "已成功添加所有职业技能")

    def _on_death_timer_end(self):
        """处理死亡计时器结束"""
        # 仅将show_death_panel设为false，但不清空死亡消息
        # 死亡面板的显示和隐藏现在由update方法根据player.is_dead状态控制
        self.show_death_panel = False
        logger.debug("死亡计时器结束，但死亡面板显示仍由update方法控制")

    def init_hunting_monsters(self):
        """初始化寻怪怪物"""
        self.monsters = []
        for _ in range(15):
            self.monsters.append({
                "x": random.randint(30, self.hunting_area.width-30),
                "y": random.randint(30, self.hunting_area.height-30),
                "size": 9,
                "color": (255, 0, 0),
                "base_speed": 2,
                "chase_speed": 4,
                "speed": 2,
                "direction": [random.uniform(-1, 1), random.uniform(-1, 1)],
                "active": True
            })

    def update_hunting(self, dt):
        """更新寻怪相关的UI特有状态（如果需要）。
        核心移动和碰撞逻辑已由 Game 类 (self.game_manager) 处理。
        此方法目前可以为空，或仅处理非常轻量级的UI特定更新。
        """
        if not self.hunting_visible or not self.game_manager:
            return

        # player = self.game_manager.player
        # if player:
        #     # GameScreen 内部的 self.player_pos 似乎不再是必需的，
        #     # 因为 draw_hunting 直接使用 game_manager.player.x/y
        #     # 如果其他UI组件依赖 self.player_pos，则需要保留此同步。
        #     # self.player_pos = [player.x, player.y]
        #     pass

        # GameScreen 内部的 self.monsters 列表也不再是必需的，
        # draw_hunting 直接使用 game_manager.map_active_monsters。
        # self.monsters.clear()
        # for m_core in self.game_manager.map_active_monsters:
        #     # 如果需要将核心Monster对象转换为UI特定的字典结构，可以在这里做
        #     # ui_monster = {"x": m_core.x, "y": m_core.y, ...}
        #     # self.monsters.append(ui_monster)
        #     pass

        # 战斗触发 (trigger_battle) 逻辑现在也由核心 Game 类处理。
        pass

    def trigger_battle(self, monster):
        """触发战斗"""
        monster["active"] = False  # 暂时禁用怪物
        # 调用现有战斗系统
        if hasattr(self.game_manager, "start_battle"):
            self.game_manager.start_battle()
        # 可以在这里添加更多战斗初始化逻辑

    def draw_hunting(self, surface):
        """绘制寻怪界面，使用核心游戏逻辑的数据。"""
        if not self.hunting_visible or not self.game_manager:
            return

        # 绘制背景 (self.hunting_area 定义了绘制区域和大小)
        pygame.draw.rect(surface, (20, 50, 30), self.hunting_area) # 深绿色背景
        pygame.draw.rect(surface, (60, 120, 80), self.hunting_area, 2) # 绿色边框，线宽为2

        original_clip = surface.get_clip() # 保存原始剪裁区域
        surface.set_clip(self.hunting_area)  # 设置剪裁区域为寻怪背景区域

        core_player = self.game_manager.player
        # 绘制玩家
        if core_player:
            pygame.draw.circle(surface, core_player.color,
                              (int(self.hunting_area.left + core_player.x),
                               int(self.hunting_area.top + core_player.y)),
                              int(core_player.size / 2))

        # 绘制怪物
        # 感知范围半径，应与 Game.is_monster_aware_of_player 中的 sight_radius 一致
        monster_sight_radius = 40
        for monster_obj in self.game_manager.map_active_monsters:
            if not monster_obj: continue

            # 怪物本体
            pygame.draw.circle(surface, monster_obj.color,
                             (int(self.hunting_area.left + monster_obj.x),
                              int(self.hunting_area.top + monster_obj.y)),
                             int(monster_obj.size / 2))

            # 绘制怪物感知范围 (半透明红色圆圈)
            # 创建一个带alpha通道的颜色
            awareness_color = (255, 0, 0, 100) # RGBA，A=100表示半透明

            # 为了绘制带alpha的圆圈，我们可能需要一个临时的surface如果直接在主surface上绘制效果不佳
            # 或者，如果Pygame版本支持，可以直接用带alpha的颜色绘制
            # 简单起见，先尝试直接绘制。如果透明度不起作用，再考虑临时surface。
            # pygame.draw.circle(surface, awareness_color,
            #                  (int(self.hunting_area.left + monster_obj.x),
            #                   int(self.hunting_area.top + monster_obj.y)),
            #                  monster_sight_radius, 1) # 1是线宽

            # 使用Surface对象实现半透明效果是更可靠的方式
            temp_surface = pygame.Surface((monster_sight_radius * 2, monster_sight_radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(temp_surface, awareness_color,
                               (monster_sight_radius, monster_sight_radius),
                               monster_sight_radius, 1) # 线宽为1
            surface.blit(temp_surface,
                         (int(self.hunting_area.left + monster_obj.x - monster_sight_radius),
                          int(self.hunting_area.top + monster_obj.y - monster_sight_radius)))

        surface.set_clip(original_clip) # 恢复原始剪裁区域

    def update(self, dt: float):
        """更新界面状态"""
        super().update(dt)

        # 调用游戏管理器的update方法，确保游戏逻辑得到更新
        if self.game_manager:
            self.game_manager.update(dt)

        # 只有当 hunting_visible 为 True 时才调用 update_hunting
        if self.hunting_visible: # 这将一直为 True
            self.update_hunting(dt) # update_hunting 内部的逻辑也需要调整

        current_time = time.time()

        # 如果在战斗中，自动释放技能
        if self.is_battle_active and self.game_manager.in_battle:
            self._auto_cast_skills(dt)

        # 更新全局技能冷却条
        self._update_global_cooldown_bar()

        # 控制玩家属性更新频率 - 增加更新间隔到2秒，降低刷新频率
        should_update_stats = False
        if current_time - self.last_stats_update_time >= self.stats_update_interval:
            should_update_stats = True
            self.last_stats_update_time = current_time

        # 更新玩家属性，但控制更新频率
        if self.game_manager.player and should_update_stats:
            # 检查玩家数据是否真的发生变化，只有在数据变化时才刷新UI
            need_update = self._check_player_stats_changed()

            if need_update:
                self.update_player_stats(self.game_manager.player)

            # 检查是否需要更新地图UI
            if not self.is_map_ui_updated:
                if "area_title" in self.components_map:
                    self.set_area_title(self.game_manager.current_map)
                self.is_map_ui_updated = True

            # 检查玩家死亡状态
            if self.game_manager.player.is_dead:
                # 计算剩余复活时间
                death_time = float(self.game_manager.player.last_death_time)
                remaining_time = max(0, 10.0 - (current_time - death_time))
                remaining_seconds = int(remaining_time)

                # --- 修改：优化死亡倒计时显示 ---
                # 只在倒计时秒数变化时更新显示，避免刷屏
                if not hasattr(self, '_last_death_countdown') or self._last_death_countdown != remaining_seconds:
                    self._last_death_countdown = remaining_seconds
                    if remaining_seconds > 0:
                        death_message_log = f"你已死亡！将在 {remaining_seconds} 秒后复活..."
                        self.game_manager.add_log(death_message_log, is_battle=True)
                    else:
                        self.game_manager.add_log("正在复活中...", is_battle=True)

                # 在怪物提示区域显示死亡倒计时
                if remaining_seconds > 0:
                    self.monster_components["hint"].set_text(f"死亡状态 - {remaining_seconds}秒后复活")
                else:
                    self.monster_components["hint"].set_text("正在复活中...")
                # --- 结束修改 ---

                # --- 移除死亡面板显示逻辑 ---
                # # 显示死亡面板
                # death_panel = self.components_map.get("death_panel")
                # if death_panel:
                #     death_panel.visible = True
                #     # self.death_message.visible = True # death_message 已被移除
                # --- 结束移除 ---
            else:
                # 玩家复活时清除死亡倒计时显示
                if hasattr(self, '_last_death_countdown'):
                    # 清除倒计时缓存，确保下次死亡时能正确显示
                    delattr(self, '_last_death_countdown')
                    # 清除怪物提示区域的死亡信息
                    if self.is_auto_battle and not self.is_battle_active:
                        self.monster_components["hint"].set_text("正在寻找怪物...")
                    else:
                        self.monster_components["hint"].set_text("无怪物")

                # 更新副本倒计时显示
        self._update_dungeon_timer()

        # 确保游戏管理器和UI的自动战斗状态同步
        if self.is_auto_battle != self.game_manager.auto_battle:
            logger.info(f"同步自动战斗状态: UI={self.is_auto_battle} -> 游戏={self.game_manager.auto_battle}")
            self.is_auto_battle = self.game_manager.auto_battle
            # 更新UI状态
            if self.is_auto_battle:
                self.components_map["auto_battle_button"].set_text("停止自动战斗")
            else:
                self.components_map["auto_battle_button"].set_text("自动战斗")

        # 更新战斗日志和怪物显示，但仅在战斗状态变化时更新
        if self.game_manager.in_battle and self.game_manager.battle_system:
            # 更新战斗日志
            if hasattr(self.game_manager.battle_system, 'battle_logs') and self.game_manager.battle_system.battle_logs:
                self.update_battle_log(self.game_manager.battle_system.battle_logs)
            elif hasattr(self.game_manager, 'battle_logs') and self.game_manager.battle_logs:
                # 如果battle_system没有日志，则使用game_manager中的日志
                self.update_battle_log(self.game_manager.battle_logs)

            # 只在战斗状态变化或怪物变化时更新怪物显示
            if not self.is_battle_active or should_update_stats:
                if hasattr(self.game_manager.battle_system, 'monster') and self.game_manager.battle_system.monster:
                    self.update_monster_display(self.game_manager.battle_system.monster)
                    self.is_battle_active = True
                elif hasattr(self.game_manager, 'current_enemy') and self.game_manager.current_enemy:
                    self.update_monster_display(self.game_manager.current_enemy)
                    self.is_battle_active = True
        elif not self.game_manager.in_battle and self.is_battle_active:
            # 战斗已结束但界面还显示战斗状态
            self.is_battle_active = False
            self.update_monster_display(None)
            # 重新生成随机战斗间隔并重置计时器
            base_interval = random.uniform(3, 5)  # 修改为更长的自动战斗间隔，随机3-5秒
            self.auto_battle_interval = base_interval * GameConfig.BATTLE_SPEED_MODIFIER  # 应用速度修正，乘以而不是除以，使间隔延长
            self.last_auto_battle_time = current_time  # 重置计时器，确保完整等待间隔时间
            logger.info(f"战斗结束，设置新的寻找怪物时间: {self.auto_battle_interval:.2f}秒")

        # 处理自动战斗
        if self.is_auto_battle and not self.game_manager.player.is_dead and not self.is_battle_active:
            # 如果在自动战斗中但不在战斗状态，显示寻找怪物的倒计时
            if not self.game_manager.in_battle:
                time_left = max(0, self.auto_battle_interval - (current_time - self.last_auto_battle_time))

                # 在怪物提示区域显示简单提示
                self.monster_components["hint"].set_text(f"正在寻找怪物...")

                # 在战斗信息栏中显示寻怪倒计时
                # 创建一个临时的战斗日志列表，用于显示寻怪倒计时
                temp_battle_logs = []

                # 如果之前有战斗日志，保留它们
                if hasattr(self.game_manager, 'battle_system') and self.game_manager.battle_system:
                    battle_logs = self.game_manager.battle_system.battle_logs
                    if battle_logs:
                        # 拷贝现有的战斗日志
                        temp_battle_logs = battle_logs.copy()

                # 添加寻怪倒计时信息到临时战斗日志列表
                temp_battle_logs.append(f"正在寻找怪物...({time_left:.1f}秒)")

                # 更新战斗信息栏
                self.update_battle_log(temp_battle_logs)

                # 检查是否到达自动战斗的时间
                if current_time - self.last_auto_battle_time >= self.auto_battle_interval:
                    self.last_auto_battle_time = current_time  # 重置寻找怪物的时间
                    logger.info(f"寻找怪物间隔结束，开始新战斗，自动战斗状态：UI={self.is_auto_battle}，游戏={self.game_manager.auto_battle}")
                    # 确保game_manager的自动战斗状态与UI保持一致
                    self.game_manager.set_auto_battle(self.is_auto_battle)
                    # 确保当前没有怪物，允许生成新怪物
                    self.game_manager.current_enemy = None
                    self._start_new_battle()

        # 更新技能按钮
        if hasattr(self.game_manager, 'player') and self.game_manager.player is not None:
            self._update_skill_buttons()

            # 更新技能冷却
            self.game_manager.player.update_cooldowns()

            # 自动释放技能已移至_auto_cast_skills方法中处理

        # 更新自动吃药按钮状态
        if hasattr(self.game_manager, 'auto_potion') and hasattr(self, 'auto_potion_btn'):
            button_text = "自动吃药√" if self.game_manager.auto_potion.enabled else "自动吃药"
            if self.auto_potion_btn.text != button_text:
                self.auto_potion_btn.set_text(button_text)

    def _check_player_stats_changed(self):
        """检查玩家属性是否发生变化"""
        if not hasattr(self, '_last_player_stats'):
            # 首次调用，初始化为空字典
            self._last_player_stats = {}
            return True

        player = self.game_manager.player
        if not player:
            return False

        # 获取当前关键属性
        current_stats = {
            'hp': player.hp,
            'max_hp': player.max_hp,
            'mp': player.mp,
            'max_mp': player.max_mp,
            'exp': player.exp,
            'level': player.level,
            'gold': player.gold,
            'defense': player.defense,
            'attack_speed': player.attack_speed,
        }

        # 检查是否有变化
        has_changed = False
        for key, value in current_stats.items():
            if key not in self._last_player_stats or self._last_player_stats[key] != value:
                has_changed = True
                break

        # 更新上次记录的属性
        self._last_player_stats = current_stats

        return has_changed

    def draw(self, surface: pygame.Surface):
        """绘制界面"""
        # 先调用父类的绘制方法
        super().draw(surface)

        # 如果寻怪模式激活且可见，则绘制寻怪界面
        if self.hunting_visible:
            self.draw_hunting(surface) # 直接在主 surface 上绘制

        # 绘制怪物图片
        if self.monster_image:
            img_panel = self.monster_components.get("img_panel")
            if img_panel and img_panel.visible:
                # 计算图片绘制位置（居中显示）
                img_x = img_panel.rect.x + (img_panel.rect.width - self.monster_image.get_width()) // 2
                img_y = img_panel.rect.y + (img_panel.rect.height - self.monster_image.get_height()) // 2
                surface.blit(self.monster_image, (img_x, img_y))

        # 绘制召唤物
        self._draw_summons(surface)

    def _draw_summons(self, surface: pygame.Surface):
        """绘制玩家的召唤物和被诱惑的怪物"""
        player = self.game_manager.player

        if not player:
            return

        # 获取召唤物列表，确保它是一个列表
        if not hasattr(player, 'summons'):
            logger.warning("玩家没有summons属性，初始化为空列表")
            player.summons = []
        summons = player.summons

        # 获取被诱惑怪物列表，确保它是一个列表
        if not hasattr(player, 'charmed_monsters'):
            logger.warning("玩家没有charmed_monsters属性，初始化为空列表")
            player.charmed_monsters = []
        charmed_monsters = player.charmed_monsters

        # 添加调试日志
        logger.info(f"_draw_summons: 玩家召唤物数量={len(summons)}, 魅惑怪物数量={len(charmed_monsters)}")
        if charmed_monsters:
            for i, monster in enumerate(charmed_monsters):
                logger.info(f"魅惑怪物 #{i+1}: {monster.name}, hp={monster.hp}, charmed={getattr(monster, 'charmed', False)}")

        # 如果没有召唤物和被魅惑的怪物，直接返回
        if not summons and not charmed_monsters:
            return

        # 仅在需要时记录关键日志
        if summons or charmed_monsters:
            logger.info(f"玩家 {player.name} 的召唤物数量: {len(summons)}, 被诱惑怪物数量: {len(charmed_monsters)}")

        # 获取玩家属性面板作为参考位置
        player_panel = self.components_map.get("player_panel")
        if not player_panel:
            logger.warning("无法找到玩家属性面板，无法绘制召唤物")
            return

        # 计算召唤物显示区域（在玩家属性面板下方，但上移200像素）
        summon_area_x = player_panel.rect.x
        summon_area_y = player_panel.rect.y + player_panel.rect.height + 5 - 150
        # 确保面板不会过高导致与玩家面板重叠
        min_y = player_panel.rect.y + 50  # 保留最小50像素的间距
        summon_area_y = max(summon_area_y, min_y)
        summon_area_width = player_panel.rect.width

        # 绘制召唤物标题
        font = pygame.font.SysFont("simhei", 16)
        title_text = font.render("召唤物与魅惑怪物", True, (220, 220, 100))
        title_x = summon_area_x + 10
        surface.blit(title_text, (title_x, summon_area_y))

        # 为每个召唤物创建显示区域
        summon_y = summon_area_y + 25

        # 处理普通召唤物
        for i, summon in enumerate(player.summons):
            # 创建召唤物背景
            summon_bg_rect = pygame.Rect(summon_area_x, summon_y, summon_area_width, 70)  # 增加高度以容纳图片和血条
            pygame.draw.rect(surface, (40, 40, 60), summon_bg_rect)
            pygame.draw.rect(surface, (80, 80, 120), summon_bg_rect, 1)

            # --- 修改图片加载逻辑 ---
            summon_image = None
            image_key = getattr(summon, 'image', summon.name) # 使用 image 属性或名字作为 key

            # 检查缓存
            if image_key in self.summon_image_cache:
                summon_image = self.summon_image_cache[image_key]
            else:
                # 不在缓存中，尝试加载
                try:
                    from utils.resource_manager import resources
                    import os
                    import sys

                    # 获取当前地图名称
                    current_map = self.game_manager.current_map if hasattr(self.game_manager, 'current_map') else None
                    image_name = getattr(summon, 'image', f"{summon.name}.jpg") # 获取图片文件名

                    image_paths = [
                        # 从当前地图文件夹加载
                        f"assets/images/maps/{current_map}/summon/{image_name}",
                        f"assets/images/{current_map}/{image_name}",
                        # 增加通用召唤物路径
                        f"assets/images/summon/{image_name}",
                    ]

                    loaded_image = None
                    for path in image_paths:
                        loaded_image = resources.load_image(path)
                        if loaded_image:
                            logger.info(f"成功加载并缓存召唤物图像：{path} (Key: {image_key})")
                            break

                    # ... (尝试打包环境路径的代码保持不变) ...
                    if not loaded_image and getattr(sys, 'frozen', False):
                         base_path = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
                         possible_paths = [
                             os.path.join(base_path, f"assets/images/summon/{image_name}"),
                             os.path.join(base_path, f"_internal/assets/images/summon/{image_name}"),
                         ]
                         for path in possible_paths:
                            if os.path.exists(path):
                                loaded_image = resources.load_image(path)
                                if loaded_image:
                                    logger.info(f"成功从打包环境加载并缓存召唤物图像：{path} (Key: {image_key})")
                                    break

                    # 存入缓存 (即使加载失败也存 None，避免重复尝试)
                    self.summon_image_cache[image_key] = loaded_image
                    summon_image = loaded_image

                except Exception as e:
                    logger.warning(f"加载召唤物图像 (Key: {image_key}) 出错: {e}")
                    self.summon_image_cache[image_key] = None # 标记为加载失败
            # --- 图片加载逻辑结束 ---

            # 计算左右两侧的区域宽度
            left_width = summon_area_width // 3
            right_width = summon_area_width - left_width

            # 绘制召唤物图片（如果有）
            if summon_image:
                # 调整图片大小
                image_size = min(50, summon_bg_rect.height - 20)
                summon_image = pygame.transform.scale(summon_image, (image_size, image_size))
                # 计算图片位置在左侧区域中居中
                image_x = summon_area_x + (left_width - image_size) // 2
                image_y = summon_y + (summon_bg_rect.height - image_size) // 2
                surface.blit(summon_image, (image_x, image_y))

            # 绘制召唤物名称和等级
            name_font = pygame.font.SysFont("simhei", 14)
            name_text = name_font.render(f"{summon.name} Lv.{summon.level}", True, (220, 220, 255))
            # 如果有图片，绘制在右侧区域；否则绘制在整个区域
            name_x = summon_area_x + left_width + 5 if summon_image else summon_area_x + 10
            surface.blit(name_text, (name_x, summon_y + 5))

            # 绘制攻击力
            attack_min = getattr(summon, 'attack_min', summon.level * 5)
            attack_max = getattr(summon, 'attack_max', summon.level * 8)
            attack_text = name_font.render(f"攻击: {attack_min}-{attack_max}", True, (220, 180, 180))
            attack_x = summon_area_x + left_width + 5 if summon_image else summon_area_x + 10
            surface.blit(attack_text, (attack_x, summon_y + 22))

            # 绘制防御力
            defense = getattr(summon, 'defense', summon.level * 2)
            defense_text = name_font.render(f"防御: {defense}", True, (180, 220, 180))
            defense_text_width = defense_text.get_width()
            defense_x = summon_area_x + summon_area_width - defense_text_width - 10
            surface.blit(defense_text, (defense_x, summon_y + 22))

            # 绘制血条背景
            hp_bar_rect = pygame.Rect(summon_area_x + 10, summon_y + summon_bg_rect.height - 12, summon_area_width - 20, 8)
            pygame.draw.rect(surface, (80, 80, 80), hp_bar_rect)

            # 绘制血条
            hp = getattr(summon, 'hp', 100)
            max_hp = getattr(summon, 'max_hp', 100)
            if max_hp > 0:  # 避免除以零错误
                hp_ratio = max(0, min(hp / max_hp, 1.0))  # 确保比例在0-1之间
                hp_width = int(hp_ratio * (summon_area_width - 20))
                if hp_width > 0:
                    hp_bar_fill_rect = pygame.Rect(summon_area_x + 10, summon_y + summon_bg_rect.height - 12, hp_width, 8)
                    pygame.draw.rect(surface, (220, 60, 60), hp_bar_fill_rect)

            # 绘制血条文字
            hp_text = name_font.render(f"{hp}/{max_hp}", True, (220, 220, 220))
            hp_text_x = summon_area_x + summon_area_width // 2 - hp_text.get_width() // 2
            surface.blit(hp_text, (hp_text_x, summon_y + summon_bg_rect.height - 14))

            # 绘制剩余时间（如果有）
            if hasattr(summon, "get_remaining_time") and hasattr(summon, "duration") and summon.duration > 0:
                remaining_time = summon.get_remaining_time()
                if remaining_time > 0:
                    time_text = name_font.render(f"剩余: {int(remaining_time)}秒", True, (180, 180, 220))
                    time_x = summon_area_x + summon_area_width - time_text.get_width() - 10
                    surface.blit(time_text, (time_x, summon_y + 39))

            # 更新下一个召唤物的Y坐标
            summon_y += summon_bg_rect.height + 5  # 增加间距

        # 处理被魅惑的怪物
        if hasattr(player, 'charmed_monsters') and player.charmed_monsters:
            for i, monster in enumerate(player.charmed_monsters):
                # 创建怪物背景，使用不同的颜色以区分普通召唤物
                monster_bg_rect = pygame.Rect(summon_area_x, summon_y, summon_area_width, 70)
                pygame.draw.rect(surface, (60, 40, 40), monster_bg_rect)
                pygame.draw.rect(surface, (120, 80, 80), monster_bg_rect, 1)

                # --- 修改图片加载逻辑 ---
                monster_image = None
                image_key = monster.name # 使用怪物名字作为 key

                # 检查缓存
                if image_key in self.monster_image_cache:
                    monster_image = self.monster_image_cache[image_key]
                else:
                    # 不在缓存中，尝试加载
                    try:
                        from utils.resource_manager import resources
                        image_name = f"{monster.name}.jpg"
                        current_map = self.game_manager.current_map if hasattr(self.game_manager, 'current_map') else None

                        image_paths = [
                            # 从当前地图文件夹加载
                            f"assets/images/{current_map}/{image_name}",
                            # 增加通用怪物路径
                            f"assets/images/monsters/{image_name}"
                        ]

                        loaded_image = None
                        for path in image_paths:
                            loaded_image = resources.load_image(path)
                            if loaded_image:
                                logger.info(f"成功加载并缓存魅惑怪物图像：{path} (Key: {image_key})")
                                break

                        # 存入缓存 (即使加载失败也存 None)
                        self.monster_image_cache[image_key] = loaded_image
                        monster_image = loaded_image

                    except Exception as e:
                        logger.warning(f"加载魅惑怪物图像 (Key: {image_key}) 出错: {e}")
                        self.monster_image_cache[image_key] = None
                # --- 图片加载逻辑结束 ---

                # 计算左右两侧的区域宽度
                left_width = summon_area_width // 3
                right_width = summon_area_width - left_width

                # 绘制怪物图片（如果有）
                if monster_image:
                    # 调整图片大小
                    image_size = min(50, monster_bg_rect.height - 20)
                    monster_image = pygame.transform.scale(monster_image, (image_size, image_size))
                    # 计算图片位置在左侧区域中居中
                    image_x = summon_area_x + (left_width - image_size) // 2
                    image_y = summon_y + (monster_bg_rect.height - image_size) // 2
                    surface.blit(monster_image, (image_x, image_y))

                # 绘制怪物名称和等级，添加[魅惑]标识
                name_font = pygame.font.SysFont("simhei", 14)
                name_text = name_font.render(f"[魅惑] {monster.name} Lv.{monster.level}", True, (255, 200, 200))
                # 如果有图片，绘制在右侧区域；否则绘制在整个区域
                name_x = summon_area_x + left_width + 5 if monster_image else summon_area_x + 10
                surface.blit(name_text, (name_x, summon_y + 5))

                # 绘制攻击力
                if hasattr(monster, 'attack_range'):
                    if isinstance(monster.attack_range, dict):
                        attack_min = monster.attack_range.get("min", monster.level * 3)
                        attack_max = monster.attack_range.get("max", monster.level * 6)
                    else:
                        attack_min = monster.level * 3
                        attack_max = monster.level * 6
                    attack_text = name_font.render(f"攻击: {attack_min}-{attack_max}", True, (220, 180, 180))
                    attack_x = summon_area_x + left_width + 5 if monster_image else summon_area_x + 10
                    surface.blit(attack_text, (attack_x, summon_y + 22))

                # 绘制防御力
                defense = getattr(monster, 'defense', monster.level * 1)
                defense_text = name_font.render(f"防御: {defense}", True, (180, 220, 180))
                defense_text_width = defense_text.get_width()
                defense_x = summon_area_x + summon_area_width - defense_text_width - 10
                surface.blit(defense_text, (defense_x, summon_y + 22))

                # 绘制血条背景
                hp_bar_rect = pygame.Rect(summon_area_x + 10, summon_y + monster_bg_rect.height - 12, summon_area_width - 20, 8)
                pygame.draw.rect(surface, (80, 80, 80), hp_bar_rect)

                # 绘制血条
                hp = getattr(monster, 'hp', 100)
                max_hp = getattr(monster, 'max_hp', 100)
                if max_hp > 0:  # 避免除以零错误
                    hp_ratio = max(0, min(hp / max_hp, 1.0))  # 确保比例在0-1之间
                    hp_width = int(hp_ratio * (summon_area_width - 20))
                    if hp_width > 0:
                        hp_bar_fill_rect = pygame.Rect(summon_area_x + 10, summon_y + monster_bg_rect.height - 12, hp_width, 8)
                        pygame.draw.rect(surface, (220, 60, 60), hp_bar_fill_rect)

                # 绘制血条文字
                hp_text = name_font.render(f"{hp}/{max_hp}", True, (220, 220, 220))
                hp_text_x = summon_area_x + summon_area_width // 2 - hp_text.get_width() // 2
                surface.blit(hp_text, (hp_text_x, summon_y + monster_bg_rect.height - 14))

                # 绘制魅惑剩余时间
                if hasattr(monster, 'charmed_time') and hasattr(monster, 'charmed_duration'):
                    if monster.charmed_duration == -1:
                        # 永久魅惑
                        time_text = name_font.render(f"永久魅惑", True, (255, 180, 150))
                        time_x = summon_area_x + left_width + 5 if monster_image else summon_area_x + 10
                        surface.blit(time_text, (time_x, summon_y + 39))
                # 更新下一个怪物的Y坐标
                summon_y += monster_bg_rect.height + 5  # 增加间距

    def _on_settings_click(self):
        """打开游戏设置"""
        logger.info("打开游戏设置界面")
        # 显示设置界面（自动吃药系统）
        self.ui_manager.show_screen("settings")

    def _on_use_skill_click(self, skill_idx):
        """处理使用技能按钮点击"""
        logger.info(f"尝试使用技能槽位: {skill_idx}")

        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法使用技能：找不到玩家数据")
            return

        # 检查是否有技能可用
        if not hasattr(player, 'skills') or not player.skills:
            logger.warning("玩家没有学习任何技能")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 获取技能槽
            slot = self.skill_manager.get_slot(skill_idx)
            if not slot or slot.is_empty():
                logger.warning(f"技能槽位 {skill_idx} 未绑定技能或不存在")
                return

            skill_id = slot.skill_id
            logger.info(f"使用技能管理器获取技能: 槽位 {skill_idx} -> 技能 {skill_id}")
        else:
            # 兼容旧版本
            # 处理初始技能
            if skill_idx == "initial" or skill_idx == 0:  # 兼容两种索引方式
                # 获取初始技能ID
                initial_skill_id = None
                if player.character_class == "法师":
                    initial_skill_id = "fireball"  # 火球术
                elif player.character_class == "牧师":
                    initial_skill_id = "heal"  # 治愈术
                else:
                    logger.warning("该职业没有初始技能")
                    return

                # 检查初始技能是否存在且已学习
                if initial_skill_id and initial_skill_id in player.skills and player.skills[initial_skill_id] > 0:
                    skill_id = initial_skill_id
                else:
                    logger.warning(f"初始技能 {initial_skill_id} 不可用")
                    return
            else:
                # 获取该位置的技能
                skill_slots = getattr(self, "skill_slots", {})
                if skill_idx not in skill_slots or not skill_slots[skill_idx]:
                    logger.warning(f"技能槽位{skill_idx}未绑定技能")
                    return

                skill_id = self.skill_slots[skill_idx]

        # 确保玩家对象中的技能槽与界面同步
        if hasattr(player, 'skill_slots'):
            if skill_idx not in player.skill_slots or player.skill_slots[skill_idx] != skill_id:
                logger.info(f"同步技能槽 {skill_idx} 到玩家对象: {skill_id}")
                player.skill_slots[skill_idx] = skill_id

        # --- 提前获取技能名称 ---
        try:
            from core.config import GameConfig # 确保导入 GameConfig
            skill_config = GameConfig.get_skill(skill_id, player.character_class)
            # 如果找不到配置，就用 skill_id 作为名称
            skill_name = skill_config["name"] if skill_config else skill_id
        except Exception as e:
            logger.error(f"获取技能配置失败 ({skill_id}): {e}")
            skill_name = skill_id # 获取失败时使用ID
            skill_config = None # 确保 skill_config 有定义
        logger.info(f"获取到技能名称: {skill_name} (ID: {skill_id})")
        # --- 结束 ---

        # 检查玩家是否处于可行动状态 (例如眩晕)
        if hasattr(player, 'is_stunned') and player.is_stunned:
            self.game_manager.add_log("你现在处于眩晕状态，无法使用技能！")
            return

        # --- 开始：为法师/道士设置施法状态 ---
        is_caster = player.character_class in ["法师", "道士"]
        try:
            if is_caster:
                player.is_casting_skill = True
                logger.debug(f"玩家 {player.name} ({player.character_class}) 开始施放技能 {skill_name}，暂停普攻。")
            # --- 结束：为法师/道士设置施法状态 ---

            # 尝试使用技能
            result = player.use_skill(skill_id)

            # 确保技能使用信息添加到战斗日志
            if self.game_manager and hasattr(self.game_manager, 'add_log'):
                self.game_manager.add_log(f"使用了技能 [{skill_name}]", True)

            # 处理技能使用结果
            if not result:
                # 技能使用失败，可能是冷却中、全局冷却中或MP不足
                cooldown_info = player.get_skill_cooldown_info()
                mp_cost = skill_config.get("mp_cost", 0) if skill_config else 0 # 从配置获取MP消耗

                # 检查全局冷却
                global_cooldown_info = player.get_global_cooldown_info() if hasattr(player, 'get_global_cooldown_info') else None
                global_cooldown_remaining = global_cooldown_info.get('remaining', 0) if global_cooldown_info else 0

                if global_cooldown_remaining > 0:
                    # 全局冷却中
                    self.game_manager.add_log(f"技能冷却中，无法使用 [{skill_name}]，剩余 {global_cooldown_remaining:.1f} 秒")
                elif skill_id in cooldown_info and cooldown_info[skill_id] > 0:
                    # 技能自身冷却中
                    self.game_manager.add_log(f"技能 [{skill_name}] 冷却中，剩余 {cooldown_info[skill_id]:.1f} 秒") # 使用提前获取的 skill_name
                elif player.mp < mp_cost:
                    # MP不足
                    self.game_manager.add_log(f"MP不足，无法使用技能 [{skill_name}] (需要 {mp_cost} MP)") # 使用提前获取的 skill_name
                else:
                    # 其他失败原因，use_skill内部通常会打日志
                    self.game_manager.add_log(f"无法使用技能 [{skill_name}]") # 使用提前获取的 skill_name
                # self._update_skill_buttons() # 无论成功失败都更新按钮 # 移除这行，移到finally块
                return # 失败时直接返回，finally块会处理施法状态

            # --- 技能使用成功 ---
            # skill_name 变量已在前面定义，这里不需要重新赋值
            # skill_name = result["skill_name"] # <--- 这行已不需要
            effect_type = result["effect_type"]
            effect_value = result["effect_value"]

            # 根据效果类型执行不同逻辑
            if effect_type == "damage_multiplier":
                # 烈火剑法：造成倍率伤害
                self._use_damage_multiplier_skill(result)
            elif effect_type == "stun":
                # 野蛮冲撞：眩晕目标
                self._use_stun_skill(result)
            elif effect_type == "aoe_damage":
                # 半月弯刀：对额外目标造成伤害
                self._use_aoe_skill(result)
            elif effect_type == "heal":
                # 治疗术：恢复生命值
                self._use_heal_skill(result)
            elif effect_type == "summon":
                # 召唤技能：召唤骷髅或神兽
                self._use_summon_skill(result)
            elif effect_type == "direct_damage":
                # 直接伤害：造成直接攻击伤害
                self._use_direct_damage_skill(result)
            elif effect_type == "damage_percent":
                # 伤害百分比增加：造成百分比额外伤害
                self._use_damage_percent_skill(result)
            elif effect_type == "extra_damage":
                # 额外伤害：在基础伤害上增加额外伤害
                self._use_extra_damage_skill(result)
            elif effect_type == "poison":
                # 毒药：造成持续伤害
                self._use_poison_skill(result)
            elif effect_type == "ground_damage":
                # 地面伤害：在地面创建伤害区域（火墙等）
                self._use_ground_damage_skill(result)
            elif effect_type == "charm":
                # 诱惑/控制技能：控制或驯服怪物
                self._use_charm_skill(result)
            elif effect_type == "defense":
                # 物理防御增益技能
                self._use_defense_buff_skill(result)
            elif effect_type == "magic_defense":
                # 魔法防御增益技能
                self._use_magic_defense_buff_skill(result)
            elif effect_type == "agility":
                # 敏捷增益技能
                self._use_agility_buff_skill(result)
            elif effect_type == "magic_damage":
                # 真实伤害技能(无视防御) # 注释修正：应为魔法伤害
                self._use_magic_damage_skill(result)
            elif effect_type == "percentage_health_damage":
                # 基于生命值百分比的伤害技能
                self._use_percentage_health_damage_skill(result)
            elif effect_type == "damage_reduction":
                # 伤害减免技能（如魔法盾）
                self._use_damage_reduction_skill(result)
            else:
                logger.warning(f"未知的技能效果类型: {effect_type}")

            # 更新技能按钮冷却状态 # 移除这行，移到finally块
            # self._update_skill_buttons()

        except Exception as e:
             logger.error(f"处理技能 {skill_name} (ID:{skill_id}) 时发生错误: {e}")
             import traceback
             logger.error(traceback.format_exc())
             # 添加用户提示
             self.game_manager.add_log(f"使用技能 {skill_name} 时出错，请检查日志。")
        finally:
            # --- 开始：重置法师/道士的施法状态 ---
            if is_caster and hasattr(player, 'is_casting_skill') and player.is_casting_skill:
                player.is_casting_skill = False
                logger.debug(f"玩家 {player.name} ({player.character_class}) 完成施放，恢复普攻。")
            # --- 结束：重置法师/道士的施法状态 ---

            # 总是更新技能按钮状态
            self._update_skill_buttons()

    def _use_damage_multiplier_skill(self, skill_result):
        """使用倍率伤害技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 1.0)

        # 获取目标怪物
        monster = self.game_manager.current_enemy
        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 计算玩家基础伤害
        player = self.game_manager.player
        base_damage, is_crit = player.calculate_damage()

        # 应用技能倍率
        skill_damage = int(base_damage * effect_value)

        # 应用伤害
        monster.hp = max(0, monster.hp - skill_damage)

        # 添加战斗日志
        crit_text = "暴击!" if is_crit else ""
        self.game_manager.add_log(f"[{skill_name}]{crit_text} 对 {monster.name} 造成 {skill_damage} 点伤害!")


        # 更新怪物状态显示
        self.update_monster_display(monster)

    def _use_stun_skill(self, skill_result):
        """使用眩晕技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 1.0)

        # 获取目标怪物
        monster = self.game_manager.current_enemy
        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 设置怪物眩晕状态
        self.game_manager.set_monster_stun(effect_value)

        # 添加战斗日志
        self.game_manager.add_log(f"[{skill_name}]眩晕 {monster.name} {effect_value} 秒!")

        # 更新怪物状态显示
        self.update_monster_display(monster)

    def _use_aoe_skill(self, skill_result):
        """使用AOE技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 50)
        targets = skill_result.get("targets", 2)  # 默认影响2个额外目标

        # 获取目标怪物
        monster = self.game_manager.current_enemy
        if not monster:
            logger.warning(f"使用{skill_name}失败: 没有目标")
            return

        # 计算玩家基础伤害
        player = self.game_manager.player
        base_damage, is_crit = player.calculate_damage()

        # 对主目标应用全额伤害
        monster.hp = max(0, monster.hp - base_damage)

        # 添加战斗日志
        crit_text = "暴击!" if is_crit else ""
        self.game_manager.add_log(f"[{skill_name}]{crit_text} 对 {monster.name} 造成 {base_damage} 点伤害!")

        # 对额外目标应用部分伤害 (在实际游戏中应该是随机选择同区域的其他怪物)
        aoe_damage = int(base_damage * effect_value / 100)
        self.game_manager.add_log(f"[{skill_name}]对周围目标造成 {aoe_damage} 点伤害!")

        # 检查怪物是否死亡
        if monster.hp <= 0:
            self.game_manager.handle_monster_death()

        # 更新怪物状态显示
        self.update_monster_display(monster)

    def _use_heal_skill(self, skill_result):
        """使用治疗技能"""
        skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
        effect_value = skill_result.get("effect_value", 0.2)

        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning(f"使用{skill_name}失败: 找不到玩家")
            return

        # 计算治疗量，effect_value可以是固定值或基于最大生命值的百分比
        heal_amount = 0
        try:
            if isinstance(effect_value, (int, float)):
                if effect_value < 1:  # 假设小于1的值为百分比
                    heal_amount = int(player.max_hp * effect_value)
                    logger.info(f"基于百分比治疗: {effect_value * 100}% 最大生命值 = {heal_amount}点")
                else:
                    heal_amount = int(effect_value)
                    logger.info(f"固定值治疗: {heal_amount}点")
            elif isinstance(effect_value, str) and effect_value.endswith('%'):
                # 处理字符串百分比，如 "20%"
                try:
                    percent = float(effect_value.rstrip('%')) / 100
                    heal_amount = int(player.max_hp * percent)
                    logger.info(f"字符串百分比治疗: {effect_value} 最大生命值 = {heal_amount}点")
                except ValueError:
                    logger.warning(f"无效的治疗百分比字符串: {effect_value}，使用默认值")
                    heal_amount = int(player.max_hp * 0.2)
            elif isinstance(effect_value, dict):
                # 处理复杂治疗效果，如 {"base": 100, "per_level": 20, "percent": 0.05}
                base = effect_value.get("base", 0)
                per_level = effect_value.get("per_level", 0)
                percent = effect_value.get("percent", 0)

                skill_level = skill_result.get("skill_level", 1)
                heal_amount = int(base + (per_level * (skill_level - 1)))

                if percent > 0:
                    heal_amount += int(player.max_hp * percent)

                logger.info(f"复合治疗: 基础{base} + 等级加成{per_level}*{skill_level-1} + {percent*100}%最大生命值 = {heal_amount}点")
            else:
                logger.warning(f"无效的治疗效果值类型: {type(effect_value)}, 值: {effect_value}")
                heal_amount = int(player.max_hp * 0.2)  # 默认治疗20%最大生命值
        except Exception as e:
            logger.error(f"计算治疗量时出错: {e}，使用默认值")
            heal_amount = int(player.max_hp * 0.2)  # 默认治疗20%最大生命值

        # 应用治疗效果
        old_hp = player.hp
        player.hp = min(player.max_hp, player.hp + heal_amount)
        actual_heal = player.hp - old_hp

        # 添加战斗日志
        self.game_manager.add_log(f"[{skill_name}]恢复了 {actual_heal} 点生命值!")

        # 更新玩家状态显示
        self.update_player_stats(player)

        # 添加治疗视觉效果(如果有)
        if hasattr(self.game_manager, 'add_combat_effect'):
            self.game_manager.add_combat_effect("heal",
                                               player.hp_bar_rect.centerx if hasattr(player, 'hp_bar_rect') else 200,
                                               player.hp_bar_rect.centery if hasattr(player, 'hp_bar_rect') else 100)

    def _update_skill_buttons(self):
        """更新技能按钮状态"""
        # 获取玩家
        player = self.game_manager.player

        # 检查是否有skill_buttons属性，防止界面初始化不完整的情况
        if not hasattr(self, "skill_buttons") or not self.skill_buttons:
            logger.warning("技能按钮未初始化")
            return

        # 如果玩家不存在、没有技能属性或技能列表为空，将所有技能按钮置为空状态
        if not player or not hasattr(player, 'skills') or not player.skills:
            # 只有在之前记录为有技能的情况下才打印日志
            if not getattr(self, '_logged_no_skills', False):
                logger.info("玩家没有技能，设置所有技能按钮为空状态")
                self._logged_no_skills = True # 标记已记录无技能

            # 设置为空槽位显示
            for slot_idx, skill_btn in self.skill_buttons.items():
                # 设置为空槽位显示
                if slot_idx == "initial" or slot_idx == 0:  # 兼容两种索引方式
                    button_text = "初"
                else:
                    button_text = f"{slot_idx}"
                skill_btn.set_text(button_text)
                skill_btn.set_active(False)
                skill_btn.colors.update({
                    "normal": (40, 40, 70),  # 更新为新的颜色
                    "hover": (50, 50, 80),
                    "pressed": (30, 30, 60),
                    "text": (180, 180, 180),
                    "border": (80, 80, 120)
                })
                skill_btn.image = None  # 清除图片
            return
        else:
             # 玩家有技能，重置日志标志
             self._logged_no_skills = False

        # 初始化技能槽位映射 (如果尚未创建)
        if not hasattr(self, "skill_slots"):
            self.skill_slots = {}

        # 检查技能槽中的技能是否被禁用，如果被禁用则从技能槽中移除
        if hasattr(player, 'disabled_skills'):
            for slot_idx, skill_id in list(self.skill_slots.items()):
                if skill_id in player.disabled_skills:
                    logger.info(f"从槽位{slot_idx+1}移除已禁用技能: {skill_id}")
                    del self.skill_slots[slot_idx]

        # 如果尚未创建skill_slots，则自动分配技能
        if len(self.skill_slots) == 0:
            # 自动将前6个已学习的主动技能分配到技能槽
            active_skills = []
            for skill_id, level in player.skills.items():
                if level <= 0:
                    continue

                # 跳过已禁用的技能
                if hasattr(player, 'disabled_skills') and skill_id in player.disabled_skills:
                    logger.debug(f"跳过已禁用技能: {skill_id}")
                    continue

                try:
                    from core.config import GameConfig
                    skill_config = GameConfig.get_skill(skill_id, player.character_class)
                    if skill_config and skill_config["type"] == "active":
                        active_skills.append(skill_id)
                except Exception as e:
                    logger.error(f"获取技能配置失败: {e}")

                if len(active_skills) >= 6:  # 修改为支持6个技能
                    break

            # 分配技能到槽位
            for i, skill_id in enumerate(active_skills):
                self.skill_slots[i] = skill_id

        # 获取技能冷却信息 (安全地获取)
        cooldown_info = {}
        try:
            if hasattr(player, 'get_skill_cooldown_info'):
                cooldown_info = player.get_skill_cooldown_info()
        except Exception as e:
            logger.error(f"获取技能冷却信息失败: {e}")

        # 更新技能按钮
        for slot_idx, skill_btn in self.skill_buttons.items():
            try:
                # 检查槽位是否分配了技能
                if slot_idx not in self.skill_slots or not self.skill_slots[slot_idx]:
                    # 未分配技能
                    button_text = "初" if slot_idx == 0 else f"{slot_idx}"
                    skill_btn.set_text(button_text)
                    skill_btn.set_active(False)

                    # 为初始技能槽设置特殊样式
                    if slot_idx == 0:
                        skill_btn.colors.update({
                            "normal": (40, 40, 70),
                            "hover": (50, 50, 80),
                            "pressed": (30, 30, 60),
                            "text": (180, 180, 180),
                            "border": (255, 215, 0)  # 金色边框
                        })
                        skill_btn.border_width = 2  # 加粗边框
                    else:
                        skill_btn.colors.update({
                            "normal": (40, 40, 70),  # 更新为新的颜色
                            "hover": (50, 50, 80),
                            "pressed": (30, 30, 60),
                            "text": (180, 180, 180),
                            "border": (80, 80, 120)
                        })
                        skill_btn.border_width = 1  # 正常边框

                    skill_btn.image = None  # 清除图片
                    continue

                # 获取槽位对应的技能
                skill_id = self.skill_slots[slot_idx]

                # 如果技能已被禁用，从槽位中移除（但不移除0号位置的初始技能）
                if hasattr(player, 'disabled_skills') and skill_id in player.disabled_skills and slot_idx != 0:
                    logger.info(f"从槽位{slot_idx}移除已禁用技能: {skill_id}")
                    del self.skill_slots[slot_idx]

                    # 同步到玩家对象
                    if hasattr(player, 'skill_slots') and slot_idx in player.skill_slots:
                        del player.skill_slots[slot_idx]
                        logger.debug(f"从玩家对象中移除槽位 {slot_idx} 的禁用技能")

                    # 将按钮设置为空槽位显示
                    button_text = "初" if slot_idx == 0 else f"{slot_idx}"
                    skill_btn.set_text(button_text)
                    skill_btn.set_active(False)

                    # 为初始技能槽设置特殊样式
                    if slot_idx == 0:
                        skill_btn.colors.update({
                            "normal": (40, 40, 70),
                            "hover": (50, 50, 80),
                            "pressed": (30, 30, 60),
                            "text": (180, 180, 180),
                            "border": (255, 215, 0)  # 金色边框
                        })
                        skill_btn.border_width = 2  # 加粗边框
                    else:
                        skill_btn.colors.update({
                            "normal": (40, 40, 70),
                            "hover": (50, 50, 80),
                            "pressed": (30, 30, 60),
                            "text": (180, 180, 180),
                            "border": (80, 80, 120)
                        })
                        skill_btn.border_width = 1  # 正常边框

                    skill_btn.image = None  # 清除图片
                    continue

                # 安全地获取技能配置
                skill_config = None
                try:
                    from core.config import GameConfig
                    skill_config = GameConfig.get_skill(skill_id, player.character_class)
                except Exception as e:
                    logger.error(f"获取技能配置失败: {e}")

                if not skill_config:
                    # 技能配置获取失败，显示为空槽位
                    skill_btn.set_text(f"{slot_idx+1}")
                    skill_btn.set_active(False)
                    skill_btn.colors.update({
                        "normal": (40, 40, 70),
                        "hover": (50, 50, 80),
                        "pressed": (30, 30, 60),
                        "text": (180, 180, 180),
                        "border": (80, 80, 120)
                    })
                    skill_btn.image = None  # 清除图片
                    continue

                # 获取技能名称和等级
                skill_name = skill_config["name"]
                skill_level = player.skills.get(skill_id, 0)

                # --- 修改技能图片加载逻辑 ---
                skill_image = None
                # 使用技能名称作为缓存键，如果名称为空则用ID
                image_key = skill_name if skill_name else skill_id

                # 检查缓存
                if image_key in self.skill_icon_cache:
                    skill_image = self.skill_icon_cache[image_key]
                else:
                    # 不在缓存中，尝试加载
                    loaded_image = None
                    try:
                        # --- 修改：添加 assets/images/ 前缀 ---
                        full_skill_path = f"assets/images/skill/{skill_name}.png"
                        # --- 结束修改 ---
                        loaded_image = resources.load_image(full_skill_path)

                        # 存入缓存 (即使加载失败也存 None)
                        self.skill_icon_cache[image_key] = loaded_image
                        skill_image = loaded_image
                        if skill_image:
                            logger.info(f"成功加载并缓存技能图标：{skill_name} (Key: {image_key}, Path: {full_skill_path})")
                        else:
                            # 使用完整路径记录失败日志
                            logger.warning(f"无法加载技能图标: {full_skill_path} (Key: {image_key})")

                    except Exception as e:
                        logger.error(f"加载技能图片 (Key: {image_key}) 时出错: {e}")
                        self.skill_icon_cache[image_key] = None # 标记为加载失败

                # 使用加载或缓存的图片
                if skill_image:
                    # 调整图片大小以适应按钮，保持原始宽高比
                    try:
                        # 获取按钮尺寸，留出边距
                        btn_width = skill_btn.rect.width - 8  # 左右各留4像素边距
                        btn_height = skill_btn.rect.height - 8  # 上下各留4像素边距

                        # 获取原始图片尺寸
                        img_width, img_height = skill_image.get_size()

                        # 计算缩放比例，保持原始宽高比
                        scale_factor = min(btn_width / img_width, btn_height / img_height)
                        new_width = int(img_width * scale_factor)
                        new_height = int(img_height * scale_factor)

                        # 缩放图片
                        scaled_image = pygame.transform.smoothscale(skill_image, (new_width, new_height))

                        # 创建一个透明的Surface，大小与按钮内部区域相同
                        final_image = pygame.Surface((btn_width, btn_height), pygame.SRCALPHA)

                        # 计算居中位置
                        x_offset = (btn_width - new_width) // 2
                        y_offset = (btn_height - new_height) // 2

                        # 将缩放后的图片绘制到透明Surface上的居中位置
                        final_image.blit(scaled_image, (x_offset, y_offset))

                        # 设置按钮图片
                        skill_btn.image = final_image
                        skill_btn.set_text(f"Lv.{skill_level}") # 有图片时显示等级
                    except Exception as e:
                         logger.error(f"缩放技能图标失败 (Key: {image_key}): {e}")
                         logger.error(traceback.format_exc())
                         skill_btn.image = None
                         skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}") # 出错时回退到文本
                else:
                    # 如果无法加载图片，使用文本
                    skill_btn.image = None
                    skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}")

                # ... (检查技能冷却, 设置按钮状态和颜色等) ...

                cooldown_remaining = cooldown_info.get(skill_id, 0)
                is_on_cooldown = cooldown_remaining > 0

                if is_on_cooldown:
                    # --- 添加日志确认进入冷却逻辑 ---
                    remaining_int = int(cooldown_remaining)
                    # 处理日志中的槽位显示
                    if slot_idx == "initial":
                        slot_display = "初始"
                    else:
                        slot_display = str(slot_idx)
                    logger.info(f"技能 {skill_name} (槽位 {slot_display}) 冷却中，剩余 {remaining_int} 秒，设置 active=False")
                    # ---------------------------------

                    # 技能冷却中，禁用按钮并显示灰色
                    skill_btn.set_active(False)
                    skill_btn.colors.update({
                        "normal": (50, 50, 80),
                        # --- 修改冷却文本颜色为白色 ---
                        "text": (255, 255, 255),
                        # ---------------------------
                        "border": (80, 80, 120)
                    })

                    # 显示冷却时间
                    if remaining_int > 0:
                        skill_btn.set_text(f"{remaining_int}秒")
                    else:
                        # 如果冷却时间小于1秒但大于0，显示图片或等级
                         if skill_image:
                             skill_btn.set_text(f"Lv.{skill_level}")
                         else:
                             skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}")

                else:
                    # 技能可用
                    # 处理日志中的槽位显示
                    if slot_idx == "initial":
                        slot_display = "初始"
                    else:
                        slot_display = str(slot_idx)

                    logger.info(f"技能 {skill_name} (槽位 {slot_display}) 可用，设置 active=True")
                    skill_btn.set_active(True) # 确保在设置颜色前激活

                    # 统一所有技能的背景色
                    default_border_color = (70, 140, 100) # 统一使用绿色系边框

                    # 所有技能使用相同的颜色方案
                    skill_btn.colors.update({
                        "normal": (40, 80, 60), "hover": (50, 100, 70),
                        "pressed": (30, 70, 50), "text": (220, 255, 220),
                        "border": default_border_color
                    })

                    # --- 恢复按钮文本 (如果之前显示冷却) ---
                    if skill_image:
                        skill_btn.set_text(f"Lv.{skill_level}")
                    else:
                        skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}")
                    # ------------------------------------

                    # 检查技能是否已禁用（使用新的技能管理器）
                    is_disabled = False
                    if self.skill_manager:
                        slot = self.skill_manager.get_slot(slot_idx)
                        if slot and not slot.enabled:
                            is_disabled = True

                    # 如果技能被禁用，使用灰色显示
                    if is_disabled:
                        skill_btn.colors.update({
                            "normal": (60, 60, 80),
                            "hover": (70, 70, 90),
                            "pressed": (50, 50, 70),
                            "text": (150, 150, 150),
                            "border": (100, 100, 120)
                        })
                        skill_btn.border_width = 1

                        # 添加禁用标记
                        current_text = skill_btn.get_text() if hasattr(skill_btn, 'get_text') else ""
                        if not "(禁用)" in current_text:
                            if skill_image:  # 如果有图片，只显示等级和禁用标记
                                skill_btn.set_text(f"Lv.{skill_level}(禁用)")
                            else:  # 如果没有图片，显示名称、等级和禁用标记
                                skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}(禁用)")
                        continue  # 跳过后续处理

                    # 为初始技能槽设置特殊样式
                    if slot_idx == "initial" or slot_idx == 0:  # 兼容两种索引方式
                        # 初始技能使用金色边框
                        skill_btn.colors["border"] = (255, 215, 0)  # 金色边框
                        skill_btn.border_width = 2  # 加粗边框

                        # 添加初始技能标记
                        current_text = skill_btn.get_text() if hasattr(skill_btn, 'get_text') else ""
                        if not "(初始)" in current_text:
                            if skill_image:  # 如果有图片，只显示等级和初始标记
                                skill_btn.set_text(f"Lv.{skill_level}(初始)")
                            else:  # 如果没有图片，显示名称、等级和初始标记
                                skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}(初始)")

                    # 为自动释放的技能添加绿色边框效果和标记
                    elif (hasattr(self, 'auto_cast_skills') and slot_idx in self.auto_cast_skills) or \
                         (self.skill_manager and self.skill_manager.get_slot(slot_idx) and self.skill_manager.get_slot(slot_idx).auto_cast):
                        skill_btn.colors["border"] = (0, 200, 0)  # 绿色边框
                        skill_btn.border_width = 3  # 加粗边框

                        # 添加自动释放标记
                        current_text = skill_btn.get_text() if hasattr(skill_btn, 'get_text') else ""
                        if not "(自动)" in current_text:
                            if skill_image:  # 如果有图片，只显示等级和自动标记
                                skill_btn.set_text(f"Lv.{skill_level}(自动)")
                            else:  # 如果没有图片，显示名称、等级和自动标记
                                skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}(自动)")
                    else:
                        # --- 修复：恢复默认边框宽度和颜色 ---
                        skill_btn.border_width = 1
                        skill_btn.colors["border"] = default_border_color # 恢复默认颜色

                        # 移除自动释放标记
                        current_text = skill_btn.get_text() if hasattr(skill_btn, 'get_text') else ""
                        if "(自动)" in current_text or "(初始)" in current_text or "(禁用)" in current_text:
                            if skill_image:  # 如果有图片，只显示等级
                                skill_btn.set_text(f"Lv.{skill_level}")
                            else:  # 如果没有图片，显示名称和等级
                                skill_btn.set_text(f"{skill_name}\\nLv.{skill_level}")
                        # ----------------------------------

            except Exception as e:
                logger.error(f"更新技能按钮 {slot_idx} 时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

    def _auto_cast_skills(self, dt):
        """自动释放技能槽中的技能

        参数:
            dt: 时间增量
        """
        # 使用新的技能管理器
        if self.skill_manager:
            # 如果不在战斗中，不释放技能
            if not self.is_battle_active or not self.game_manager.in_battle:
                return

            # 使用技能管理器的自动释放方法
            self.skill_manager.auto_cast_skills(self.game_manager)
            return

        # 以下是旧版本代码，用于兼容性保留
        # 如果没有自动释放技能的字典，创建一个
        if not hasattr(self, 'auto_cast_skills'):
            self.auto_cast_skills = {}

        # 确保技能槽位字典存在
        if not hasattr(self, 'skill_slots') or self.skill_slots is None:
            self.skill_slots = {}

        # 确保所有技能槽都在自动释放字典中
        for slot_idx in self.skill_slots.keys():
            if slot_idx not in self.auto_cast_skills:
                skill_id = self.skill_slots[slot_idx]
                # 获取技能冷却时间
                cooldown = 0
                try:
                    player = self.game_manager.player
                    if player:
                        # 检查技能是否存在
                        skill_config = GameConfig.get_skill(skill_id, player.character_class)
                        if not skill_config:
                            logger.warning(f"跳过不存在的技能: {skill_id}")
                            # 从技能槽中移除不存在的技能
                            del self.skill_slots[slot_idx]
                            if hasattr(player, 'skill_slots') and slot_idx in player.skill_slots:
                                del player.skill_slots[slot_idx]
                            continue

                        cooldown = skill_config.get("cooldown", 0)
                        if isinstance(cooldown, list):
                            skill_level = player.skills.get(skill_id, 1)
                            if skill_level <= len(cooldown):
                                cooldown = cooldown[skill_level - 1]
                            else:
                                cooldown = cooldown[-1]
                except Exception as e:
                    logger.error(f"获取技能冷却时间失败: {e}")

                self.auto_cast_skills[slot_idx] = {
                    "last_cast_time": 0,
                    "cooldown": cooldown,
                    "skill_id": skill_id  # 添加skill_id字段，确保知道这个槽位对应哪个技能
                }

                # 处理日志中的槽位显示
                if slot_idx == "initial":
                    slot_display = "初始"
                else:
                    slot_display = str(slot_idx)

                logger.info(f"添加技能槽 {slot_display} 到自动释放字典，技能ID: {skill_id}")

        # 如果不在战斗中，不释放技能
        if not self.is_battle_active or not self.game_manager.in_battle:
            return

        # 获取玩家
        player = self.game_manager.player
        if not player:
            return

        # 获取当前时间
        current_time = time.time()

        # 检查全局技能冷却
        global_cooldown_remaining = 0
        if hasattr(player, 'get_global_cooldown_info'):
            global_cooldown_info = player.get_global_cooldown_info()
            global_cooldown_remaining = global_cooldown_info.get('remaining', 0)

        # 如果全局冷却中，不释放任何技能
        if global_cooldown_remaining > 0:
            return

        # 获取当前怪物
        current_monster = None
        if hasattr(self.game_manager, "battle_system") and self.game_manager.battle_system:
            current_monster = self.game_manager.battle_system.monster
        elif hasattr(self.game_manager, "current_enemy"):
            current_monster = self.game_manager.current_enemy

        # 检查当前怪物是否可以被魅惑
        monster_can_be_charmed = False
        if current_monster:
            # 定义可魅惑的怪物白名单
            CHARM_ALLOWED_MONSTERS = [
                "鸡", "鹿", "羊", "钉耙猫", "多钩猫", "虎卫",
                "半兽人", "森林雪人", "狼", "红蛇", "盔甲虫",
                "猎鹰", "蜘蛛", "多角虫", "虎蛇", "洞蛆",
                "半兽战士", "蜈蚣", "黑色恶蛆", "半兽勇士",
                "黑暗战士", "巨型多角虫", "钳虫", "契蛾",
                "牛头魔", "天狼蜘蛛"
            ]
            monster_name = getattr(current_monster, "name", "")
            if monster_name in CHARM_ALLOWED_MONSTERS:
                monster_can_be_charmed = True

        # 如果当前怪物可以被魅惑，优先使用诱惑之光技能
        if monster_can_be_charmed:
            # 查找诱惑之光技能
            charm_skill_id = "charm_light"

            # 遍历所有自动释放的技能槽，查找诱惑之光技能
            for slot_idx in list(self.auto_cast_skills.keys()):
                # 检查槽位是否存在
                if not hasattr(self, 'skill_slots') or self.skill_slots is None or slot_idx not in self.skill_slots:
                    continue

                # 获取技能ID
                skill_id = self.skill_slots[slot_idx]

                # 检查是否是诱惑之光技能
                if skill_id == charm_skill_id:
                    # 检查技能是否在冷却中
                    cooldown_remaining = 0
                    if hasattr(player, 'skill_cooldowns') and skill_id in player.skill_cooldowns:
                        if current_time < player.skill_cooldowns[skill_id]:
                            cooldown_remaining = player.skill_cooldowns[skill_id] - current_time

                    if cooldown_remaining > 0:
                        break

                    # 检查自动释放冷却
                    auto_cast_info = self.auto_cast_skills[slot_idx]
                    if current_time - auto_cast_info["last_cast_time"] < auto_cast_info["cooldown"]:
                        break

                    # 尝试使用诱惑之光技能
                    logger.info(f"优先自动释放诱惑之光技能: {skill_id}")
                    success = player.use_skill(skill_id)

                    if success:
                        # 更新最后释放时间
                        self.auto_cast_skills[slot_idx]["last_cast_time"] = current_time
                        # 成功使用诱惑之光技能后，直接返回，不再使用其他技能
                        return
                    break

        # 遍历所有自动释放的技能槽
        for slot_idx in list(self.auto_cast_skills.keys()):
            # 检查槽位是否存在
            if not hasattr(self, 'skill_slots') or self.skill_slots is None or slot_idx not in self.skill_slots:
                continue

            # 获取技能ID
            skill_id = self.skill_slots[slot_idx]

            # 检查技能是否在冷却中
            cooldown_remaining = 0
            if hasattr(player, 'skill_cooldowns') and skill_id in player.skill_cooldowns:
                if current_time < player.skill_cooldowns[skill_id]:
                    cooldown_remaining = player.skill_cooldowns[skill_id] - current_time

            if cooldown_remaining > 0:
                continue

            # 检查自动释放冷却
            auto_cast_info = self.auto_cast_skills[slot_idx]
            if current_time - auto_cast_info["last_cast_time"] < auto_cast_info["cooldown"]:
                continue

            # 尝试使用技能
            if slot_idx == "initial":
                logger.info(f"自动释放初始技能: {skill_id}")
                self._on_use_skill_click("initial")
            else:
                logger.info(f"自动释放技能槽 {slot_idx} 中的技能: {skill_id}")
                self._on_use_skill_click(slot_idx)

            # 更新最后释放时间
            self.auto_cast_skills[slot_idx]["last_cast_time"] = current_time

            # 成功使用一个技能后，本次不再使用其他技能
            break

    def _init_skill_slots(self):
        """初始化技能槽位，将玩家已学习的主动技能分配到技能槽中"""
        logger.info("初始化技能槽位")

        try:
            player = self.game_manager.player
            if not player:
                logger.info("玩家不存在，无法初始化技能槽位")
                return

            # 初始化新的技能管理器
            self.skill_manager = SkillManager(player)
            logger.info("创建新的技能管理器")

            # 兼容旧版本：创建技能槽位字典（如果不存在）
            if not hasattr(self, 'skill_slots') or self.skill_slots is None:
                self.skill_slots = {}
                logger.info("创建新的技能槽位字典")

            # 将新的技能管理器中的技能槽同步到旧的技能槽字典
            for slot_id, slot in self.skill_manager.skill_slots.items():
                if not slot.is_empty():
                    self.skill_slots[slot_id] = slot.skill_id
                    logger.info(f"从技能管理器同步技能槽 {slot_id}: {slot.skill_id}")

            # 更新技能按钮显示
            self._update_skill_buttons()
            return

            # 以下代码保留但不再执行，用于兼容旧版本
            # 在完全迁移到新的技能管理器后可以删除
            if False:
                # 确保技能属性是字典类型
                if not player.skills:
                    player.skills = {}
                    logger.info("玩家技能列表为空，初始化为空字典")
                    # 更新技能按钮显示为空状态
                    self._update_skill_buttons()
                    return

                # 设置初始技能到0号位置（固定位置）
                self._setup_initial_skill(player)

            # 首先检查玩家对象中是否已有技能槽配置
            if hasattr(player, 'skill_slots') and player.skill_slots:
                logger.info(f"从玩家对象加载技能槽配置，共 {len(player.skill_slots)} 个槽位")
                # 复制玩家对象中的技能槽配置
                for slot_idx, skill_id in player.skill_slots.items():
                    # 跳过0号位置，因为它是固定的初始技能位置
                    if slot_idx == 0:
                        continue

                    # 检查技能是否有效（已学习且未禁用）
                    if (skill_id in player.skills and
                        player.skills[skill_id] > 0 and
                        skill_id not in player.disabled_skills):
                        self.skill_slots[slot_idx] = skill_id
                        logger.debug(f"从玩家对象加载技能槽 {slot_idx}: {skill_id}")

                # 如果成功加载了技能槽，直接更新按钮并返回
                if self.skill_slots:
                    logger.info(f"成功从玩家对象加载 {len(self.skill_slots)} 个技能槽")
                    self._update_skill_buttons()
                    return

            # 获取所有已学习且未禁用的主动技能
            active_skills = []
            logger.info(f"玩家技能列表: {player.skills}")

            for skill_id, level in player.skills.items():
                if level <= 0:
                    logger.debug(f"跳过未学习的技能: {skill_id}, 等级: {level}")
                    continue

                # 跳过已禁用的技能
                if hasattr(player, 'disabled_skills') and skill_id in player.disabled_skills:
                    logger.debug(f"跳过已禁用技能: {skill_id}")
                    continue

                try:
                    from core.config import GameConfig
                    skill_config = GameConfig.get_skill(skill_id, player.character_class)
                    if skill_config:
                        logger.debug(f"找到技能配置: {skill_id}, 名称: {skill_config.get('name')}, 类型: {skill_config.get('type')}")
                        if skill_config["type"] == "active":
                            active_skills.append(skill_id)
                            logger.info(f"添加主动技能: {skill_id}, 名称: {skill_config.get('name')}")
                    else:
                        logger.warning(f"无法获取技能配置: {skill_id}")
                except Exception as e:
                    logger.error(f"获取技能配置失败: {e}")

            # 记录找到的主动技能数量
            logger.info(f"玩家有 {len(active_skills)} 个可用主动技能: {active_skills}")

            # 检查是否有常规技能槽（除了0号位置）
            has_regular_slots = False
            for slot_idx in self.skill_slots:
                if slot_idx != 0:  # 跳过0号位置
                    has_regular_slots = True
                    break

            # 如果没有常规技能槽，则初始化技能槽
            if not has_regular_slots:
                logger.info("常规技能槽为空，初始化技能槽")

                # 分配所有主动技能到槽位（从1号位置开始）
                slot_idx = 1  # 从1开始，因为0是初始技能
                for skill_id in active_skills:
                    # 获取初始技能ID
                    initial_skill_id = None
                    if player.character_class == "法师":
                        initial_skill_id = "fireball"  # 火球术
                    elif player.character_class == "牧师":
                        initial_skill_id = "heal"  # 治愈术

                    # 跳过初始技能，因为它只能放在初始技能槽位
                    if initial_skill_id and skill_id == initial_skill_id:
                        continue

                    # 获取技能配置
                    skill_config = GameConfig.get_skill(skill_id, player.character_class)
                    if not skill_config:
                        logger.warning(f"跳过无效技能: {skill_id}")
                        continue

                    # 检查技能是否已经在技能槽中
                    # 使用更严格的检查，确保相同ID的技能不会重复添加
                    skill_already_in_slots = False
                    for existing_slot, existing_skill_id in self.skill_slots.items():
                        if existing_skill_id == skill_id:
                            skill_already_in_slots = True
                            logger.info(f"技能 {skill_id} 已存在于槽位 {existing_slot}")
                            break

                    if skill_already_in_slots:
                        continue

                    # 将技能添加到槽位
                    self.skill_slots[slot_idx] = skill_id

                    # 同步到玩家对象
                    if hasattr(player, 'skill_slots'):
                        player.skill_slots[slot_idx] = skill_id
                        logger.info(f"同步技能槽 {slot_idx} 到玩家对象: {skill_id}")
                    else:
                        # 如果玩家对象没有skill_slots属性，创建它
                        if 0 in self.skill_slots:  # 如果有0号位置，保留它
                            player.skill_slots = {0: self.skill_slots[0], slot_idx: skill_id}
                        else:
                            player.skill_slots = {slot_idx: skill_id}
                        logger.info(f"创建玩家技能槽并添加技能: {slot_idx} -> {skill_id}")

                    # 增加槽位索引
                    slot_idx += 1
            else:
                logger.info(f"保留现有技能槽配置: {self.skill_slots}")

                # 检查是否有新的主动技能需要添加到技能槽
                for skill_id in active_skills:
                    # 获取初始技能ID
                    initial_skill_id = None
                    if player.character_class == "法师":
                        initial_skill_id = "fireball"  # 火球术
                    elif player.character_class == "牧师":
                        initial_skill_id = "heal"  # 治愈术

                    # 跳过初始技能，因为它只能放在初始技能槽位
                    if initial_skill_id and skill_id == initial_skill_id:
                        continue

                    # 获取技能配置
                    skill_config = GameConfig.get_skill(skill_id, player.character_class)
                    if not skill_config:
                        logger.warning(f"跳过无效技能: {skill_id}")
                        continue

                    # 检查技能是否已经在技能槽中
                    # 使用更严格的检查，确保相同ID的技能不会重复添加
                    skill_already_in_slots = False
                    for existing_slot, existing_skill_id in self.skill_slots.items():
                        if existing_skill_id == skill_id:
                            skill_already_in_slots = True
                            logger.info(f"技能 {skill_id} 已存在于槽位 {existing_slot}")
                            break

                    if skill_already_in_slots:
                        continue

                    # 查找空闲的技能槽（从1号位置开始）
                    for slot_idx in range(1, 10):  # 支持最多9个常规技能槽（1-9）
                        if slot_idx not in self.skill_slots:
                            # 将技能添加到空闲槽位
                            self.skill_slots[slot_idx] = skill_id
                            logger.info(f"将新技能 {skill_id} 添加到技能槽 {slot_idx}")

                            # 同步到玩家对象
                            if hasattr(player, 'skill_slots'):
                                player.skill_slots[slot_idx] = skill_id

                            break

            # 确保自动释放技能字典存在
            if not hasattr(self, 'auto_cast_skills'):
                self.auto_cast_skills = {}

            # 确保所有技能槽都在自动释放字典中
            for slot_idx in self.skill_slots.keys():
                if slot_idx not in self.auto_cast_skills:
                    skill_id = self.skill_slots[slot_idx]
                    # 获取技能冷却时间
                    cooldown = 0
                    try:
                        # 检查技能是否存在
                        skill_config = GameConfig.get_skill(skill_id, player.character_class)
                        if not skill_config:
                            logger.warning(f"跳过不存在的技能: {skill_id}")
                            # 从技能槽中移除不存在的技能
                            del self.skill_slots[slot_idx]
                            if hasattr(player, 'skill_slots') and slot_idx in player.skill_slots:
                                del player.skill_slots[slot_idx]
                            continue

                        cooldown = skill_config.get("cooldown", 0)
                        if isinstance(cooldown, list):
                            skill_level = player.skills.get(skill_id, 1)
                            if skill_level <= len(cooldown):
                                cooldown = cooldown[skill_level - 1]
                            else:
                                cooldown = cooldown[-1]
                    except Exception as e:
                        logger.error(f"获取技能冷却时间失败: {e}")

                    self.auto_cast_skills[slot_idx] = {
                        "last_cast_time": 0,
                        "cooldown": cooldown,
                        "skill_id": skill_id  # 添加skill_id字段，确保知道这个槽位对应哪个技能
                    }

                    # 处理日志中的槽位显示
                    if slot_idx == "initial":
                        slot_display = "初始"
                    else:
                        slot_display = str(slot_idx)

                    logger.info(f"设置技能槽 {slot_display} 为自动释放，技能ID: {skill_id}，冷却时间: {cooldown}秒")

            # 更新技能按钮显示
            self._update_skill_buttons()

        except Exception as e:
            logger.error(f"初始化技能槽位时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 即使出错也创建一个空的技能槽位
            self.skill_slots = {}
            # 尝试更新技能按钮显示
            try:
                self._update_skill_buttons()
            except:
                pass

    def _update_inventory_indicator(self):
        """更新背包数量指示器"""
        logger.debug("更新背包数量指示器")
        try:
            player = self.game_manager.player
            if not player or not hasattr(player, 'inventory'):
                logger.warning("玩家不存在或没有背包属性，无法更新背包指示器")
                return

            # 如果背包按钮存在并有对应的徽章，则更新背包数量
            # 由于本游戏中没有实现此功能，这里只是留作未来扩展
            inventory_size = len(player.inventory) if hasattr(player, 'inventory') else 0
            logger.debug(f"背包物品数量: {inventory_size}")

        except Exception as e:
            logger.error(f"更新背包指示器时出错: {e}")

    def _on_vip_click(self):
        """处理点击VIP按钮"""
        logger.info("打开VIP界面")
        self.ui_manager.show_screen("vip")

    def _on_signin_click(self):
        """处理点击签到按钮"""
        logger.info("打开签到界面")
        self.ui_manager.show_screen("signin")

    def on_show(self):
        """当屏幕显示时调用"""
        logger.info("游戏主界面显示")
        # 调用核心刷新方法
        self._refresh_ui_state()

        # 检查是否是新创建的角色，如果是则自动启动寻怪模式
        if (self.game_manager and self.game_manager.player and
            self.game_manager.game_mode == 'idle' and
            not self.game_manager.in_battle):
            logger.info("检测到新角色，自动启动寻怪模式")
            self.game_manager.start_battle()  # 启动寻怪模式

    def initialize(self):
        """初始化游戏界面，在显示之前调用（通常在加载游戏后）"""
        logger.info("初始化游戏界面 (通常在加载后)")

        try:
            # 1. 更新战斗日志 (加载存档后可能需要设置初始日志)
            if hasattr(self.game_manager, 'battle_system') and self.game_manager.battle_system:
                battle_logs = self.game_manager.battle_system.battle_logs
                if battle_logs:
                    logger.info(f"从战斗系统加载战斗日志，共{len(battle_logs)}条")
                    self.update_battle_log(battle_logs)
            elif hasattr(self.game_manager, 'battle_logs') and self.game_manager.battle_logs:
                 logger.info(f"从游戏管理器加载战斗日志，共{len(self.game_manager.battle_logs)}条")
                 self.update_battle_log(self.game_manager.battle_logs)
            # 2. 调用核心刷新方法更新其他UI
            self._refresh_ui_state()
            # 3. 初始化技能槽位 (加载游戏后应该根据玩家数据重新设置一次)
            self._init_skill_slots() #

            # 4. 检查是否需要启动寻怪模式（加载存档后）
            if (self.game_manager and self.game_manager.player and
                self.game_manager.game_mode == 'idle' and
                not self.game_manager.in_battle and
                not self.game_manager.player.is_dead):
                logger.info("加载存档后自动启动寻怪模式")
                self.game_manager.start_battle()  # 启动寻怪模式

        except Exception as e:
            logger.error(f"初始化游戏界面时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        logger.info("游戏界面初始化完成")

    def _toggle_auto_potion(self):
        """切换自动吃药系统的状态"""
        if not self.game_manager or not hasattr(self.game_manager, 'auto_potion'):
            logger.warning("自动吃药系统不可用")
            return

        enabled = self.game_manager.auto_potion.toggle()
        status_text = "已启用" if enabled else "已禁用"

        # 更新按钮文本，根据启用状态改变
        button_text = "自动吃药√" if enabled else "自动吃药"
        self.auto_potion_btn.set_text(button_text)

        # 显示状态变更提示
        self.ui_manager.show_message("自动吃药", f"自动吃药系统{status_text}")
        logger.info(f"自动吃药系统{status_text}")

    def _use_summon_skill(self, skill_result):
        """使用召唤技能，创建召唤物"""
        try:
            # 从技能结果中获取信息
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            summon_id = skill_result.get("effect_value", "")  # 召唤物类型保存在 effect_value 中
            duration = skill_result.get("duration", -1)

            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 玩家不存在")
                return

            # 使用召唤工厂创建召唤物
            summon_factory = SummonFactory()
            # 修复参数：传入召唤物类型、玩家等级和持续时间，而不是skill_result
            summon = summon_factory.create_summon(summon_id, player.level, duration)

            if not summon:
                logger.warning(f"使用{skill_name}失败: 无法创建召唤物 ID:{summon_id}")
                self.game_manager.add_log(f"[{skill_name}]召唤失败!")
                return

            # 将召唤物添加到召唤列表
            if not hasattr(player, "summons"):
                player.summons = []

            # 检查最大召唤数量限制 - 修复：使用getattr代替get_attribute
            max_summons = getattr(player, "max_summons", 3)  # 默认最多3个召唤物
            if len(player.summons) >= max_summons:
                # 移除最早的召唤物
                oldest_summon = player.summons.pop(0)
                self.game_manager.add_log(f"{oldest_summon.name} 消失了!")

            # 添加新召唤物
            player.summons.append(summon)

            # 更新战斗状态
            if self.game_manager.battle:
                self.game_manager.battle.update_summons(player)

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]成功召唤了 {summon.name}!")

            # 播放召唤特效
            if hasattr(self, "show_summon_effect"):
                self.show_summon_effect(summon)

            # 更新召唤物UI
            self.update_summons_display()
        except Exception as e:
            logger.error(f"使用召唤技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def add_log(self, message, is_battle=False):
        """添加日志到游戏管理器"""
        if hasattr(self.game_manager, 'add_log'):
            self.game_manager.add_log(message, is_battle)
        else:
            # 如果无法添加日志，则记录错误
            from utils.logger import logger
            logger.error(f"无法添加日志: {message}")

    def _use_direct_damage_skill(self, skill_result):
        """使用直接伤害技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            is_magic_skill = skill_result.get("is_magic_skill", False)

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 计算玩家基础伤害
            player = self.game_manager.player
            base_damage, is_crit = player.calculate_damage()

            # 使用技能固定伤害值，而不是作为倍率
            skill_damage = effect_value

            # 获取魔法/道术附加伤害比例（如果配置中有）
            magic_ratio = skill_result.get("magic_ratio", 0)
            taoism_ratio = skill_result.get("taoism_ratio", 0)

            # 如果技能配置中没有指定魔法/道术比例，但是这是一个魔法系技能，则使用默认值
            if is_magic_skill and magic_ratio == 0 and taoism_ratio == 0:
                # 根据职业选择默认比例
                if player.character_class == "法师":
                    magic_ratio = 0.9  # 默认法师技能依赖魔法攻击90%加成
                elif player.character_class == "道士":
                    taoism_ratio = 0.9  # 默认道士技能依赖道术攻击90%加成

            # 计算魔法/道术附加伤害
            extra_damage = 0
            extra_damage_source = ""

            if magic_ratio > 0:
                try:
                    # 使用幸运值影响的魔法伤害计算
                    magic_damage, magic_is_crit = player.calculate_magic_damage()
                    # 应用ratio
                    magic_damage = int(magic_damage * magic_ratio)
                    extra_damage += magic_damage
                    extra_damage_source = "魔法"
                    # 如果魔法部分暴击，整个技能都算暴击
                    if magic_is_crit:
                        is_crit = True # is_crit 变量在上面已经定义过，这里直接赋值
                except (AttributeError, TypeError) as e:
                    logger.error(f"计算魔法攻击加成失败: {e}")

            if taoism_ratio > 0:
                try:
                    # 使用幸运值影响的道术伤害计算
                    taoism_damage, taoism_is_crit = player.calculate_taoism_damage()
                    # 应用ratio
                    taoism_damage = int(taoism_damage * taoism_ratio)
                    extra_damage += taoism_damage
                    extra_damage_source = "道术" if not extra_damage_source else "魔法和道术"
                    # 如果道术部分暴击，整个技能都算暴击
                    if taoism_is_crit:
                        is_crit = True # is_crit 变量在上面已经定义过，这里直接赋值
                except (AttributeError, TypeError) as e:
                    logger.error(f"计算道术攻击加成失败: {e}")

            # 应用最终伤害
            final_damage = base_damage + extra_damage # 修正：这里应该是 skill_damage + extra_damage
            final_damage = skill_damage + extra_damage # 直接修正

            # 应用伤害到怪物
            monster.hp = max(0, monster.hp - final_damage)

            # 添加战斗日志
            crit_text = "暴击!" if is_crit else ""
            extra_text = f" (含{extra_damage_source}加成{extra_damage})" if extra_damage > 0 and extra_damage_source else ""
            self.game_manager.add_log(f"[{skill_name}]{crit_text} 对 {monster.name} 造成 {final_damage} 点伤害{extra_text}!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用直接伤害技能失败: {e}")
            traceback.print_exc()

    def _use_damage_percent_skill(self, skill_result):
        """使用伤害百分比增加技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 计算玩家基础伤害
            player = self.game_manager.player
            base_damage, is_crit = player.calculate_damage()

            # 应用百分比增加
            percent_bonus = effect_value / 100.0
            skill_damage = int(base_damage * (1 + percent_bonus))

            # 应用伤害
            monster.hp = max(0, monster.hp - skill_damage)

            # 添加战斗日志
            crit_text = "暴击!" if is_crit else ""
            self.game_manager.add_log(f"[{skill_name}]{crit_text} 造成 {skill_damage} 点伤害 (增加{effect_value}%伤害)!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用伤害百分比技能失败: {e}") # 添加日志
            traceback.print_exc()

    def _use_extra_damage_skill(self, skill_result):
        """使用额外伤害技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 计算玩家基础伤害
            player = self.game_manager.player
            base_damage, is_crit = player.calculate_damage()

            # 应用额外伤害(基于百分比计算额外伤害)
            extra_percent = effect_value / 100.0
            extra_damage = int(base_damage * extra_percent)
            total_damage = base_damage + extra_damage

            # 应用伤害
            monster.hp = max(0, monster.hp - total_damage)

            # 添加战斗日志
            crit_text = "暴击!" if is_crit else ""
            self.game_manager.add_log(f"[{skill_name}]{crit_text} 造成 {base_damage} + {extra_damage} = {total_damage} 点伤害!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用额外伤害技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_poison_skill(self, skill_result):
        """使用毒药技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)  # 每秒伤害
            duration = skill_result.get("duration", 15)  # 默认持续15秒

            # 获取目标怪物 - 修复方式
            monster = None
            # 尝试从战斗系统获取怪物
            if hasattr(self.game_manager, "battle_system") and self.game_manager.battle_system:
                monster = self.game_manager.battle_system.monster
            # 如果战斗系统中没有怪物，尝试从current_enemy属性获取
            if not monster and hasattr(self.game_manager, "current_enemy"):
                monster = self.game_manager.current_enemy

            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 设置怪物中毒状态
            if not hasattr(monster, "poison_effects"):
                monster.poison_effects = []

            # 添加毒药效果
            poison_effect = {
                "damage_per_tick": effect_value,
                "remaining_duration": duration,
                "last_tick_time": time.time(),
                "skill_name": skill_name
            }
            monster.poison_effects.append(poison_effect)

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]使 {monster.name} 中毒了! 每秒受到 {effect_value} 点伤害，持续 {duration} 秒!")

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用毒药技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_ground_damage_skill(self, skill_result):
        """使用地面伤害技能（如火墙）"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 5)  # 默认持续5秒
            is_magic_skill = skill_result.get("is_magic_skill", False)

            # 获取目标怪物 - 修复方式
            monster = None
            # 尝试从战斗系统获取怪物
            if hasattr(self.game_manager, "battle_system") and self.game_manager.battle_system:
                monster = self.game_manager.battle_system.monster
            # 如果战斗系统中没有怪物，尝试从current_enemy属性获取
            if not monster and hasattr(self.game_manager, "current_enemy"):
                monster = self.game_manager.current_enemy

            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 获取魔法/道术附加伤害比例（如果配置中有）
            player = self.game_manager.player
            magic_ratio = skill_result.get("magic_ratio", 0)
            taoism_ratio = skill_result.get("taoism_ratio", 0)

            # 如果技能配置中没有指定魔法/道术比例，但是这是一个魔法系技能，则使用默认值
            if is_magic_skill and magic_ratio == 0 and taoism_ratio == 0:
                # 根据职业选择默认比例
                if player.character_class == "法师":
                    magic_ratio = 0.9  # 默认法师技能依赖魔法攻击90%加成
                elif player.character_class == "道士":
                    taoism_ratio = 0.9  # 默认道士技能依赖道术攻击90%加成

            # 计算基础地面伤害
            base_damage = effect_value

            # 计算魔法/道术附加伤害
            extra_damage = 0
            extra_damage_source = ""
            is_crit = False

            if magic_ratio > 0:
                try:
                    # 使用幸运值影响的魔法伤害计算
                    magic_damage, magic_is_crit = player.calculate_magic_damage()
                    # 应用ratio
                    magic_damage = int(magic_damage * magic_ratio)
                    extra_damage += magic_damage
                    extra_damage_source = "魔法"
                    # 如果魔法部分暴击，整个技能都算暴击
                    if magic_is_crit:
                        is_crit = True
                except (AttributeError, TypeError) as e:
                    logger.error(f"计算魔法攻击加成失败: {e}")

            if taoism_ratio > 0:
                try:
                    # 使用幸运值影响的道术伤害计算
                    taoism_damage, taoism_is_crit = player.calculate_taoism_damage()
                    # 应用ratio
                    taoism_damage = int(taoism_damage * taoism_ratio)
                    extra_damage += taoism_damage
                    extra_damage_source = "道术" if not extra_damage_source else "魔法和道术"
                    # 如果道术部分暴击，整个技能都算暴击
                    if taoism_is_crit:
                        is_crit = True
                except (AttributeError, TypeError) as e:
                    logger.error(f"计算道术攻击加成失败: {e}")

            # 应用最终伤害
            final_damage = base_damage + extra_damage

            # 设置怪物中毒状态 - 地面效果不应直接添加到 poison_effects，而是由 BattleSystem 处理
            # if not hasattr(monster, "poison_effects"):
            #     monster.poison_effects = []

            # 添加持续伤害效果(类似毒药效果) - 应由 BattleSystem 管理地面效果
            # effect = {
            #     "damage_per_tick": final_damage,
            #     "remaining_duration": duration,
            #     "last_tick_time": time.time(),
            #     "skill_name": skill_name,
            #     "type": "damage" # 明确效果类型
            # }
            # # 将效果添加到游戏管理器的地面效果列表，而不是怪物身上
            # if hasattr(self.game_manager, "add_ground_effect"):
            #      self.game_manager.add_ground_effect(effect)
            # else:
            #      logger.warning("GameManager 没有 add_ground_effect 方法")


            # 立即造成一次伤害
            monster.hp = max(0, monster.hp - final_damage)

            # 添加战斗日志
            crit_text = "暴击!" if is_crit else ""
            extra_text = f" (含{extra_damage_source}加成{extra_damage})" if extra_damage > 0 and extra_damage_source else ""
            self.game_manager.add_log(f"[{skill_name}]{crit_text} 在地面施放，对 {monster.name} 造成 {final_damage} 点伤害{extra_text}!") # 移除 “并将持续...” 部分

            # 添加地面效果到 BattleSystem (假设 game_manager 有 battle_system)
            if hasattr(self.game_manager, 'battle_system') and self.game_manager.battle_system:
                 ground_effect_data = {
                     "type": "damage", # 效果类型
                     "damage_per_tick": final_damage, # 每跳伤害
                     "remaining_duration": duration, # 持续时间
                     "last_tick_time": time.time(), # 上次触发时间
                     "skill_name": skill_name # 技能名称
                 }
                 # 假设 BattleSystem 有一个方法来添加地面效果
                 if hasattr(self.game_manager.battle_system, 'add_ground_effect'):
                     self.game_manager.battle_system.add_ground_effect(ground_effect_data)
                     self.game_manager.add_log(f"{skill_name} 效果将持续 {duration} 秒")
                 else:
                     logger.warning("BattleSystem 没有 add_ground_effect 方法")
            else:
                 logger.warning("无法访问 BattleSystem 来添加地面效果")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用地面伤害技能失败: {e}")
            traceback.print_exc()

    def _use_charm_skill(self, skill_result):
        """使用诱惑/控制技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0.5)  # 成功率 0-1
            level_diff = skill_result.get("level_diff", 2)  # 默认可控制比自己高2级的怪物

            # 获取目标怪物 - 修复方式
            monster = None
            # 尝试从战斗系统获取怪物
            if hasattr(self.game_manager, "battle_system") and self.game_manager.battle_system:
                monster = self.game_manager.battle_system.monster
            # 如果战斗系统中没有怪物，尝试从current_enemy属性获取
            if not monster and hasattr(self.game_manager, "current_enemy"):
                monster = self.game_manager.current_enemy

            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 获取玩家
            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 找不到玩家")
                return

            # 确保player有charmed_monsters属性
            if not hasattr(player, "charmed_monsters"):
                player.charmed_monsters = []

            # 检查是否已经达到魅惑怪物上限(2只)
            active_charmed = [m for m in player.charmed_monsters if not getattr(m, 'is_dead', False)]
            if len(active_charmed) >= 2:
                self.game_manager.add_log(f"[{skill_name}]失败！你已经控制了2只怪物，无法再控制更多")
                return
            # 定义可魅惑的怪物白名单
            CHARM_ALLOWED_MONSTERS = [
                "鸡", "鹿", "羊", "钉耙猫", "多钩猫", "虎卫",
                "半兽人", "森林雪人", "狼", "红蛇", "盔甲虫",
                "猎鹰", "蜘蛛", "多角虫", "虎蛇", "洞蛆",
                "半兽战士", "蜈蚣", "黑色恶蛆", "半兽勇士",
                "黑暗战士", "巨型多角虫", "钳虫", "契蛾",
                "牛头魔", "天狼蜘蛛"
            ]

            # 检查怪物是否在可魅惑列表中
            monster_name = getattr(monster, "name", "")
            if monster_name not in CHARM_ALLOWED_MONSTERS:
                self.game_manager.add_log(f"[{skill_name}]失败！{monster_name} 不受魅惑之光的影响")
                return

            # 检查怪物等级是否可控制
            if monster.level > player.level + level_diff:
                self.game_manager.add_log(f"[{skill_name}]失败！{monster.name} 的等级太高，无法被控制")
                return

            # 检查怪物类型是否可控制
            if hasattr(monster, "type") and monster.type not in ["beast", "animal", "undead", "human"]:
                self.game_manager.add_log(f"[{skill_name}]失败！{monster.name} 类型的怪物无法被控制")
                return

            # 计算成功率
            success_rate = effect_value
            # 根据等级差调整成功率
            level_factor = 1.0 - (monster.level - player.level) * 0.1
            success_rate = max(0.1, min(0.9, success_rate * level_factor))

            # 检查是否成功
            if random.random() <= success_rate:
                # 控制成功
                if not hasattr(monster, "charmed"):
                    monster.charmed = False

                monster.charmed = True
                monster.charmed_by = player.name
                # 设置魅惑持续时间为-1，表示永久魅惑
                monster.charmed_duration = -1
                monster.charmed_time = time.time()

                # 添加战斗日志
                self.game_manager.add_log(f"[{skill_name}]成功！{monster.name} 被控制了，将永久听从你的指挥！")

                # 确保是深拷贝怪物对象
                import copy
                monster_copy = copy.deepcopy(monster)
                player.charmed_monsters.append(monster_copy)

                # 添加调试日志
                logger.info(f"添加魅惑怪物 {monster.name}，当前属性: charmed={monster.charmed}, duration={monster.charmed_duration}")
                logger.info(f"魅惑怪物已添加，当前玩家魅惑怪物列表长度: {len(player.charmed_monsters)}")

                self.game_manager.end_battle(True)
            else:
                # 控制失败
                self.game_manager.add_log(f"[{skill_name}]失败！{monster.name} 抵抗了控制")
        except Exception as e:
            logger.error(f"使用诱惑/控制技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_defense_buff_skill(self, skill_result):
        """使用物理防御增益技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 60)  # 默认持续60秒

            # 获取玩家
            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 找不到玩家")
                return

            # 创建或更新buff系统
            if not hasattr(player, "buffs"):
                player.buffs = {}

            # 添加防御buff
            buff_id = f"defense_{int(time.time())}"
            player.buffs[buff_id] = {
                "type": "defense",
                "value": effect_value,
                "duration": duration,
                "start_time": time.time(),
                "skill_name": skill_name
            }

            # 刷新玩家属性
            player.recalculate_stats()

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]成功！物理防御增加 {effect_value} 点，持续 {duration} 秒")

            # 更新玩家状态显示
            self.update_player_stats(player)
        except Exception as e:
            logger.error(f"使用物理防御增益技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_magic_defense_buff_skill(self, skill_result):
        """使用魔法防御增益技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 60)  # 默认持续60秒

            # 获取玩家
            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 找不到玩家")
                return

            # 创建或更新buff系统
            if not hasattr(player, "buffs"):
                player.buffs = {}

            # 添加魔法防御buff
            buff_id = f"magic_defense_{int(time.time())}"
            player.buffs[buff_id] = {
                "type": "magic_defense",
                "value": effect_value,
                "duration": duration,
                "start_time": time.time(),
                "skill_name": skill_name
            }

            # 刷新玩家属性
            player.recalculate_stats()

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]成功！魔法防御增加 {effect_value} 点，持续 {duration} 秒")

            # 更新玩家状态显示
            self.update_player_stats(player)
        except Exception as e:
            logger.error(f"使用魔法防御增益技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_agility_buff_skill(self, skill_result):
        """使用敏捷增益技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 60)  # 默认持续60秒

            # 获取玩家
            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 找不到玩家")
                return

            # 创建或更新buff系统
            if not hasattr(player, "buffs"):
                player.buffs = {}

            # 添加敏捷buff
            buff_id = f"agility_{int(time.time())}"
            player.buffs[buff_id] = {
                "type": "agility",
                "value": effect_value,
                "duration": duration,
                "start_time": time.time(),
                "skill_name": skill_name
            }

            # 刷新玩家属性
            player.recalculate_stats()

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]成功！敏捷增加 {effect_value} 点，持续 {duration} 秒")

            # 更新玩家状态显示
            self.update_player_stats(player)
        except Exception as e:
            logger.error(f"使用敏捷增益技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_magic_damage_skill(self, skill_result):
        """使用纯魔法伤害技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 计算玩家基础伤害
            player = self.game_manager.player
            base_damage, is_crit = player.calculate_damage()

            # 应用技能伤害倍率
            skill_damage = int(base_damage * effect_value)

            # 应用伤害
            monster.hp = max(0, monster.hp - skill_damage)

            # 添加战斗日志
            crit_text = "暴击!" if is_crit else ""
            self.game_manager.add_log(f"[{skill_name}]{crit_text} 对 {monster.name} 造成 {skill_damage} 点伤害!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用魔法伤害技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_true_damage_skill(self, skill_result):
        """使用真实伤害技能(无视防御)"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 直接应用技能伤害值（不考虑怪物防御）
            monster.hp = max(0, monster.hp - effect_value)

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]对 {monster.name} 造成 {effect_value} 点真实伤害!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用真实伤害技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_percentage_health_damage_skill(self, skill_result):
        """使用基于生命值百分比的伤害技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)  # 百分比值，例如10表示10%
            max_damage = skill_result.get("max_damage", 9999)  # 最大伤害限制

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 计算百分比伤害
            percent = effect_value / 100.0
            damage = int(monster.max_hp * percent)

            # 应用最大伤害限制
            damage = min(damage, max_damage)

            # 应用伤害
            monster.hp = max(0, monster.hp - damage)

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]对 {monster.name} 造成 {damage} 点伤害 ({effect_value}%生命值)!")

            # 移除死亡检查和 handle_monster_death 调用
            # if monster.hp <= 0:
            #     self.game_manager.handle_monster_death()

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用百分比生命值伤害技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_damage_reduction_skill(self, skill_result):
        """使用伤害减免技能（如魔法盾）"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 60)  # 默认持续60秒

            # 获取玩家
            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 找不到玩家")
                return

            # 创建或更新buff系统
            if not hasattr(player, "buffs"):
                player.buffs = {}

            # 添加伤害减免buff
            buff_id = f"damage_reduction_{int(time.time())}"
            player.buffs[buff_id] = {
                "type": "damage_reduction",
                "value": effect_value,
                "duration": duration,
                "start_time": time.time(),
                "skill_name": skill_name
            }

            # 刷新玩家属性
            player.recalculate_stats()

            # 添加战斗日志
            reduction_percent = int(effect_value * 100)
            self.game_manager.add_log(f"[{skill_name}]成功！伤害减免 {reduction_percent}%，持续 {duration} 秒")

            # 更新玩家状态显示
            self.update_player_stats(player)
        except Exception as e:
            logger.error(f"使用伤害减免技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _on_skill_right_click(self, skill_idx):
        """处理技能按钮右键点击，直接切换技能自动释放状态"""
        logger.info(f"右键点击技能槽位: {skill_idx}")

        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法操作技能槽：找不到玩家数据")
            return

        # 检查是否有技能可用
        if not hasattr(player, 'skills') or not player.skills:
            logger.warning("玩家没有学习任何技能")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 获取技能槽
            slot = self.skill_manager.get_slot(skill_idx)
            if not slot:
                logger.warning(f"技能槽 {skill_idx} 不存在")
                return

            # 如果技能槽为空，显示提示
            if slot.is_empty():
                logger.info(f"技能槽位{skill_idx}未绑定技能")
                self.ui_manager.show_message("技能槽", "这个技能槽是空的")
                return

            # 获取技能信息
            skill_id = slot.skill_id
            skill_config = GameConfig.get_skill(skill_id, player.character_class)
            skill_name = skill_config["name"] if skill_config else skill_id

            # 检查技能是否已禁用
            if not slot.enabled:
                self.ui_manager.show_message("技能已禁用", f"技能 [{skill_name}] 已禁用，无法设置自动释放")
                return

            # 切换技能自动释放状态
            auto_cast = self.skill_manager.toggle_auto_cast(skill_idx)

            # 更新技能按钮样式以反映自动释放状态
            self._update_skill_buttons()

            # 显示提示
            if auto_cast:
                self.ui_manager.show_message("自动释放", f"技能 [{skill_name}] 将自动释放")
            else:
                self.ui_manager.show_message("手动释放", f"技能 [{skill_name}] 需要手动释放")

            return

        # 以下是旧版本代码，用于兼容性保留
        # 获取该位置的技能
        skill_slots = getattr(self, "skill_slots", {})

        # 如果技能槽为空，显示提示
        if skill_idx not in skill_slots or not skill_slots[skill_idx]:
            logger.info(f"技能槽位{skill_idx}未绑定技能")
            self.ui_manager.show_message("技能槽", "这个技能槽是空的")
            return

        # 获取技能信息
        skill_id = skill_slots[skill_idx]
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        skill_name = skill_config["name"] if skill_config else skill_id

        # 检查技能是否已禁用
        is_disabled = hasattr(player, 'disabled_skills') and skill_id in player.disabled_skills
        if is_disabled:
            self.ui_manager.show_message("技能已禁用", f"技能 [{skill_name}] 已禁用，无法设置自动释放")
            return

        # 检查技能是否设置为自动释放
        is_auto_cast = False
        if hasattr(self, 'auto_cast_skills') and skill_idx in self.auto_cast_skills:
            is_auto_cast = True

        # 切换自动释放状态
        if is_auto_cast:
            # 取消自动释放
            if hasattr(self, 'auto_cast_skills') and skill_idx in self.auto_cast_skills:
                del self.auto_cast_skills[skill_idx]

            # 同步到玩家对象
            if hasattr(player, 'auto_cast_skills') and skill_idx in player.auto_cast_skills:
                del player.auto_cast_skills[skill_idx]

            self.ui_manager.show_message("手动释放", f"技能 [{skill_name}] 需要手动释放")
        else:
            # 设置为自动释放
            if not hasattr(self, 'auto_cast_skills'):
                self.auto_cast_skills = {}

            # 获取技能冷却时间
            cooldown = 0
            if skill_config:
                cooldown = skill_config.get("cooldown", 0)
                if isinstance(cooldown, list):
                    skill_level = player.skills.get(skill_id, 1)
                    if skill_level <= len(cooldown):
                        cooldown = cooldown[skill_level - 1]
                    else:
                        cooldown = cooldown[-1]

            # 添加到自动释放字典
            self.auto_cast_skills[skill_idx] = {
                "last_cast_time": 0,
                "cooldown": cooldown,
                "skill_id": skill_id
            }

            # 同步到玩家对象
            if not hasattr(player, 'auto_cast_skills'):
                player.auto_cast_skills = {}
            player.auto_cast_skills[skill_idx] = True

            self.ui_manager.show_message("自动释放", f"技能 [{skill_name}] 将自动释放")

        # 更新技能按钮
        self._update_skill_buttons()

    def _show_skill_selection_menu(self, skill_idx):
        """显示技能选择菜单

        参数:
            skill_idx: 技能槽位索引
        """
        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法显示技能选择菜单：找不到玩家数据")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 获取技能槽
            slot = self.skill_manager.get_slot(skill_idx)
            if not slot:
                logger.warning(f"技能槽 {skill_idx} 不存在")
                return

            # 如果是初始技能槽，不允许选择技能
            if slot.is_initial_slot():
                self.ui_manager.show_message("初始技能", "初始技能槽位不能手动添加技能")
                return

            # 获取所有可用的主动技能
            available_skills = self.skill_manager.get_available_skills()

            # 如果没有可用技能，显示提示
            if not available_skills:
                self.ui_manager.show_message("技能选择", "没有可用的主动技能")
                return

            # 创建技能选择菜单
            menu_dialog = self.ui_manager.create_dialog("skill_selection_menu", "选择技能", (300, 400))

            # 添加技能按钮
            y_offset = 20
            for skill_id, skill_config in available_skills:
                skill_name = skill_config["name"]
                skill_level = player.skills.get(skill_id, 0)

                # 创建技能按钮
                skill_button_rect = pygame.Rect(
                    menu_dialog.content_rect.x + 20,
                    menu_dialog.content_rect.y + y_offset,
                    260,
                    40
                )

                skill_button = self.ui_manager.create_button(
                    skill_button_rect,
                    f"{skill_name} Lv.{skill_level}",
                    lambda s_id=skill_id: self._on_select_skill(skill_idx, s_id, menu_dialog),
                    "chinese_normal"
                )

                menu_dialog.add_content(skill_button)
                y_offset += 50

            # 添加取消按钮
            cancel_button_rect = pygame.Rect(
                menu_dialog.content_rect.x + (menu_dialog.content_rect.width - 100) // 2,
                menu_dialog.content_rect.y + menu_dialog.content_rect.height - 60,
                100,
                40
            )

            cancel_button = self.ui_manager.create_button(
                cancel_button_rect,
                "取消",
                lambda: self.ui_manager.hide_dialog("skill_selection_menu"),
                "chinese_normal"
            )

            menu_dialog.add_content(cancel_button)

            return

        # 以下是旧版本代码，用于兼容性保留
        # 如果是0号位置（初始技能），不允许选择技能
        if skill_idx == 0:
            self.ui_manager.show_message("初始技能", "初始技能槽位不能手动添加技能")
            return

        # 获取所有已学习且未禁用的主动技能
        active_skills = []
        for skill_id, level in player.skills.items():
            if level <= 0 or skill_id in player.disabled_skills:
                continue

            try:
                skill_config = GameConfig.get_skill(skill_id, player.character_class)
                if skill_config and skill_config["type"] == "active":
                    # 检查技能是否已经在技能槽中
                    if skill_id not in self.skill_slots.values():
                        active_skills.append((skill_id, skill_config))
            except Exception as e:
                logger.error(f"获取技能配置失败: {e}")

        # 如果没有可用技能，显示提示
        if not active_skills:
            self.ui_manager.show_message("技能选择", "没有可用的主动技能")
            return

        # 创建技能选择菜单
        menu_dialog = self.ui_manager.create_dialog("skill_selection_menu", "选择技能", (300, 400))

        # 添加技能按钮
        y_offset = 20
        for skill_id, skill_config in active_skills:
            skill_name = skill_config["name"]
            skill_level = player.skills.get(skill_id, 0)

            # 创建技能按钮
            skill_button_rect = pygame.Rect(
                menu_dialog.content_rect.x + 20,
                menu_dialog.content_rect.y + y_offset,
                260,
                40
            )

            skill_button = self.ui_manager.create_button(
                skill_button_rect,
                f"{skill_name} Lv.{skill_level}",
                lambda s_id=skill_id: self._on_select_skill(skill_idx, s_id, menu_dialog),
                "chinese_normal"
            )

            menu_dialog.add_content(skill_button)
            y_offset += 50

        # 添加取消按钮
        cancel_button_rect = pygame.Rect(
            menu_dialog.content_rect.x + (menu_dialog.content_rect.width - 100) // 2,
            menu_dialog.content_rect.y + menu_dialog.content_rect.height - 60,
            100,
            40
        )

        cancel_button = self.ui_manager.create_button(
            cancel_button_rect,
            "取消",
            lambda: self.ui_manager.hide_dialog("skill_selection_menu"),
            "chinese_normal"
        )

        menu_dialog.add_content(cancel_button)

    def _on_select_skill(self, skill_idx, skill_id, menu_dialog=None):
        """处理技能选择

        参数:
            skill_idx: 技能槽位索引
            skill_id: 选择的技能ID
            menu_dialog: 菜单对话框，如果提供则关闭
        """
        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法选择技能：找不到玩家数据")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 获取技能槽
            slot = self.skill_manager.get_slot(skill_idx)
            if not slot:
                logger.warning(f"技能槽 {skill_idx} 不存在")
                return

            # 允许向初始技能槽添加技能
            if slot.is_initial_slot():
                logger.info(f"允许向初始技能槽添加技能: {skill_id}")

            # 获取初始技能ID
            initial_skill_id = self.skill_manager.get_initial_skill_id()

            # 允许将初始技能添加到非初始技能槽位
            if initial_skill_id and skill_id == initial_skill_id:
                logger.info(f"允许将初始技能 {skill_id} 添加到非初始技能槽位 {skill_idx}")

            # 获取技能配置
            skill_config = GameConfig.get_skill(skill_id, player.character_class)
            if not skill_config:
                logger.warning(f"无法获取技能配置: {skill_id}")
                return

            # 获取技能名称
            skill_name = skill_config["name"]

            # 将技能添加到技能槽
            self.skill_manager.set_skill(skill_idx, skill_id)

            # 更新技能按钮
            self._update_skill_buttons()

            # 添加日志
            self.game_manager.add_log(f"将技能 [{skill_name}] 添加到技能槽 {skill_idx}")
            logger.info(f"将技能 {skill_name} 添加到技能槽 {skill_idx}")

            # 如果提供了菜单对话框，关闭它
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_selection_menu")

            return

        # 以下是旧版本代码，用于兼容性保留
        # 如果是0号位置（初始技能），不允许手动添加技能
        if skill_idx == 0:
            self.ui_manager.show_message("初始技能", "初始技能槽位不能手动添加技能")
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_selection_menu")
            return

        # 获取初始技能ID
        initial_skill_id = None
        if player.character_class == "法师":
            initial_skill_id = "fireball"  # 火球术
        elif player.character_class == "牧师":
            initial_skill_id = "heal"  # 治愈术

        # 如果尝试将初始技能添加到非0号位置，显示提示并返回
        if initial_skill_id and skill_id == initial_skill_id:
            self.ui_manager.show_message("初始技能", f"初始技能 [{skill_id}] 只能放在0号位置")
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_selection_menu")
            return

        # 获取技能配置
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        if not skill_config:
            logger.warning(f"无法获取技能配置: {skill_id}")
            return

        # 获取技能名称
        skill_name = skill_config["name"]

        # 将技能添加到技能槽
        self.skill_slots[skill_idx] = skill_id

        # 同步到玩家对象
        if hasattr(player, 'skill_slots'):
            player.skill_slots[skill_idx] = skill_id
        else:
            player.skill_slots = {skill_idx: skill_id}

        # 添加到自动释放字典
        if hasattr(self, 'auto_cast_skills'):
            cooldown = skill_config.get("cooldown", 0)
            if isinstance(cooldown, list):
                skill_level = player.skills.get(skill_id, 1)
                if skill_level <= len(cooldown):
                    cooldown = cooldown[skill_level - 1]
                else:
                    cooldown = cooldown[-1]
            self.auto_cast_skills[skill_idx] = {
                "last_cast_time": 0,
                "cooldown": cooldown,
                "skill_id": skill_id  # 添加skill_id字段，确保知道这个槽位对应哪个技能
            }

        # 更新技能按钮
        self._update_skill_buttons()

        # 添加日志
        self.game_manager.add_log(f"将技能 [{skill_name}] 添加到技能槽 {skill_idx}")
        logger.info(f"将技能 {skill_name} 添加到技能槽 {skill_idx}")

        # 如果提供了菜单对话框，关闭它
        if menu_dialog:
            self.ui_manager.hide_dialog("skill_selection_menu")

    def _clear_skill_slot(self, skill_idx, menu_dialog=None):
        """清空技能槽位

        参数:
            skill_idx: 技能槽位索引
            menu_dialog: 菜单对话框，如果提供则关闭
        """
        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法清空技能槽：找不到玩家数据")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 获取技能槽
            slot = self.skill_manager.get_slot(skill_idx)
            if not slot:
                logger.warning(f"技能槽 {skill_idx} 不存在")
                return

            # 如果是初始技能槽，不允许清空
            if slot.is_initial_slot():
                self.ui_manager.show_message("初始技能", "初始技能槽位不能清空")
                if menu_dialog:
                    self.ui_manager.hide_dialog("skill_slot_menu")
                return

            # 如果技能槽为空，不需要清空
            if slot.is_empty():
                logger.warning(f"技能槽位{skill_idx}未绑定技能")
                return

            # 获取技能信息
            skill_id = slot.skill_id
            skill_config = GameConfig.get_skill(skill_id, player.character_class)
            skill_name = skill_config["name"] if skill_config else skill_id

            # 清空技能槽
            self.skill_manager.clear_slot(skill_idx)

            # 更新技能按钮
            self._update_skill_buttons()

            # 添加日志
            self.game_manager.add_log(f"已从技能槽 {skill_idx} 移除技能 [{skill_name}]")
            logger.info(f"已清空技能槽位 {skill_idx}: {skill_name}")

            # 如果提供了菜单对话框，关闭它
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_slot_menu")

            return

        # 以下是旧版本代码，用于兼容性保留
        # 如果是0号位置（初始技能），不允许清空
        if skill_idx == 0:
            self.ui_manager.show_message("初始技能", "初始技能槽位不能清空")
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_slot_menu")
            return

        # 获取该位置的技能
        skill_slots = getattr(self, "skill_slots", {})
        if skill_idx not in skill_slots or not skill_slots[skill_idx]:
            logger.warning(f"技能槽位{skill_idx}未绑定技能")
            return

        # 获取技能信息
        skill_id = skill_slots[skill_idx]
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        skill_name = skill_config["name"] if skill_config else skill_id

        # 获取初始技能ID
        initial_skill_id = None
        if player.character_class == "法师":
            initial_skill_id = "fireball"  # 火球术
        elif player.character_class == "牧师":
            initial_skill_id = "heal"  # 治愈术

        # 允许清空初始技能
        if initial_skill_id and skill_id == initial_skill_id:
            logger.info(f"允许清空初始技能 [{skill_name}]")

        # 从技能槽中移除技能
        del self.skill_slots[skill_idx]

        # 同步到玩家对象
        if hasattr(player, 'skill_slots') and skill_idx in player.skill_slots:
            del player.skill_slots[skill_idx]

        # 从自动释放字典中移除
        if hasattr(self, 'auto_cast_skills') and skill_idx in self.auto_cast_skills:
            del self.auto_cast_skills[skill_idx]

        # 更新技能按钮显示
        self._update_skill_buttons()

        # 添加日志
        self.game_manager.add_log(f"已从技能槽 {skill_idx} 移除技能 [{skill_name}]")
        logger.info(f"已清空技能槽位 {skill_idx}: {skill_name}")

        # 如果提供了菜单对话框，关闭它
        if menu_dialog:
            self.ui_manager.hide_dialog("skill_slot_menu")

    def _toggle_auto_cast(self, skill_idx, menu_dialog=None):
        """切换技能自动释放状态

        参数:
            skill_idx: 技能槽位索引
            menu_dialog: 菜单对话框，如果提供则关闭
        """
        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法切换自动释放状态：找不到玩家数据")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 切换自动释放状态
            auto_cast = self.skill_manager.toggle_auto_cast(skill_idx)
            if auto_cast is None:
                logger.warning(f"切换自动释放状态失败：槽位 {skill_idx} 不存在或没有技能")
                return

            # 获取技能信息
            slot = self.skill_manager.get_slot(skill_idx)
            skill_id = slot.skill_id
            skill_config = GameConfig.get_skill(skill_id, player.character_class)
            skill_name = skill_config["name"] if skill_config else skill_id

            # 添加日志
            if auto_cast:
                self.game_manager.add_log(f"设置技能 [{skill_name}] 为自动释放")
                logger.info(f"设置技能槽 {skill_idx} 为自动释放: {skill_name}")
            else:
                self.game_manager.add_log(f"取消自动释放技能 [{skill_name}]")
                logger.info(f"取消自动释放技能槽 {skill_idx}: {skill_name}")

            # 更新技能按钮
            self._update_skill_buttons()

            # 如果提供了菜单对话框，关闭它
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_slot_menu")

            return

        # 以下是旧版本代码，用于兼容性保留
        # 获取该位置的技能
        skill_slots = getattr(self, "skill_slots", {})
        if skill_idx not in skill_slots or not skill_slots[skill_idx]:
            logger.warning(f"技能槽位{skill_idx}未绑定技能")
            return

        # 获取技能信息
        skill_id = skill_slots[skill_idx]
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        skill_name = skill_config["name"] if skill_config else skill_id

        # 确保自动释放字典存在
        if not hasattr(self, 'auto_cast_skills'):
            self.auto_cast_skills = {}

        # 切换自动释放状态
        if skill_idx in self.auto_cast_skills:
            # 取消自动释放
            del self.auto_cast_skills[skill_idx]
            self.game_manager.add_log(f"取消自动释放技能 [{skill_name}]")
            logger.info(f"取消自动释放技能槽位 {skill_idx}: {skill_name}")
        else:
            # 启用自动释放
            # 获取技能冷却时间
            cooldown = 0
            try:
                skill_config = GameConfig.get_skill(skill_id, player.character_class)
                if skill_config:
                    cooldown = skill_config.get("cooldown", 0)
                    if isinstance(cooldown, list):
                        skill_level = player.skills.get(skill_id, 1)
                        if skill_level <= len(cooldown):
                            cooldown = cooldown[skill_level - 1]
                        else:
                            cooldown = cooldown[-1]
            except Exception as e:
                logger.error(f"获取技能冷却时间失败: {e}")

            # 检查是否有其他槽位已经设置了相同的技能自动释放
            for existing_slot, auto_cast_info in list(self.auto_cast_skills.items()):
                if existing_slot != skill_idx and auto_cast_info.get("skill_id") == skill_id:
                    logger.info(f"技能 {skill_id} 已在槽位 {existing_slot} 设置为自动释放，移除旧设置")
                    del self.auto_cast_skills[existing_slot]

            self.auto_cast_skills[skill_idx] = {
                "last_cast_time": 0,
                "cooldown": cooldown,
                "skill_id": skill_id  # 添加skill_id字段，确保知道这个槽位对应哪个技能
            }
            self.game_manager.add_log(f"设置技能 [{skill_name}] 为自动释放")

            # 处理日志中的槽位显示
            if skill_idx == "initial":
                slot_display = "初始"
            else:
                slot_display = str(skill_idx)

            logger.info(f"设置技能槽 {slot_display} 为自动释放: {skill_name}，冷却时间: {cooldown}秒")

        # 更新技能按钮显示
        self._update_skill_buttons()

        # 如果提供了菜单对话框，关闭它
        if menu_dialog:
            self.ui_manager.hide_dialog("skill_slot_menu")

    def _setup_initial_skill(self, player):
        """设置初始技能到专用槽位

        参数:
            player: 玩家对象
        """
        if not player:
            return

        # 根据职业设置初始技能
        initial_skill_id = None
        if player.character_class == "法师":
            initial_skill_id = "fireball"  # 火球术
        elif player.character_class == "牧师":
            initial_skill_id = "heal"  # 治愈术
        else:
            # 战士没有初始主动技能
            # 清除可能存在的初始技能槽
            if "initial" in self.skill_slots:  # 兼容旧版本
                del self.skill_slots["initial"]
            if 0 in self.skill_slots:
                del self.skill_slots[0]
            if hasattr(player, 'skill_slots'):
                if "initial" in player.skill_slots:  # 兼容旧版本
                    del player.skill_slots["initial"]
                if 0 in player.skill_slots:
                    del player.skill_slots[0]
            return

        # 检查初始技能是否存在且已学习
        if initial_skill_id:
            # 确保玩家学习了初始技能
            if initial_skill_id not in player.skills or player.skills[initial_skill_id] <= 0:
                # 自动学习初始技能
                player.skills[initial_skill_id] = 1
                logger.info(f"自动学习初始技能: {initial_skill_id}")

            # 不再自动启用初始技能，允许玩家禁用初始技能
            # 检查初始技能是否被禁用（仅用于日志记录）
            if hasattr(player, 'disabled_skills') and initial_skill_id in player.disabled_skills:
                logger.info(f"初始技能 {initial_skill_id} 当前处于禁用状态")

            # 设置初始技能到0号槽位
            self.skill_slots[0] = initial_skill_id

            # 同步到玩家对象
            if hasattr(player, 'skill_slots'):
                # 兼容旧版本：如果存在"initial"键，将其转换为0
                if "initial" in player.skill_slots:
                    player.skill_slots[0] = player.skill_slots["initial"]
                    del player.skill_slots["initial"]
                else:
                    player.skill_slots[0] = initial_skill_id
            else:
                player.skill_slots = {0: initial_skill_id}

            # 添加到自动释放字典
            if hasattr(self, 'auto_cast_skills'):
                # 获取技能冷却时间
                cooldown = 0
                try:
                    skill_config = GameConfig.get_skill(initial_skill_id, player.character_class)
                    if skill_config:
                        cooldown = skill_config.get("cooldown", 0)
                        if isinstance(cooldown, list):
                            skill_level = player.skills.get(initial_skill_id, 1)
                            if skill_level <= len(cooldown):
                                cooldown = cooldown[skill_level - 1]
                            else:
                                cooldown = cooldown[-1]
                except Exception as e:
                    logger.error(f"获取技能冷却时间失败: {e}")

                self.auto_cast_skills["initial"] = {
                    "last_cast_time": 0,
                    "cooldown": cooldown,
                    "skill_id": initial_skill_id  # 添加skill_id字段，确保知道这个槽位对应哪个技能
                }

            # 检查其他技能槽中是否有初始技能，如果有则移除
            for slot_idx in list(self.skill_slots.keys()):
                if slot_idx != "initial" and slot_idx != 0 and self.skill_slots[slot_idx] == initial_skill_id:
                    logger.info(f"从技能槽 {slot_idx} 移除初始技能 {initial_skill_id}，因为它只能放在初始技能槽位")
                    del self.skill_slots[slot_idx]
                    if hasattr(player, 'skill_slots') and slot_idx in player.skill_slots:
                        del player.skill_slots[slot_idx]

            # 确保初始技能不会与其他技能冲突
            # 例如，如果火球术是初始技能，确保它不会与雷电术等其他技能冲突
            if initial_skill_id:
                # 获取技能配置
                skill_config = GameConfig.get_skill(initial_skill_id, player.character_class)
                if skill_config:
                    # 记录初始技能的类型和效果类型，用于后续处理
                    logger.info(f"初始技能 {initial_skill_id} 类型: {skill_config.get('type')}, 效果: {skill_config.get('effects', [{}])[0].get('type')}")

                    # 确保初始技能在自动释放字典中有正确的条目
                    if hasattr(self, 'auto_cast_skills'):
                        if "initial" in self.auto_cast_skills:  # 兼容旧版本
                            # 将"initial"键转换为0
                            self.auto_cast_skills[0] = self.auto_cast_skills["initial"]
                            del self.auto_cast_skills["initial"]
                        if 0 in self.auto_cast_skills:
                            self.auto_cast_skills[0]["skill_id"] = initial_skill_id

            # 兼容旧版本：如果"initial"槽位有初始技能，移动到0号槽位
            if "initial" in self.skill_slots and self.skill_slots["initial"] == initial_skill_id:
                self.skill_slots[0] = self.skill_slots["initial"]
                del self.skill_slots["initial"]
                if hasattr(player, 'skill_slots') and "initial" in player.skill_slots:
                    player.skill_slots[0] = player.skill_slots["initial"]
                    del player.skill_slots["initial"]

            logger.info(f"设置初始技能 {initial_skill_id} 到0号槽位")

    def _use_dot_skill(self, skill_result):
        """使用持续伤害技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 3)  # 默认持续3回合

            # 获取目标怪物
            monster = self.game_manager.current_enemy
            if not monster:
                logger.warning(f"使用{skill_name}失败: 没有目标")
                return

            # 添加DOT效果
            dot_effect = {
                "type": "dot",
                "name": skill_name,
                "damage_per_turn": effect_value,
                "turns_left": duration
            }

            if not hasattr(monster, "effects"):
                monster.effects = []

            # 检查是否已存在同名效果，如果有则更新
            updated = False
            for i, effect in enumerate(monster.effects):
                if effect.get("type") == "dot" and effect.get("name") == skill_name:
                    monster.effects[i] = dot_effect
                    updated = True
                    break

            if not updated:
                monster.effects.append(dot_effect)

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]对 {monster.name} 施加了持续伤害效果，每回合受到 {effect_value} 点伤害，持续 {duration} 回合!")

            # 更新怪物状态显示
            self.update_monster_display(monster)
        except Exception as e:
            logger.error(f"使用持续伤害技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")

    def _use_buff_skill(self, skill_result):
        """使用增益技能"""
        try:
            skill_name = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            buff_type = skill_result.get("buff_type", "")
            effect_value = skill_result.get("effect_value", 0)
            duration = skill_result.get("duration", 3)  # 默认持续3回合

            player = self.game_manager.player
            if not player:
                logger.warning(f"使用{skill_name}失败: 玩家不存在")
                return

            # 添加buff效果
            buff_effect = {
                "type": "buff",
                "name": skill_name,
                "buff_type": buff_type,
                "effect_value": effect_value,
                "turns_left": duration
            }

            if not hasattr(player, "effects"):
                player.effects = []

            # 检查是否已存在同名效果，如果有则更新
            updated = False
            for i, effect in enumerate(player.effects):
                if effect.get("type") == "buff" and effect.get("name") == skill_name:
                    player.effects[i] = buff_effect
                    updated = True
                    break

            if not updated:
                player.effects.append(buff_effect)

            # 应用效果
            buff_description = ""
            if buff_type == "attack":
                buff_description = f"攻击力提高 {effect_value}%"
            elif buff_type == "defense":
                buff_description = f"防御力提高 {effect_value}%"
            elif buff_type == "speed":
                buff_description = f"速度提高 {effect_value}%"
            elif buff_type == "crit_rate":
                buff_description = f"暴击率提高 {effect_value}%"
            elif buff_type == "crit_damage":
                buff_description = f"暴击伤害提高 {effect_value}%"
            else:
                buff_description = f"{buff_type}提高 {effect_value}"

            # 添加战斗日志
            self.game_manager.add_log(f"[{skill_name}]施加了增益效果: {buff_description}，持续 {duration} 回合!")

            # 更新玩家状态显示
            self.update_player_stats()
        except Exception as e:
            logger.error(f"使用增益技能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 防御性地获取技能名称或ID用于日志
            name_for_log = "未知技能"
            if isinstance(skill_result, dict):
                name_for_log = skill_result.get("skill_name", skill_result.get("skill_id", "未知技能"))
            self.game_manager.add_log(f"[{name_for_log}]使用失败: 内部错误")


            import traceback
            logger.error(traceback.format_exc())

    def _update_global_cooldown_bar(self):
        """更新全局技能冷却条"""
        try:
            # 获取玩家和冷却条组件
            player = self.game_manager.player
            cooldown_bar = self.components_map.get("global_cooldown_bar")

            if not player or not cooldown_bar:
                return

            # 获取全局冷却信息
            if hasattr(player, 'get_global_cooldown_info'):
                cooldown_info = player.get_global_cooldown_info()

                # 更新冷却条进度
                progress = cooldown_info.get('progress', 1.0)
                cooldown_bar.set_value(progress)

                # 如果冷却已完成，使用绿色；否则使用蓝色
                if progress >= 1.0:
                    cooldown_bar.colors["fill"] = (100, 255, 100)  # 绿色表示可以使用技能
                else:
                    cooldown_bar.colors["fill"] = (100, 100, 255)  # 蓝色表示冷却中
            else:
                # 如果玩家没有全局冷却方法，默认显示为可用状态
                cooldown_bar.set_value(1.0)
                cooldown_bar.colors["fill"] = (100, 255, 100)

        except Exception as e:
            logger.error(f"更新全局技能冷却条时出错: {e}")

    def _refresh_ui_state(self):
        """根据当前游戏状态刷新所有相关的UI元素"""
        logger.debug("开始刷新 GameScreen UI 状态")
        try:
            player = self.game_manager.player
            current_map = self.game_manager.current_map

            # 1. 更新玩家状态显示
            if player:
                self.update_player_stats(player)
                self._update_inventory_indicator() # 更新背包指示器

            # 2. 更新怪物显示
            monster = self.game_manager.current_enemy or \
                      (self.game_manager.battle_system.monster if self.game_manager.battle_system else None)
            if monster and self.game_manager.in_battle:
                 self.update_monster_display(monster)
            else:
                 # 清空怪物显示或显示提示
                 self.update_monster_display(None)
                 if "hint" in self.monster_components:
                    self.monster_components["hint"].set_text("点击自动战斗按钮开始战斗")


            # 3. 更新技能按钮状态 (不重新初始化槽位)
            if player:
                self._update_skill_buttons()

            # 3.1 更新全局技能冷却条
            self._update_global_cooldown_bar()

            # 4. 更新区域标题
            if current_map and "area_title" in self.components_map:
                self.set_area_title(current_map)

            # 5. 更新副本倒计时
            self._update_dungeon_timer()

            # 6. 同步自动战斗按钮状态
            if "auto_battle_button" in self.components_map:
                self.is_auto_battle = self.game_manager.auto_battle
                button_text = "停止自动战斗" if self.is_auto_battle else "自动战斗"
                self.components_map["auto_battle_button"].set_text(button_text)
                logger.debug(f"同步自动战斗按钮状态: {button_text}")

        except Exception as e:
            logger.error(f"刷新 GameScreen UI 状态时出错: {e}", exc_info=True)

        logger.debug("完成刷新 GameScreen UI 状态")

    def _toggle_skill_enabled(self, skill_idx, menu_dialog=None):
        """切换技能启用状态

        参数:
            skill_idx: 技能槽位索引
            menu_dialog: 菜单对话框，如果提供则关闭
        """
        # 获取玩家
        player = self.game_manager.player
        if not player:
            logger.warning("无法切换技能启用状态：找不到玩家数据")
            return

        # 使用新的技能管理器
        if self.skill_manager:
            # 切换启用状态
            enabled = self.skill_manager.toggle_skill_enabled(skill_idx)
            if enabled is None:
                logger.warning(f"切换技能槽 {skill_idx} 的启用状态失败")
                return

            # 获取技能信息
            slot = self.skill_manager.get_slot(skill_idx)
            if slot and slot.skill_id:
                skill_id = slot.skill_id
                skill_config = GameConfig.get_skill(skill_id, player.character_class)
                skill_name = skill_config["name"] if skill_config else skill_id
            else:
                skill_name = f"技能槽 {skill_idx}"

            # 更新技能按钮
            self._update_skill_buttons()

            # 添加日志
            enabled_text = "启用" if enabled else "禁用"
            self.game_manager.add_log(f"已{enabled_text}技能 [{skill_name}]")
            logger.info(f"已{enabled_text}技能槽 {skill_idx} 中的技能")

            # 如果提供了菜单对话框，关闭它
            if menu_dialog:
                self.ui_manager.hide_dialog("skill_slot_menu")

            return
