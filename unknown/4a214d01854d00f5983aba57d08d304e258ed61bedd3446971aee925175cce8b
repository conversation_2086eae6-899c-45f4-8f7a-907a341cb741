# -*- coding: utf-8 -*-
import pygame
import logging
from ui.components import Panel, Button, Text, ScrollableList, UIComponent
from ui.ui_manager import Screen
from utils.resource_manager import resources
import os
import random

# 设置日志记录器
logger = logging.getLogger(__name__)

class InventoryScreen(Screen):
    """背包界面类"""

    def __init__(self, ui_manager, game_manager):
        """初始化背包界面"""
        super(InventoryScreen, self).__init__("inventory")  # 修复继承问题
        logger.info("初始化背包界面")

        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.title = "背包"

        # 背包中当前选中的物品
        self.selected_item = None

        # 背包物品的分页
        self.current_page = 0
        self.items_per_page = 36  # 每页显示的物品数量，6×6的布局

        # 物品名称显示的最大长度 - 增加到5个字符
        self.MAX_NAME_LENGTH = 5

        # 物品图片缓存
        self.item_images = {}

        # 初始化强制刷新标志
        self._force_refresh_flag = False

        # 创建界面元素
        self._create_ui_elements()
        logger.info("背包界面初始化完成")

    def _get_item_image(self, item):
        """获取物品图片 (重构版)

        参数:
            item: 物品数据字典

        返回:
            pygame.Surface或None: 缩放后的物品图片，如果找不到或加载失败则返回None
        """
        # 1. 输入验证
        if not item or not isinstance(item, dict):
            logger.warning("_get_item_image: Invalid item data provided.")
            return None

        item_name = item.get("name", "")
        if not item_name:
            logger.warning(f"_get_item_image: Item missing name: {item}")
            return None

        # 如果物品缺少type属性，尝试根据名称推断
        item_type = item.get("type", "")
        if not item_type:
            # 根据物品名称推断类型
            inferred_type = self._infer_item_type(item_name)
            if inferred_type:
                # 更新物品的type属性
                item["type"] = inferred_type
                item_type = inferred_type
                logger.info(f"为物品推断类型: {item_name}, 推断类型: {item_type}")
            else:
                logger.warning(f"_get_item_image: Item missing type and cannot infer: {item}")
                return None

        # 2. 检查缓存
        # 使用简单缓存键，可能需要根据具体情况扩展（例如，如果图标受品质影响）
        cache_key = f"{item_type}_{item_name}"
        if cache_key in self.item_images:
            # logger.debug(f"Cache hit for item image: {cache_key}")
            return self.item_images[cache_key]

        # 3. 确定基础图片路径
        image_path = None
        try:

            equipment_types_map = {
                "武器": "武器", "防具": "防具", "头盔": "头盔", "项链": "项链",
                "手镯": "手镯", "左手镯": "手镯", "右手镯": "手镯",
                "戒指": "戒指", "左戒指": "戒指", "右戒指": "戒指",
                "勋章": "勋章"
            }

            if item_type in equipment_types_map:
                equipment_dir = equipment_types_map[item_type]
                image_path = f"assets/images/equipment/{equipment_dir}/{item_name}.png"
            elif item_type in ["消耗品", "药品", "药剂", "药水", "任务道具"]: # 组合消耗品类
                # 主要尝试路径
                image_path = f"assets/images/equipment/消耗品/{item_name}.png"
                # 注意：这里只确定路径，实际加载在后面统一处理
            elif item_type == "技能书":
                # 技能书只有唯一路径
                image_path = "assets/images/equipment/技能书/技能书.png"
            else:
                logger.debug(f"未处理的物品类型，无法确定图片路径: {item_type}, 名称: {item_name}")
                # 对于未知类型，image_path 保持 None

        except Exception as e:
             logger.error(f"确定图片路径时出错 for item {item_name}: {e}", exc_info=True)
             return None # 出错则不继续

        # 4. 加载图片 (如果路径已确定)
        image = None
        if image_path:
            try:
                logger.debug(f"尝试加载图片: {image_path}")
                # --- 修改：使用导入的 resources ---
                image = resources.load_image(image_path)
                # --- 结束修改 ---
                if not image:
                    logger.warning(f"图片加载失败 (文件不存在或格式错误?): {image_path}")
                    # 可选：尝试加载该类型的默认图标，例如 assets/images/equipment/武器/default.png
                    # if item_type in equipment_types_map:
                    #     default_path = f"assets/images/equipment/{equipment_types_map[item_type]}/default.png"
                    #     image = resources.load_image(default_path)
                    #     if image:
                    #         logger.info(f"使用默认类型图标: {default_path}")
            except Exception as e:
                 logger.error(f"加载图片时发生异常: {image_path}, Error: {e}", exc_info=True)
                 image = None # 确保加载异常时 image 为 None

        # 5. 缩放和缓存
        scaled_image = None
        if image:
            try:
                # 获取物品槽的实际大小，如果没有定义则使用默认值
                slot_width = getattr(self, 'item_slot_width', 50)
                slot_height = getattr(self, 'item_slot_height', 50)

                # 留出边距，避免图片紧贴边缘
                content_width = slot_width - 6  # 左右各留3像素边距
                content_height = slot_height - 6  # 上下各留3像素边距

                # 获取原始图片尺寸
                img_width, img_height = image.get_size()

                # 计算缩放比例，保持原始宽高比
                scale_factor = min(content_width / img_width, content_height / img_height)
                new_width = int(img_width * scale_factor)
                new_height = int(img_height * scale_factor)

                # 缩放图片
                resized_image = pygame.transform.smoothscale(image, (new_width, new_height))

                # 创建一个透明的Surface，大小与物品槽内部区域相同
                final_image = pygame.Surface((content_width, content_height), pygame.SRCALPHA)

                # 计算居中位置
                x_offset = (content_width - new_width) // 2
                y_offset = (content_height - new_height) // 2

                # 将缩放后的图片绘制到透明Surface上的居中位置
                final_image.blit(resized_image, (x_offset, y_offset))

                # 设置最终图片
                scaled_image = final_image

                logger.debug(f"物品图片缩放成功: {item_name}, 原始尺寸: {img_width}x{img_height}, 新尺寸: {new_width}x{new_height}")
            except Exception as e:
                logger.error(f"缩放图片时出错 for {item_name}: {e}", exc_info=True)
                scaled_image = None # 缩放失败视为无图

        # 缓存结果 (scaled_image 或 None)
        self.item_images[cache_key] = scaled_image
        # logger.debug(f"缓存键 '{cache_key}' 的结果: {'Image' if scaled_image else 'None'}")

        # 6. 返回结果
        return scaled_image

    def _create_ui_elements(self):
        """创建界面元素"""
        logger.info("开始创建背包界面元素")
        screen_size = pygame.display.get_surface().get_size()

        # 顶部面板（标题）
        top_panel_height = 60
        top_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(top_panel)

        # 标题文本
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            self.title,
            "chinese_title",
            (220, 220, 220),
            "center"
        )
        self.add_component(title_text)

        # 返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 10, 100, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)

        # 金币显示 - 移至右上角并确保有足够空间显示大数额
        gold_rect = pygame.Rect(
            screen_size[0] - 350, 10, 300, 30  # 移至右上角，增加宽度
        )
        self.gold_text = self.ui_manager.create_text(
            gold_rect,
            "金币: 0",
            "chinese_normal",
            (255, 215, 0),  # 金黄色
            "left"  # 左对齐
        )
        self.add_component(self.gold_text)

        # 元宝显示 - 移至右上角并确保有足够空间显示大数额
        yuanbao_rect = pygame.Rect(
            screen_size[0] - 350, 35, 300, 30  # 移至右上角，增加宽度
        )
        self.yuanbao_text = self.ui_manager.create_text(
            yuanbao_rect,
            "元宝: 0",
            "chinese_normal",
            (0, 191, 255),  # 深天蓝色
            "left"  # 左对齐
        )
        self.add_component(self.yuanbao_text)

        # 主内容区域
        content_area = Panel(
            pygame.Rect(20, top_panel_height + 20, screen_size[0] - 40, screen_size[1] - top_panel_height - 80),
            color=(30, 30, 50),
            border_color=(50, 50, 70),
            border_width=1
        )
        self.add_component(content_area)
        self.content_area = content_area

        # 物品列表面板
        item_list_width = content_area.rect.width // 2 - 10
        item_list_panel = Panel(
            pygame.Rect(
                content_area.rect.left + 10,
                content_area.rect.top + 10,
                item_list_width,
                content_area.rect.height - 20
            ),
            color=(25, 25, 45),
            border_color=(45, 45, 65),
            border_width=1
        )
        self.add_component(item_list_panel)
        self.item_list_panel = item_list_panel

        # 物品详情面板
        item_detail_panel = Panel(
            pygame.Rect(
                item_list_panel.rect.right + 20,
                content_area.rect.top + 10,
                item_list_width,
                content_area.rect.height - 20
            ),
            color=(25, 25, 45),
            border_color=(45, 45, 65),
            border_width=1
        )
        self.add_component(item_detail_panel)
        self.item_detail_panel = item_detail_panel

        # 物品列表标题
        item_list_title = self.ui_manager.create_text(
            pygame.Rect(
                item_list_panel.rect.left,
                item_list_panel.rect.top - 30,
                item_list_panel.rect.width,
                30
            ),
            "物品列表",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(item_list_title)
        self.item_list_title = item_list_title

        # 物品详情标题
        item_detail_title = self.ui_manager.create_text(
            pygame.Rect(
                item_detail_panel.rect.left,
                item_detail_panel.rect.top - 30,
                item_detail_panel.rect.width,
                30
            ),
            "物品详情",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(item_detail_title)
        self.item_detail_title = item_detail_title

        # 创建物品列表项
        self.item_buttons = []
        self._create_item_slots()

        # 物品操作按钮
        # 减小按钮尺寸为原来的一半
        button_width = (item_detail_panel.rect.width - 60) // 2  # 减半宽度并留出间距
        button_height = 20  # 减半高度
        button_spacing = 10  # 按钮间距

        # 底部边距
        bottom_margin = 20

        # 计算底部起始位置
        bottom_y = item_detail_panel.rect.bottom - bottom_margin - button_height

        # 左侧按钮 x 坐标
        left_x = item_detail_panel.rect.left + 20
        # 右侧按钮 x 坐标
        right_x = left_x + button_width + button_spacing

        # 使用物品按钮 - 左上
        self.use_button = self.ui_manager.create_button(
            pygame.Rect(
                left_x,
                bottom_y - (button_height + button_spacing) * 2,  # 上排
                button_width,
                button_height
            ),
            "使用物品",
            self._on_use_item,
            "chinese_normal"
        )
        self.add_component(self.use_button)

        # 出售物品按钮 - 右上
        self.sell_button = self.ui_manager.create_button(
            pygame.Rect(
                right_x,
                bottom_y - (button_height + button_spacing) * 2,  # 上排
                button_width,
                button_height
            ),
            "出售物品",
            self._on_sell_item,
            "chinese_normal"
        )
        self.add_component(self.sell_button)

        # 锁定/解锁按钮 - 左中
        self.lock_button = self.ui_manager.create_button(
            pygame.Rect(
                left_x,
                bottom_y - button_height - button_spacing,  # 中排
                button_width,
                button_height
            ),
            "锁定物品",
            self._on_lock_item,
            "chinese_normal"
        )
        self.add_component(self.lock_button)

        # 添加一键出售未锁定装备按钮 - 右中
        self.sell_all_button = self.ui_manager.create_button(
            pygame.Rect(
                right_x,
                bottom_y - button_height - button_spacing,  # 中排
                button_width,
                button_height
            ),
            "一键出售",
            self._on_sell_all_unlocked,
            "chinese_normal"
        )
        self.add_component(self.sell_all_button)

        # 设置一键出售按钮的颜色，使其更加醒目
        self.sell_all_button.colors["normal"] = (150, 50, 50)  # 较深的红色
        self.sell_all_button.colors["hover"] = (180, 60, 60)   # 悬停时的红色
        self.sell_all_button.colors["pressed"] = (120, 40, 40) # 点击时的红色

        # 添加整理按钮 - 放在左侧，与上方按钮对齐
        sort_button_x = left_x  # 与左侧按钮对齐
        sort_button_y = bottom_y  # 底排
        self.sort_button = self.ui_manager.create_button(
            pygame.Rect(
                sort_button_x,
                sort_button_y,
                button_width,
                button_height
            ),
            "整理背包",
            self._on_sort_inventory,
            "chinese_normal"
        )
        self.add_component(self.sort_button)

        # 在物品详情面板添加全部卖出按钮 - 放在右侧，与上方按钮对齐
        sell_all_global_button_x = right_x  # 与右侧按钮对齐
        sell_all_global_button_y = bottom_y  # 底排
        self.sell_all_global_button = self.ui_manager.create_button(
            pygame.Rect(
                sell_all_global_button_x,
                sell_all_global_button_y,
                button_width,
                button_height
            ),
            "全部卖出",
            self._on_sell_all_global,
            "chinese_normal"
        )
        self.add_component(self.sell_all_global_button)

        # 设置全部卖出按钮的颜色，使其更加醒目
        self.sell_all_global_button.colors["normal"] = (180, 60, 60)  # 较深的红色
        self.sell_all_global_button.colors["hover"] = (220, 70, 70)   # 悬停时的红色
        self.sell_all_global_button.colors["pressed"] = (150, 50, 50) # 点击时的红色

        # 页码文本
        page_text_rect = pygame.Rect(
            item_list_panel.rect.centerx - 50,
            item_list_panel.rect.bottom + 10,
            100,
            30
        )
        self.page_text = self.ui_manager.create_text(
            page_text_rect,
            "第 1 / 1 页",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(self.page_text)

        # 上一页按钮
        prev_page_rect = pygame.Rect(
            page_text_rect.left - 100,
            page_text_rect.top,
            80,
            30
        )
        self.prev_page_button = self.ui_manager.create_button(
            prev_page_rect,
            "上一页",
            self._on_prev_page,
            "chinese_normal"
        )
        self.add_component(self.prev_page_button)

        # 下一页按钮
        next_page_rect = pygame.Rect(
            page_text_rect.right + 20,
            page_text_rect.top,
            80,
            30
        )
        self.next_page_button = self.ui_manager.create_button(
            next_page_rect,
            "下一页",
            self._on_next_page,
            "chinese_normal"
        )
        self.add_component(self.next_page_button)

        # 物品详情文本
        self.item_name = self.ui_manager.create_text(
            pygame.Rect(
                item_detail_panel.rect.left + 20,
                item_detail_panel.rect.top + 20,
                item_detail_panel.rect.width - 40,
                30
            ),
            "",
            "chinese_normal",
            (220, 220, 220),
            "center"
        )
        self.add_component(self.item_name)

        self.item_description = self.ui_manager.create_text(
            pygame.Rect(
                item_detail_panel.rect.left + 20,
                item_detail_panel.rect.top + 60,
                item_detail_panel.rect.width - 40,
                200
            ),
            "",
            "chinese_small",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.item_description)

        # 初始化刷新背包
        self.refresh_inventory()

        logger.info("背包界面元素创建完成")

    def _on_sell_item(self):
        """处理出售物品按钮点击"""
        logger.info("点击了出售物品按钮")
        if not self.selected_item:
            self.ui_manager.show_message("出售失败", "请先选择要出售的物品")
            return

        player = self.game_manager.player
        if not player:
            return

        item_name = self.selected_item.get("name", "未知物品")

        # 检查物品是否被锁定
        if self.selected_item.get("locked", False):
            self.ui_manager.show_message("无法出售", "该物品已被锁定，解锁后才能出售")
            logger.warning(f"尝试出售锁定的物品: {item_name}")
            return

        # 调用 Player 类的方法计算基础售价
        try:
            # 注意：这里假设 Player 类有 calculate_sell_price 方法
            base_sell_price = player.calculate_sell_price(self.selected_item)
        except Exception as e:
            logger.error(f"计算物品售价失败: {item_name}, 错误: {e}")
            self.ui_manager.show_message("出售失败", "计算物品售价时出错")
            return

        # 获取VIP加成
        vip_bonus = 1.0 # 默认为1.0，如果需要从player获取则修改
        if hasattr(player, 'get_vip_sell_bonus'):
             vip_bonus = player.get_vip_sell_bonus()
        final_sell_price = int(base_sell_price * vip_bonus)

        # 准备确认信息
        vip_level_text = ""
        if hasattr(player, 'vip_level'):
             vip_level_text = f"VIP{player.vip_level}"
        vip_text = f" ({vip_level_text}加成 x{vip_bonus:.1f})" if vip_bonus > 1.0 else ""
        confirmation_message = f"确定要出售 {item_name} 吗？\n售价: {final_sell_price} 金币{vip_text}"

        # 显示确认对话框，传递最终售价给确认函数
        self.ui_manager.show_confirmation(
            "出售物品",
            confirmation_message,
            lambda: self._confirm_sell_item(final_sell_price)
        )

    def _confirm_sell_item(self, final_sell_price):
        """确认出售物品"""
        if not self.selected_item:
            logger.warning("确认出售时物品未选中")
            return

        player = self.game_manager.player
        if not player:
            return

        item_to_sell = self.selected_item
        item_name = item_to_sell.get("name", "未知物品")

        # 再次检查锁定状态和物品是否存在于背包
        if item_to_sell.get("locked", False):
            self.ui_manager.show_message("出售失败", "该物品已被锁定")
            logger.warning(f"确认出售时发现物品 {item_name} 已被锁定")
            return

        if item_to_sell not in player.inventory:
            self.ui_manager.show_message("出售失败", "该物品已不在背包中")
            logger.warning(f"确认出售时发现物品 {item_name} 已不在背包中")
            self.selected_item = None
            self._clear_item_details()
            self.refresh_inventory()
            return

        # 增加金币
        player.gold += final_sell_price
        logger.info(f"物品 {item_name} 售出，获得 {final_sell_price} 金币. 当前金币: {player.gold}")

        # 从背包移除物品
        try:
            player.inventory.remove(item_to_sell)
            logger.info(f"已从背包移除售出的物品: {item_name}")
        except ValueError:
            logger.error(f"在确认出售后移除物品 {item_name} 失败，物品不在背包中？")
            # 即使移除失败，金币已经加上了，显示成功信息

        # 显示成功消息
        self.ui_manager.show_message("出售成功", f"成功出售 {item_name}，获得 {final_sell_price} 金币")

        # 清除选中状态并刷新
        self.selected_item = None
        self._clear_item_details()
        self.refresh_inventory()

    def _on_use_item(self):
        """处理使用物品按钮点击"""
        logger.info("点击了使用物品按钮")
        if not self.selected_item:
            return

        item_name = self.selected_item.get("name", "未知物品")
        item_type = self.selected_item.get("type", "")

        # 检查物品是否被锁定
        if self.selected_item.get("locked", False):
            self.ui_manager.show_message("无法使用", "该物品已被锁定，解锁后才能使用")
            logger.warning(f"尝试使用锁定的物品: {item_name}")
            return

        # 获取玩家对象
        player = self.game_manager.player
        if not player:
            return

        logger.info(f"正在使用物品: {item_name}, 类型: {item_type}")

        # 区分不同类型的物品，执行相应的动作
        if item_type == "消耗品":
            # 使用消耗品
            consumed = self._use_consumable(self.selected_item)

            # 只有当物品成功被使用时才处理数量变化
            if consumed:
                try:
                    # 获取物品当前数量
                    amount = self.selected_item.get("amount", 1)

                    # 如果数量大于1，减少数量
                    if amount > 1:
                        self.selected_item["amount"] = amount - 1
                        logger.info(f"物品数量减少: {item_name}, 剩余数量: {amount - 1}")
                        # 更新背包
                        self.refresh_inventory()
                    else:
                        # 如果数量为1，从背包中移除物品
                        player.inventory.remove(self.selected_item)
                        logger.info(f"已消耗物品: {item_name}")

                        # 更新背包
                        self.refresh_inventory()

                        # 清除选中
                        self.selected_item = None
                        self._clear_item_details()
                except ValueError:
                    logger.error(f"移除消耗品失败: 背包中未找到物品 {item_name}")
        elif item_type in ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "左手镯", "右手镯", "左戒指", "右戒指"]:
            # 装备物品
            logger.info(f"正在装备物品: {item_name}")
            # _equip_item方法内部已经处理了背包刷新和清除选中项，所以这里不需要重复操作
            self._equip_item(self.selected_item)
        else:
            logger.warning(f"未知物品类型无法使用: {item_type}")
            self.ui_manager.show_message("无法使用", f"物品类型 '{item_type}' 不可使用")

    def _on_back_click(self):
        """处理返回按钮点击"""
        logger.info("点击了返回按钮，返回游戏界面")

        # 确保角色属性已重新计算
        if self.game_manager.player:
            self.game_manager.player.recalculate_stats()
            logger.info("返回游戏界面前，强制重新计算玩家属性")

        # 返回游戏界面
        self.ui_manager.show_screen("game")

        # 如果能够访问游戏界面，则刷新玩家属性显示
        if "game" in self.ui_manager.screens:
            game_screen = self.ui_manager.screens["game"]
            if hasattr(game_screen, "update_player_stats") and self.game_manager.player:
                game_screen.update_player_stats(self.game_manager.player)
                logger.info("已在返回游戏界面时更新玩家属性显示")

    def _create_item_slots(self):
        """创建物品槽位"""
        logger.info("开始创建背包物品槽位")

        # 清除之前的物品槽位 (if they were added to the screen previously)
        # We also need to clear them from the panel if they were added there
        if hasattr(self, 'item_list_panel') and self.item_list_panel:
            self.item_list_panel.clear_components() # Clear previous buttons from panel
            logger.debug("Cleared existing components from item_list_panel")

        # Ensure item_buttons list is cleared
        self.item_buttons = []

        # 计算物品槽位的布局 - 改为6×6网格布局
        slot_width = 60  # 增大槽位宽度
        slot_height = 60  # 增大槽位高度
        slot_spacing = 10  # 调整间距
        slots_per_row = 6  # 每行显示6个物品
        rows = 6  # 6行

        # 保存物品槽尺寸，供图片缩放使用
        self.item_slot_width = slot_width
        self.item_slot_height = slot_height

        # 更新每页显示的物品数量
        self.items_per_page = slots_per_row * rows

        # 创建新的物品槽位
        for i in range(self.items_per_page):
            row = i // slots_per_row
            col = i % slots_per_row

            # Calculate position RELATIVE to the item_list_panel's top-left
            # The Panel will handle drawing them at the correct absolute position
            relative_x = 15 + col * (slot_width + slot_spacing)
            relative_y = 15 + row * (slot_height + slot_spacing)

            # 定义按钮颜色
            button_colors = {
                "normal": (20, 20, 40),
                "hover": (30, 30, 50),
                "pressed": (15, 15, 35),
                "text": (200, 200, 200),
                "border": (40, 40, 60)
            }

            item_slot = self.ui_manager.create_button(
                pygame.Rect(relative_x, relative_y, slot_width, slot_height), # Use relative coords
                "",
                lambda idx=i: self._on_item_click(idx),
                "chinese_normal"  # 使用普通大小字体以提高可读性
            )
            # 设置按钮颜色（通过更新colors字典）
            item_slot.colors.update(button_colors)
            # 设置文本换行和居中
            item_slot.text_wrap = True
            item_slot.text_align = "center"
            # 设置文本填充，减少内边距确保更多文本可见
            item_slot.padding = 2  # 减少内边距
            # 调整字体大小 - 降低字体大小以确保文本完全显示
            item_slot.font_size = 16
            # 添加图片属性
            item_slot.image = None

            # --- CRITICAL FIX: Add button to the panel, not the screen ---
            if hasattr(self, 'item_list_panel') and self.item_list_panel:
                self.item_list_panel.add_component(item_slot)
            else:
                logger.error("item_list_panel not initialized before _create_item_slots! Cannot add buttons.")
                # Fallback: add to screen (might cause positioning issues)
                # self.add_component(item_slot)
            # Keep track of buttons for easy access in refresh_inventory
            self.item_buttons.append(item_slot)

        logger.info(f"创建了 {len(self.item_buttons)} 个背包槽位并添加到 item_list_panel")

        # 检查玩家是否有背包数据
        player = self.game_manager.player
        if player and hasattr(player, "inventory"):
            inventory = player.inventory
            logger.info(f"玩家背包中有 {len(inventory)} 件物品，需要显示在槽位中")

    def _on_item_click(self, index):
        """处理物品点击"""
        actual_index = self.current_page * self.items_per_page + index
        player = self.game_manager.player

        if player and player.inventory and actual_index < len(player.inventory):
            self.selected_item = player.inventory[actual_index]

            if isinstance(self.selected_item, dict) and "name" in self.selected_item:
                item_name = self.selected_item.get("name", "未知物品")
                item_type = self.selected_item.get("type", "未知")
                logger.info(f"选择了物品: {item_name}, 类型: {item_type}, 索引: {actual_index}")

                # 根据物品类型做额外处理
                equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
                if item_type in equipment_types:
                    logger.info(f"选择了装备类物品: {item_name}, 准备显示详情")

                # 检查物品是否有必要的属性
                if not item_type:
                    logger.warning(f"物品 {item_name} 缺少类型信息")
            else:
                logger.warning(f"选择了无效的物品数据: {self.selected_item}")

            self._update_item_details(self.selected_item)  # 传递选中的物品
        else:
            self.selected_item = None
            self._clear_item_details()
            logger.warning(f"物品索引越界: {actual_index}, 当前背包物品数: {len(player.inventory) if player and player.inventory else 0}")

    def _use_consumable(self, item):
        """使用消耗品"""
        if not item or "type" not in item or item["type"] != "消耗品":
            logger.error(f"尝试使用非消耗品: {item}")
            return False

        name = item.get("name", "")
        effect = item.get("effect", "")
        player = self.game_manager.player

        if "effect" not in item:
            logger.error(f"消耗品 {name} 没有效果定义")
            return False

        # 处理祝福油
        if name == "祝福油":
            return self._use_blessing_oil(player)

        # 处理金锭、金条、金砖
        elif name == "金锭" or name == "金条" or name == "金砖":
            gold_value = 0
            if name == "金锭":
                gold_value = 100000
            elif name == "金条":
                gold_value = 1000000
            elif name == "金砖":
                gold_value = 10000000

            # 增加玩家金币
            player.gold += gold_value

            # 显示成功消息
            self.ui_manager.show_message("兑换成功", f"已将 {name} 兑换成 {gold_value} 金币")
            logger.info(f"使用 {name} 兑换成 {gold_value} 金币 (当前: {player.gold})")
            return True  # 消耗物品

        # 处理元宝类物品
        elif name == "一个元宝" or name == "十个元宝":
            yuanbao_value = 0
            if name == "一个元宝":
                yuanbao_value = 1
            elif name == "十个元宝":
                yuanbao_value = 10

            # 增加玩家元宝
            player.yuanbao += yuanbao_value

            # 显示成功消息
            self.ui_manager.show_message("兑换成功", f"已将 {name} 兑换成 {yuanbao_value} 元宝")
            logger.info(f"使用 {name} 兑换成 {yuanbao_value} 元宝 (当前: {player.yuanbao})")
            return True  # 消耗物品

        # 获取效果类型和值
        if isinstance(effect, dict):
            effect_type = effect.get("type", "")
            effect_value = effect.get("value", 0)
        else:
            effect_type = effect
            effect_value = item.get("effect_value", 0)

        # 处理商店刷新符
        if name == "商店刷新符":
            self.ui_manager.show_message("商店刷新符", "请在商店界面点击'立即刷新'按钮使用此道具")
            return False  # 不消耗物品

        # 处理生命药水
        elif effect_type == "heal":
            if player.hp >= player.max_hp:
                self.ui_manager.show_message("无法使用", "你的生命值已满")
                return False  # 不消耗物品

            # 恢复生命值
            old_hp = player.hp
            player.hp = min(player.max_hp, player.hp + effect_value)
            actual_heal = player.hp - old_hp

            self.ui_manager.show_message("使用成功", f"使用 {name} 恢复了 {actual_heal} 点生命值")
            logger.info(f"使用 {name} 恢复生命值 {actual_heal} (当前: {player.hp}/{player.max_hp})")
            return True  # 消耗物品

        # 处理魔法药水
        elif effect_type == "mana":
            if player.mp >= player.max_mp:
                self.ui_manager.show_message("无法使用", "你的魔法值已满")
                return False  # 不消耗物品

            # 恢复魔法值
            old_mp = player.mp
            player.mp = min(player.max_mp, player.mp + effect_value)
            actual_mana = player.mp - old_mp

            self.ui_manager.show_message("使用成功", f"使用 {name} 恢复了 {actual_mana} 点魔法值")
            logger.info(f"使用 {name} 恢复魔法值 {actual_mana} (当前: {player.mp}/{player.max_mp})")
            return True  # 消耗物品

        # 处理其他类型的消耗品（可以扩展）
        else:
            logger.warning(f"未知的消耗品效果类型: {effect_type}")
            return False  # 不消耗物品

    def _equip_item(self, item):
        """装备物品"""
        player = self.game_manager.player

        # 获取装备的类型
        item_type = item.get("type", "")
        if not item_type:
            self.ui_manager.show_message("装备失败", "该物品不是装备或缺少类型信息")
            logger.error(f"装备失败: 物品 {item.get('name', '未知')} 缺少类型信息")
            return

        # 检查等级要求 - 修复：使用level字段代替level_required
        level_required = item.get("level", 0)
        has_explicit_level_req = False

        # 以下情况视为有明确的等级要求：
        # 1. 存在明确的level_required字段
        explicit_level_req = item.get("level_required", 0)
        if explicit_level_req > 0:
            level_required = explicit_level_req
            has_explicit_level_req = True

        # 2. requirements字典中存在level要求
        if "requirements" in item and "level" in item["requirements"]:
            level_required = item["requirements"]["level"]
            has_explicit_level_req = True

        # 所有装备类型都使用相同处理逻辑：检查level是否为属性值
        if not has_explicit_level_req and level_required > 0:
            # 检查是否有与level相等的属性值
            for attr in ["attack", "defense", "magic_defense", "magic", "taoism"]:
                if attr in item and level_required == item[attr]:
                    logger.info(f"装备 {item.get('name', '未知')} 的{attr}属性值 {level_required} 被错误识别为等级要求，已修正")
                    level_required = 0
                    break

        # 只有存在明确的等级要求时才检查
        if has_explicit_level_req and level_required > player.level:
            self.ui_manager.show_message(
                "装备失败",
                f"需要等级 {level_required}，当前等级 {player.level}",
                None
            )
            logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要等级 {level_required}，玩家当前等级 {player.level}")
            return

        # 检查属性要求
        if "requirements" in item:
            for attr, value in item["requirements"].items():
                if attr == "level":
                    # 等级要求已经检查过
                    continue

                # 特殊处理：将strength要求视为attack要求
                if attr == "strength":
                    attr_name = self._get_attr_display_name("attack")
                    player_attr_value = getattr(player, "attack", 0)
                    if player_attr_value < value:
                        self.ui_manager.show_message(
                            "装备失败",
                            f"需要{attr_name} {value}，当前{attr_name} {player_attr_value}",
                            None
                        )
                        logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要{attr_name} {value}，玩家当前{attr_name} {player_attr_value}")
                        return
                    continue

                # 获取玩家对应属性的值
                player_attr_value = getattr(player, attr, 0)
                if player_attr_value < value:
                    # 获取属性的中文名称
                    attr_name = self._get_attr_display_name(attr)
                    self.ui_manager.show_message(
                        "装备失败",
                        f"需要{attr_name} {value}，当前{attr_name} {player_attr_value}",
                        None
                    )
                    logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要{attr_name} {value}，玩家当前{attr_name} {player_attr_value}")
                    return

        # 保留原有的属性检查逻辑以兼容旧数据
        # 检查攻击力要求
        attack_required = item.get("attack_required", 0)
        if attack_required > 0 and attack_required > player.attack:
            self.ui_manager.show_message(
                "装备失败",
                f"需要攻击力 {attack_required}，当前攻击力 {player.attack}",
                None
            )
            logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要攻击力 {attack_required}，玩家当前攻击力 {player.attack}")
            return

        # 检查魔法要求
        magic_required = item.get("magic_required", 0)
        if magic_required > 0:  # 只有当有魔法要求时才检查
            player_magic = player.magic
            if magic_required > player_magic:
                self.ui_manager.show_message(
                    "装备失败",
                    f"需要魔法 {magic_required}，当前魔法 {player_magic}",
                    None
                )
                logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要魔法 {magic_required}，玩家当前魔法 {player_magic}")
                return

        # 检查道术要求
        taoism_required = item.get("taoism_required", 0)
        if taoism_required > 0:  # 只有当有道术要求时才检查
            player_taoism = getattr(player, "taoism", 0)  # 安全获取玩家道术值
            if taoism_required > player_taoism:
                self.ui_manager.show_message(
                    "装备失败",
                    f"需要道术 {taoism_required}，当前道术 {player_taoism}",
                    None
                )
                logger.warning(f"装备失败: {item.get('name', '未知装备')} 需要道术 {taoism_required}，玩家当前道术 {player_taoism}")
                return

        # 检查性别限制
        item_name = item.get("name", "")
        if item_type == "防具" and ("(男)" in item_name or "(女)" in item_name):
            gender_requirement = "男" if "(男)" in item_name else "女"
            player_gender = player.gender

            if gender_requirement != player_gender:
                self.ui_manager.show_message(
                    "装备失败",
                    f"此装备只能由{gender_requirement}性角色穿戴",
                    None
                )
                logger.warning(f"装备失败: {item_name} 需要{gender_requirement}性角色，但玩家是{player_gender}性")
                return  # 只有当性别不匹配时才返回

        # 根据装备类型确定装备槽位
        slot_map = {
            "武器": "武器",
            "防具": "防具",
            "头盔": "头盔",
            "项链": "项链",
            "手镯": "左手镯",  # 手镯默认装备到左手
            "戒指": "左戒指",  # 戒指默认装备到左手
            "勋章": "勋章"
        }

        # 获取对应的槽位
        slot = slot_map.get(item_type, item_type)
        if not slot:
            self.ui_manager.show_message("装备失败", f"未知装备类型: {item_type}")
            logger.error(f"装备失败: 未知装备类型 {item_type}, 物品名称: {item_name}")
            return

        # 检查双持槽位（手镯和戒指可以左右手都装备）
        if item_type == "手镯" and player.equipment.get("左手镯"):
            # 如果左手已有手镯，尝试装备到右手
            if not player.equipment.get("右手镯"):
                slot = "右手镯"
            else:
                # 如果左右手都有手镯，可以选择替换左手的
                slot = "左手镯"
        elif item_type == "戒指" and player.equipment.get("左戒指"):
            # 如果左手已有戒指，尝试装备到右手
            if not player.equipment.get("右戒指"):
                slot = "右戒指"
            else:
                # 如果左右手都有戒指，可以选择替换左手的
                slot = "左戒指"
        elif item_type == "手镯":
            # 如果是手镯且左手没有装备，优先装备到左手
            slot = "左手镯"
        elif item_type == "戒指":
            # 如果是戒指且左手没有装备，优先装备到左手
            slot = "左戒指"

        logger.info(f"装备物品 '{item.get('name', '未知装备')}' 到槽位 '{slot}'")

        # 检查是否已有相同槽位的装备
        old_equipment = player.equipment.get(slot)

        # 装备新物品并将旧装备放回背包
        player.equipment[slot] = item

        try:
            player.inventory.remove(item)
            logger.info(f"已从背包移除物品: {item_name}")
        except ValueError:
            logger.error(f"移除物品失败: 背包中未找到物品 {item['name']}")
            # 尝试恢复原状态
            if old_equipment:
                player.equipment[slot] = old_equipment
                self.ui_manager.show_message("装备失败", "背包中未找到该物品")
                return

        if old_equipment:
            player.inventory.append(old_equipment)
            self.ui_manager.show_message("装备更换", f"成功装备 {item['name']}，将 {old_equipment['name']} 放回背包")
            logger.info(f"装备更换: 装备 {item['name']}，卸下 {old_equipment['name']}")
        else:
            self.ui_manager.show_message("装备成功", f"成功装备 {item['name']}")
            logger.info(f"装备物品: {item['name']}")

        # 更新玩家属性
        player.recalculate_stats()
        logger.info(f"已重新计算玩家属性")

        # 更新视图
        self.selected_item = None
        self._clear_item_details()

        # 强制刷新背包显示
        self.force_refresh()
        logger.info(f"已强制刷新背包显示")

    def _on_prev_page(self):
        """处理上一页按钮点击"""
        # 只要当前页大于0就允许翻到上一页
        if self.current_page > 0:
            self.current_page -= 1
            # 确保选中的物品被清除，避免在翻页后显示错误的物品详情
            self.selected_item = None
            self._clear_item_details()

            # 设置强制刷新标志
            self._force_refresh_flag = True

            # 使用强制刷新而不是普通刷新
            self.force_refresh()

            logger.info(f"切换到背包第 {self.current_page + 1} 页")

    def _on_next_page(self):
        """处理下一页按钮点击"""
        # 允许10页空白页
        max_empty_pages = 10
        # 计算实际总页数和最大允许页数
        item_pages = (len(self.game_manager.player.inventory) + self.items_per_page - 1) // self.items_per_page
        if item_pages == 0:
            item_pages = 1
        total_pages = max(item_pages, max_empty_pages)  # 允许空白页，最多10页

        logger.info(f"尝试翻到下一页: 当前页={self.current_page}, 总页数={total_pages}, 物品页数={item_pages}, 最大页数={max_empty_pages}")

        # 强制允许翻到下一页，只要不超过最大页数
        if self.current_page < total_pages - 1:  # 使用包含空白页的总页数判断
            old_page = self.current_page
            self.current_page += 1
            self.selected_item = None
            self._clear_item_details()

            # 确保current_page被更新 - 防止refresh_inventory重置它
            logger.info(f"成功翻到下一页: 从{old_page}到{self.current_page}")

            # 先设置下一页按钮为激活状态
            self.next_page_button.active = (self.current_page < total_pages - 1)
        else:
            logger.warning(f"无法翻到下一页，已达最大页数: 当前页={self.current_page}, 总页数={total_pages}")

        # 设置强制刷新标志
        self._force_refresh_flag = True

        # 记录刷新前的页码
        before_refresh = self.current_page

        # 使用强制刷新而不是普通刷新
        self.force_refresh()

        # 验证刷新后页码是否被意外改变
        if before_refresh != self.current_page:
            logger.warning(f"警告: 页码在刷新过程中被改变，从{before_refresh}变为{self.current_page}")

        logger.info(f"切换到背包第 {self.current_page + 1} 页")

    def _on_lock_item(self):
        """处理锁定/解锁物品按钮点击"""
        if not self.selected_item:
            logger.warning("锁定物品但未选中任何物品")
            self.ui_manager.show_message("无法锁定", "未选中任何物品")
            return

        # 切换物品锁定状态
        player = self.game_manager.player
        if player:
            is_locked = self.selected_item.get("locked", False)

            # 更新锁定状态
            self.selected_item["locked"] = not is_locked

            # 更新按钮文本
            if not is_locked:  # 如果原来未锁定，现在锁定了
                self.lock_button.set_text("解锁物品")
                logger.info(f"已锁定物品: {self.selected_item.get('name', '未知物品')}")
                self.ui_manager.show_message("锁定成功", "该物品已被锁定，无法被出售")
            else:  # 如果原来锁定，现在解锁了
                self.lock_button.set_text("锁定物品")
                logger.info(f"已解锁物品: {self.selected_item.get('name', '未知物品')}")
                self.ui_manager.show_message("解锁成功", "该物品已被解锁，可以正常出售")

        # 设置强制刷新标志
        self._force_refresh_flag = True

        # 使用强制刷新替代普通刷新
        self.force_refresh()

        # 更新物品详情
        self._update_item_details(self.selected_item)

    def _update_item_details(self, item=None):
        """更新物品详情显示"""
        if not item:
            self._clear_item_details()
            return

        # 检查物品是否有效
        if not isinstance(item, dict) or "name" not in item:
            logger.warning(f"尝试显示无效物品详情: {item}")
            self._clear_item_details()
            return

        # 更新锁定按钮状态和文本
        self.lock_button.enabled = True
        is_locked = item.get("locked", False)
        self.lock_button.set_text("解锁物品" if is_locked else "锁定物品")

        # 根据锁定状态更新出售按钮状态
        self.sell_button.enabled = not is_locked

        # 物品名称，根据品质设置颜色
        item_name = item.get("name", "未知物品")
        # 先尝试从quality属性读取，如果不存在则尝试从tier属性读取
        item_quality = item.get("quality", item.get("tier", "普通"))
        quality_colors = {
            "普通": (220, 220, 220),  # 提高基础物品的亮度
            "精良": (50, 255, 50),
            "稀有": (50, 180, 255),
            "史诗": (200, 50, 255),
            "传说": (255, 160, 50)
        }
        name_color = quality_colors.get(item_quality, (220, 220, 220))

        # 设置物品名称并应用颜色，确保名称完整显示
        self.item_name.set_text(item_name)
        self.item_name.set_color(name_color)

        # 定义物品锁定状态
        is_locked = item.get("locked", False)

        # 更新物品描述
        item_type = item.get("type", "未知类型")
        item_desc = item.get("description", "无描述")

        # Initialize description_text
        description_text = ""

        # 根据物品类型添加额外信息
        equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "左手镯", "右手镯", "左戒指", "右戒指"]

        if item_type in equipment_types:
            # 使用与装备界面相同的描述方法获取完整描述
            description_text = self._get_equipment_description(item)
            # 如果物品被锁定，在最前面添加锁定状态
            if is_locked:
                description_text = f"状态: 已锁定 🔒\n" + description_text
            logger.info(f"显示装备'{item_name}'详情 (使用装备界面格式), 类型: {item_type}")

        elif item_type == "消耗品":
            # 消耗品保持原有逻辑，但需要初始化 description_text
            description_text = f"类型: {item_type}\n"
            description_text += f"品质: {item_quality}\n"
            if is_locked:
                description_text += f"状态: 已锁定 🔒\n"

            effect = item.get("effect", {})
            effect_type = effect.get("type", "未知")
            effect_value = effect.get("value", 0)

            # 显示物品数量
            amount = item.get("amount", 1)
            if amount > 1:
                description_text += f"数量: {amount}\n"

            if effect_type == "heal":
                description_text += f"效果: 恢复 {effect_value} 点生命值\n"
            elif effect_type == "mana":
                description_text += f"效果: 恢复 {effect_value} 点魔法值\n"
            elif effect_type == "buff":
                buff_duration = effect.get("duration", 0)
                description_text += f"效果: 提升属性\n"
                description_text += f"持续时间: {buff_duration} 秒\n"
            # ... 可以添加其他消耗品效果描述 ...
            logger.info(f"显示消耗品'{item_name}'详情")

            # 附加物品描述（lore/flavor text）
            if item_desc:
                description_text += f"\n{item_desc}"
        else:
             # 其他未知类型物品的基础信息
             description_text = f"类型: {item_type}\n"
             description_text += f"品质: {item_quality}\n"
             if is_locked:
                 description_text += f"状态: 已锁定 🔒\n"
             # 附加物品描述（lore/flavor text）
             if item_desc:
                 description_text += f"\n{item_desc}"

        self.item_description.set_text(description_text)

        # 启用使用物品和出售物品按钮
        self.use_button.enabled = True
        self.sell_button.enabled = True

        # 根据物品类型调整使用按钮文本
        if item_type in equipment_types:
            self.use_button.set_text("装备")
        elif item_type == "消耗品":
            self.use_button.set_text("使用")
        else:
            self.use_button.set_text("使用")
            self.use_button.enabled = False

    def _clear_item_details(self):
        """清空物品详情显示"""
        self.item_name.set_text("")
        self.item_description.set_text("")
        self.use_button.enabled = False
        self.sell_button.enabled = False
        self.lock_button.enabled = False
        self.lock_button.set_text("锁定物品")  # 重置锁定按钮文本

        # 确保一键出售按钮始终可用
        self.sell_all_button.enabled = True

    def refresh_inventory(self):
        """刷新背包显示 (只更新现有按钮的内容)"""
        try:
            player = self.game_manager.player
            if not player:
                logger.warning("刷新背包失败：玩家数据不存在")
                return

            inventory = player.inventory if hasattr(player, "inventory") else []
            logger.info(f"refresh_inventory: 开始刷新，背包物品数量: {len(inventory)}, 当前页: {self.current_page + 1}") # Added log

            # --- 页码和有效性检查 ---
            # 计算总页数 (只计算包含物品的页数)
            item_pages = (len(inventory) + self.items_per_page - 1) // self.items_per_page
            total_pages = max(1, item_pages)  # 至少有1页

            # 验证并修正当前页码
            if self.current_page >= total_pages:
                old_page_log = self.current_page + 1 # For logging
                self.current_page = max(0, total_pages - 1)
                logger.info(f"页码超出范围，从 {old_page_log} 调整到第 {self.current_page + 1} 页")
            # ------------------------

            # 检查背包是否有变化 (如果不需要强制刷新)
            # Use the _force_refresh_flag set in __init__ and force_refresh
            inventory_changed = self._check_inventory_changed()
            logger.debug(f"refresh_inventory: inventory_changed={inventory_changed}, _force_refresh_flag={self._force_refresh_flag}") # Added log
            if not inventory_changed and not self._force_refresh_flag:
                logger.debug("背包物品未发生变化且非强制刷新，跳过部分刷新")
                # Still need to update page text and button states even if skipping item updates
                self.page_text.set_text(f"第 {self.current_page + 1} / {total_pages} 页")
                self.prev_page_button.active = self.current_page > 0
                self.next_page_button.active = self.current_page < total_pages - 1
                return

            # 重置强制刷新标志
            self._force_refresh_flag = False
            logger.debug("refresh_inventory: 重置强制刷新标志") # Added log

            # 记录背包中不同类型物品数量
            item_types_count = {}
            for item in inventory:
                if isinstance(item, dict):
                    item_type = item.get("type", "未知")
                    item_types_count[item_type] = item_types_count.get(item_type, 0) + 1
            logger.debug(f"背包物品类型统计: {item_types_count}")

            # 更新金币和元宝显示
            self.gold_text.set_text(f"金币: {player.gold}")
            self.yuanbao_text.set_text(f"元宝: {player.yuanbao}")

            # 计算当前页的物品起始索引
            start_index = self.current_page * self.items_per_page
            logger.debug(f"refresh_inventory: 处理索引范围从 {start_index} 开始，每页 {self.items_per_page} 个") # Added log

            # 更新物品按钮 (不再重新创建，只更新内容)
            items_displayed_on_page = 0 # Counter for logging
            for i, button in enumerate(self.item_buttons):
                # Reset button state for this refresh cycle
                button.visible = False
                button.image = None # 清除图片
                button.set_text("")
                button.active = False # Default to inactive
                button.dirty = True # Mark for redraw

                item_index = start_index + i
                # Check if this slot corresponds to an actual item
                if item_index < len(inventory):
                    item = inventory[item_index]
                    # Validate item data
                    if not item or not isinstance(item, dict) or "name" not in item:
                        logger.warning(f"背包物品索引 {item_index} 数据无效: {item}") # Changed log level
                        continue # Skip this slot

                    items_displayed_on_page += 1 # Increment counter
                    # --- Start: Item Display Logic (applied to existing button) ---
                    item_name = item.get("name", "未知物品")
                    display_name = item_name
                    item_type = item.get("type", "未知")
                    is_locked = item.get("locked", False)
                    quality = item.get("quality", "普通")
                    item_amount = item.get("amount", 1)
                    has_amount = item_amount > 1

                    logger.debug(f"显示物品[{item_index}]: {display_name}, 类型: {item_type}")
                    # Truncate name if necessary
                    if len(display_name) > self.MAX_NAME_LENGTH:
                        display_name = display_name[:self.MAX_NAME_LENGTH]
                    # Add lock prefix if locked
                    if is_locked:
                        display_name = f"*{display_name}"

                    # Attempt to load item image
                    item_image = self._get_item_image(item)

                    # Set button image or text
                    final_button_text = ""
                    if item_image:
                        button.image = item_image
                        # If image exists, show lock icon or amount, otherwise clear text
                        if is_locked:
                            final_button_text = "*" # Placeholder for lock indicator on image
                        elif has_amount:
                            final_button_text = f"x{item_amount}"
                        else:
                            final_button_text = ""
                    else:
                        # No image, display text
                        if has_amount:
                            # Display name and amount (consider newline if space allows)
                            final_button_text = f"{display_name}\\nx{item_amount}" # Example with newline
                        else:
                            final_button_text = display_name

                    button.set_text(final_button_text) # Set text after deciding image/text logic

                    logger.debug(f"设置按钮[{i}] (物品索引 {item_index}): Item='{item_name}', Text=\"{button.text}\", Image?={item_image is not None}, Visible=True, Active=True") # Updated log

                    # Make button visible and clickable
                    button.visible = True
                    button.active = True

                    # Reset button colors to default before applying quality/type colors
                    default_colors = {
                        "normal": (20, 20, 40),
                        "hover": (30, 30, 50),
                        "pressed": (15, 15, 35),
                        "text": (200, 200, 200),
                        "border": (40, 40, 60)
                    }
                    button.colors.update(default_colors)
                    button.border_width = 1 # Reset border width

                    # Apply specific colors based on quality/type/lock status
                    equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
                    consumable_types = ["消耗品"]

                    # Item type correction logic (keep as is)
                    if item_name and "项链" in item_name.lower() and item_type not in equipment_types:
                         if "type" in item:
                            if "项链" in item_name or "铃铛" in item_name or "珠" in item_name:
                                item["type"] = "项链"
                                item_type = "项链"
                                logger.info(f"修复项链类型: {item_name}")

                    # Equipment coloring
                    if item_type in equipment_types:
                        quality_border_colors = {
                            "普通": (160, 160, 200), "精良": (100, 200, 100), "稀有": (100, 140, 255),
                            "史诗": (200, 100, 255), "传说": (255, 165, 0)
                        }
                        base_color = (30, 30, 50)
                        border_color = quality_border_colors.get(quality, (160, 160, 200))
                        button.colors["normal"] = base_color
                        button.colors["border"] = border_color
                        button.border_width = 3 if is_locked else 2
                        button.colors["hover"] = tuple(min(c + 20, 255) for c in base_color)
                    # Consumable coloring
                    elif item_type in consumable_types:
                        button.colors["normal"] = (70, 40, 40)
                        if is_locked:
                            button.colors["border"] = (255, 215, 0)
                            button.border_width = 2
                        else:
                            button.colors["border"] = (140, 90, 90)
                            button.border_width = 1
                        button.colors["hover"] = (100, 60, 60)
                    # Other item coloring
                    else:
                        button.colors["normal"] = (50, 50, 50)
                        if is_locked:
                            button.colors["border"] = (255, 215, 0)
                            button.border_width = 2
                        else:
                             button.colors["border"] = (100, 100, 100) # Default border for others
                             button.border_width = 1
                    # --- End: Item Display Logic ---
                else:
                    # Log slots that are beyond inventory length (should be hidden)
                    logger.debug(f"设置按钮[{i}] (物品索引 {item_index}): 超出背包范围, Visible=False, Active=False") # Added log for empty slots

            # Log how many items were actually processed for display on this page
            logger.info(f"refresh_inventory: 本页处理并显示了 {items_displayed_on_page} 个物品按钮。") # Added log

            # Update page navigation text and buttons
            self.page_text.set_text(f"第 {self.current_page + 1} / {total_pages} 页")
            self.prev_page_button.active = self.current_page > 0
            self.next_page_button.active = self.current_page < total_pages - 1

            # Ensure nav elements are visible and marked for redraw
            self.page_text.visible = True
            self.prev_page_button.visible = True
            self.next_page_button.visible = True
            self.page_text.dirty = True
            self.prev_page_button.dirty = True
            self.next_page_button.dirty = True

            logger.debug(f"背包刷新完成: 共{len(inventory)}件物品，当前第{self.current_page + 1}/{total_pages}页")
        except Exception as e:
            logger.error(f"刷新背包时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def show(self):
        """显示背包界面"""
        logger.info(f"显示背包界面")
        super().show()

        # 重置当前页码
        self.current_page = 0
        logger.info(f"show: 重置当前页码为 {self.current_page + 1}") # Added log

        # 确保选中项为空
        self.selected_item = None
        self._clear_item_details()

        # 确保获取最新的玩家背包数据
        player = self.game_manager.player
        if player and hasattr(player, "inventory"):
            inventory_count = len(player.inventory)
            logger.info(f"show: 获取到玩家背包数据，物品数量: {inventory_count}") # Added log
            # 更新金币和元宝显示
            self.gold_text.set_text(f"金币: {player.gold}")
            self.yuanbao_text.set_text(f"元宝: {player.yuanbao}")

            # 记录背包中不同类型物品数量
            item_types_count = {}
            for item in player.inventory:
                if isinstance(item, dict):
                    item_type = item.get("type", "未知")
                    item_types_count[item_type] = item_types_count.get(item_type, 0) + 1
            logger.debug(f"背包物品类型统计: {item_types_count}")

            # 运行调试函数
            self._debug_item_display()

            # 对物品按类型分类并记录
            item_types = {}
            logger.info("开始分析背包物品:")
            for i, item in enumerate(player.inventory[:10]):  # 只展示前10个物品
                if isinstance(item, dict) and "name" in item:
                    item_type = item.get("type", "未知")
                    if item_type not in item_types:
                        item_types[item_type] = []
                    item_types[item_type].append(item["name"])
                    logger.info(f"  物品[{i}]: {item['name']}, 类型: {item_type}")
                else:
                    logger.info(f"  物品[{i}]: 无效物品格式 {type(item)}")

            # 统计各类型物品数量
            for item_type, items in item_types.items():
                logger.info(f"  类型[{item_type}]数量: {len(items)}, 物品: {', '.join(items)}")

            # 检查装备类物品是否存在
            equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章"]
            equipment_count = sum(len(items) for item_type, items in item_types.items() if item_type in equipment_types)
            if equipment_count > 0:
                logger.info(f"背包中存在 {equipment_count} 件装备类物品，确保它们能正确显示")
            else:
                logger.info("背包中没有装备类物品")
        else:
            logger.warning("show: 无法获取玩家背包数据") # Added log

        # 强制刷新背包界面
        logger.info("show: 调用 force_refresh() 来显示第一页") # Added log
        self.force_refresh()

        logger.info("背包界面显示完成")

    def update(self, dt: float):
        """更新背包界面"""
        super().update(dt)

        # 去掉额外的点击检测逻辑，使用标准按钮事件处理

        # 反作弊检查
        if hasattr(self, 'anti_cheat') and self.anti_cheat.enabled:
            cheat_detected = not self._run_anti_cheat_checks()
            if cheat_detected:
                self.handle_cheat_detection("速度异常或数据篡改")

    def draw(self, surface):
        """绘制背包界面"""
        super().draw(surface)

    def _check_inventory_changed(self):
        """检查背包是否有变化 (Simplified: just checks length)"""
        player = self.game_manager.player
        if not player or not hasattr(player, "inventory"):
            # Assume change if no player data or inventory attribute
            # Ensure last length is reset if player/inventory disappears
            if hasattr(self, '_last_inventory_length'):
                delattr(self, '_last_inventory_length')
            return True

        inventory = player.inventory
        inventory_length = len(inventory) if inventory else 0

        # Cache inventory length to detect changes
        # Initialize _last_inventory_length if it doesn't exist
        if not hasattr(self, '_last_inventory_length'):
            self._last_inventory_length = -1 # Initialize to a value that guarantees first check triggers change
        changed = self._last_inventory_length != inventory_length
        if changed:
             self._last_inventory_length = inventory_length
             logger.debug(f"Inventory length changed to {inventory_length}. Flagging change.")

        return changed

    def _debug_item_display(self):
        """调试物品名称显示问题"""
        player = self.game_manager.player
        if not player or not hasattr(player, "inventory"):
            logger.warning("无法进行物品显示调试：玩家数据不存在")
            return

        inventory = player.inventory
        if not inventory:
            logger.warning("无法进行物品显示调试：背包为空")
            return

        logger.info("===== 开始物品显示调试 =====")
        logger.info(f"背包物品总数: {len(inventory)}")

        # 检查物品名称和显示情况
        for i, item in enumerate(inventory[:10]):  # 只展示前10个物品
            if isinstance(item, dict) and "name" in item:
                item_name = item.get("name", "未知物品")
                item_type = item.get("type", "未知")

                # 计算需要显示的物品名称
                display_name = item_name
                if len(display_name) > self.MAX_NAME_LENGTH:
                    display_name = display_name[:self.MAX_NAME_LENGTH]

                logger.info(f"物品[{i}]: 原始名称=\"{item_name}\", 类型={item_type}, 显示名称=\"{display_name}\"")
            else:
                logger.info(f"物品[{i}]: 无效物品格式 {type(item)}")

        logger.info("===== 物品显示调试结束 =====")

    def force_refresh(self):
        """强制刷新背包，标记需要刷新并调用 refresh_inventory"""
        logger.info("强制刷新背包界面")
        try:
            # 设置强制刷新标志
            self._force_refresh_flag = True

            # DO NOT recreate item slots here - refresh_inventory handles updating existing ones
            # self._create_item_slots()

            # 清空缓存状态 (like inventory length check) - let refresh_inventory handle it
            # if hasattr(self, '_last_inventory_length'):
            #    delattr(self, '_last_inventory_length')

            # 调用刷新方法，它会检查标志并执行刷新
            self.refresh_inventory()

            logger.info("强制刷新背包标记完成，refresh_inventory将执行")
        except Exception as e:
            logger.error(f"强制刷新背包时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_sell_all_unlocked(self):
        """处理一键出售未锁定装备按钮点击"""
        player = self.game_manager.player
        if not player or not player.inventory:
            logger.warning("一键出售失败：玩家数据或背包不存在")
            self.ui_manager.show_message("出售失败", "背包数据不存在")
            return

        # 获取当前页面的物品
        start_index = self.current_page * self.items_per_page
        end_index = min(start_index + self.items_per_page, len(player.inventory))
        current_page_items = player.inventory[start_index:end_index]

        # 获取当前页面所有未锁定的装备、技能书和药品
        equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "左手镯", "右手镯", "左戒指", "右戒指"]
        sellable_types = equipment_types + ["技能书", "药品", "消耗品"]

        items_to_sell = [
            item for item in current_page_items
            if isinstance(item, dict)
            and item.get("type", "") in sellable_types
            and not item.get("locked", False)
        ]

        # 如果没有可出售的物品，显示提示并返回
        if not items_to_sell:
            logger.info("当前页面没有可出售的未锁定物品")
            self.ui_manager.show_message("出售提示", "当前页面没有可出售的未锁定物品")
            return

        # 获取VIP加成系数
        vip_bonus = player.get_vip_sell_bonus()

        # 分类统计物品数量
        equipment_count = sum(1 for item in items_to_sell if item.get("type", "") in equipment_types)
        skillbook_count = sum(1 for item in items_to_sell if "技能书" in item.get("type", ""))
        consumable_count = sum(1 for item in items_to_sell if item.get("type", "") in ["药品", "消耗品"])

        # 显示确认对话框
        item_types_text = []
        if equipment_count > 0:
            item_types_text.append(f"{equipment_count} 件装备")
        if skillbook_count > 0:
            item_types_text.append(f"{skillbook_count} 本技能书")
        if consumable_count > 0:
            item_types_text.append(f"{consumable_count} 个药品/消耗品")

        items_text = "、".join(item_types_text)
        total_count = len(items_to_sell)

        confirmation_message = f"确定要出售当前页面所有未锁定物品吗？"
        self.ui_manager.show_confirmation(
            "一键出售物品",
            confirmation_message,
            lambda: self._confirm_sell_all_unlocked(items_list=items_to_sell, vip_bonus=vip_bonus)
        )

    def _confirm_sell_all_unlocked(self, items_list, vip_bonus):
        """确认一键出售未锁定物品"""
        player = self.game_manager.player
        if not player:
            return

        total_sell_price = 0
        sold_items = []
        item_types = {"装备": 0, "技能书": 0, "药品/消耗品": 0}

        # 出售每件物品并计算总价
        for item in items_list:
            if item in player.inventory:  # 确保物品仍在背包中
                # 计算基础售价
                base_price = self._calculate_item_sell_price(item)

                # 应用VIP加成
                final_price = int(base_price * vip_bonus)
                total_sell_price += final_price

                # 记录已售物品名称
                sold_items.append(item["name"])

                # 分类统计
                item_type = item.get("type", "")
                if item_type in ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "左手镯", "右手镯", "左戒指", "右戒指"]:
                    item_types["装备"] += 1
                elif "技能书" in item_type:
                    item_types["技能书"] += 1
                else:
                    item_types["药品/消耗品"] += 1

                # 从背包中移除物品
                player.inventory.remove(item)

        # 增加金币
        player.gold += total_sell_price

        # 显示售出信息
        if sold_items:
            sold_items_text = "\n".join([f"- {name}" for name in sold_items[:10]])
            if len(sold_items) > 10:
                sold_items_text += f"\n...等共 {len(sold_items)} 件物品"

            # 创建物品类型统计信息
            type_summary = []
            for type_name, count in item_types.items():
                if count > 0:
                    type_summary.append(f"{type_name}: {count}件")
            type_summary_text = "，".join(type_summary)

            vip_text = "" if vip_bonus <= 1.0 else f"\nVIP{player.vip_level}加成: 售价 x{vip_bonus:.1f}"

            message = f"成功出售 {len(sold_items)} 件物品，获得 {total_sell_price} 金币{vip_text}\n\n{type_summary_text}\n"
            self.ui_manager.show_message("出售成功", message)
            logger.info(f"一键出售: 出售了 {len(sold_items)} 件物品，获得 {total_sell_price} 金币")
        else:
            self.ui_manager.show_message("出售失败", "没有物品被出售")
            logger.warning("一键出售: 没有物品被出售")

        # 清除选中的物品（如果它已被出售）
        if self.selected_item and self.selected_item in items_list:
            self.selected_item = None
            self._clear_item_details()

        # 更新背包显示
        self.refresh_inventory()

    def _on_sell_all_global(self):
        """处理全部卖出按钮点击（卖出整个背包的未锁定物品）"""
        player = self.game_manager.player
        if not player or not player.inventory:
            logger.warning("全部卖出失败：玩家数据或背包不存在")
            self.ui_manager.show_message("出售失败", "背包数据不存在")
            return

        # 获取整个背包中所有未锁定的可售出物品
        equipment_types = ["武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "左手镯", "右手镯", "左戒指", "右戒指"]
        sellable_types = equipment_types + ["技能书", "药品", "消耗品"]

        items_to_sell = [
            item for item in player.inventory
            if isinstance(item, dict)
            and item.get("type", "") in sellable_types
            and not item.get("locked", False)
        ]

        # 如果没有可出售的物品，显示提示并返回
        if not items_to_sell:
            logger.info("背包中没有可出售的未锁定物品")
            self.ui_manager.show_message("出售提示", "背包中没有可出售的未锁定物品")
            return

        # 获取VIP加成系数
        vip_bonus = player.get_vip_sell_bonus()

        # 分类统计物品数量
        equipment_count = sum(1 for item in items_to_sell if item.get("type", "") in equipment_types)
        skillbook_count = sum(1 for item in items_to_sell if "技能书" in item.get("type", ""))
        consumable_count = sum(1 for item in items_to_sell if item.get("type", "") in ["药品", "消耗品"])

        # 显示确认对话框
        item_types_text = []
        if equipment_count > 0:
            item_types_text.append(f"{equipment_count} 件装备")
        if skillbook_count > 0:
            item_types_text.append(f"{skillbook_count} 本技能书")
        if consumable_count > 0:
            item_types_text.append(f"{consumable_count} 个药品/消耗品")

        items_text = "、".join(item_types_text)
        total_count = len(items_to_sell)

        confirmation_message = f"确定要出售背包中所有未锁定物品吗？\n共 {total_count} 件物品，包括：\n{items_text}"
        self.ui_manager.show_confirmation(
            "全部卖出",
            confirmation_message,
            lambda: self._confirm_sell_all_unlocked(items_list=items_to_sell, vip_bonus=vip_bonus)
        )

    def _infer_item_type(self, item_name: str) -> str:
        """根据物品名称推断物品类型

        参数:
            item_name: 物品名称

        返回:
            str: 推断出的物品类型，如果无法推断则返回空字符串
        """
        type_patterns = {
            "武器": ["剑", "刀", "杖", "斧", "弓", "匕首", "锤", "枪"],
            "防具": ["盔甲", "战衣", "布衣", "袍", "护甲", "铠甲"],
            "项链": ["项链", "明珠", "珠子", "护符", "符"],
            "戒指": ["戒指", "指环"],
            "手镯": ["手镯", "护腕", "手套", "臂环"],
            "头盔": ["头盔", "帽子", "头饰", "冠"],
            "技能书": ["技能书", "秘籍", "法术书", "咒语书"],
            "消耗品": ["药水", "药", "丹", "符", "卷轴", "强化石", "祝福油", "战神油"]
        }

        for item_type, patterns in type_patterns.items():
            if any(pattern in item_name for pattern in patterns):
                return item_type

        # 特殊处理项链
        if "项链" in item_name:
            return "项链"

        return ""

    def _get_attr_display_name(self, attr):
        """获取属性的中文显示名称"""
        attr_names = {
            "attack": "攻击力",
            "magic": "魔法力",
            "taoism": "道术",
            "defense": "防御力",
            "magic_defense": "魔法防御",
            "strength": "攻击力",  # 将strength也显示为攻击力
            "agility": "敏捷",
            "vitality": "体力",
            "spirit": "精神"
        }
        return attr_names.get(attr, attr)

    def _on_sort_inventory(self):
        """处理整理背包按钮点击"""
        logger.info("开始整理背包物品")
        player = self.game_manager.player
        if not player or not player.inventory:
            logger.warning("整理背包失败：玩家数据或背包不存在")
            self.ui_manager.show_message("整理失败", "背包数据不存在")
            return

        # 设置强制刷新标志
        self._force_refresh_flag = True

        # 定义物品类型的排序顺序
        type_order = {
            "消耗品": 1,
            "特殊物品": 2,
            "勋章": 3,
            "武器": 4,
            "防具": 5,
            "头盔": 6,
            "手镯": 7,
            "戒指": 8,
            "项链": 9
        }

        # 定义品质的排序顺序
        quality_order = {
            "传说": 1,
            "史诗": 2,
            "稀有": 3,
            "精良": 4,
            "普通": 5
        }

        # 定义排序函数
        def sort_key(item):
            if not isinstance(item, dict):
                return (999, "", 999, "")  # 非字典类型的物品放最后

            item_type = item.get("type", "未知")
            item_name = item.get("name", "")
            # 获取物品品质，先尝试从quality属性读取，如果不存在则尝试从tier属性读取
            item_quality = item.get("quality", item.get("tier", "普通"))

            # 获取物品类型的排序顺序，未知类型排在最后
            type_priority = type_order.get(item_type, 100)

            # 获取品质的排序顺序，未知品质排在最后
            quality_priority = quality_order.get(item_quality, 999)

            # 返回排序键：(类型优先级, 名称, 品质优先级, 品质名称)
            # 这样会先按类型排序，然后同类型的按名称排序，同名物品再按品质排序（品质高的排前面）
            return (type_priority, item_name, quality_priority, item_quality)

        # 进行排序
        try:
            # 保存当前选中的物品，以便排序后能找到它
            selected_item_name = self.selected_item.get("name", "") if self.selected_item else ""

            # 对背包物品进行排序
            player.inventory.sort(key=sort_key)

            # 如果之前有选中的物品，尝试在排序后找到它
            if selected_item_name:
                # 查找名称相同的物品
                for i, item in enumerate(player.inventory):
                    if isinstance(item, dict) and item.get("name", "") == selected_item_name:
                        # 计算物品在当前页面的位置
                        page_index = i // self.items_per_page
                        # 如果物品在不同的页面，切换到该页面
                        if page_index != self.current_page:
                            self.current_page = page_index
                        # 更新选中的物品
                        self.selected_item = item
                        # 更新物品详情
                        self._update_item_details(item)
                        break
                else:
                    # 如果找不到原来选中的物品，清除选中状态
                    self.selected_item = None
                    self._clear_item_details()

            # 使用强制刷新而不是普通刷新，确保界面立即更新
            self.force_refresh()

            # 添加额外步骤确保物品按钮状态被正确更新
            for button in self.item_buttons:
                button.dirty = True  # 强制重绘

            # 确保背包检查状态被重置，下次更新时可以正确检测变化
            if hasattr(self, '_last_inventory_length'):
                delattr(self, '_last_inventory_length')

            logger.info("背包物品整理完成")
            self.ui_manager.show_message("整理完成", "已按物品类型和品质整理背包")
        except Exception as e:
            logger.error(f"整理背包时出错: {e}")
            self.ui_manager.show_message("整理失败", f"发生错误: {str(e)}")

    def _get_equipment_description(self, equipment):
        """生成装备描述文本 - 与装备界面一致"""
        if not equipment:
            return ""

        # 初始化描述文本
        description = f"{equipment.get('name', '未知装备')}"

        # 显示装备类型
        eq_type = equipment.get('type', '')
        if eq_type:
            description += f" ({eq_type})"

        # 添加等级需求
        if "level" in equipment:
            description += f"\n等级需求: {equipment['level']}"

        # 添加职业需求
        if "class" in equipment:
            description += f"\n职业需求: {equipment['class']}"

        # 添加其他需求
        if "requirements" in equipment:
            description += "\n需求:"
            for req, value in equipment["requirements"].items():
                req_name = {
                    "level": "等级",
                    "attack": "攻击",
                    "magic": "魔法",
                    "taoism": "道术"
                }.get(req, req)

                # 获取玩家的对应属性值
                player_value = 0
                if self.game_manager.player:
                    if req == "level":
                        player_value = self.game_manager.player.level
                    else:
                        player_value = getattr(self.game_manager.player, req, 0)

                # 如果玩家属性不满足需求，显示红色
                if player_value < value:
                    description += f"\n- {req_name}: {player_value}/{value} ❌"
                else:
                    description += f"\n- {req_name}: {player_value}/{value} ✓"

        # 添加基础属性
        description += "\n\n基础属性:"
        # 同时检查 quality_bonus 和 tier_bonus 属性，优先使用 quality_bonus
        quality_bonus = equipment.get("quality_bonus", equipment.get("tier_bonus", {})) # 获取品质加成字典，默认为空

        # 攻击力
        if "attack" in equipment:
            attack = equipment["attack"]
            bonus = quality_bonus.get("attack", 0)
            if isinstance(attack, list) and len(attack) >= 2:
                # 处理范围攻击
                min_attack = attack[0] + bonus
                max_attack = attack[1] + bonus
                description += f"\n- 攻击: {min_attack}~{max_attack}"
                if bonus > 0:
                    description += f" (+{bonus})" # 可选：显示加成值
            else:
                # 处理单个攻击值
                total_attack = attack + bonus
                description += f"\n- 攻击: {total_attack}"
                if bonus > 0:
                    description += f" (+{bonus})" # 可选：显示加成值

        # 防御力
        if "defense" in equipment:
            defense = equipment["defense"]
            bonus = quality_bonus.get("defense", 0)
            if isinstance(defense, list) and len(defense) >= 2:
                min_defense = defense[0] + bonus
                max_defense = defense[1] + bonus
                description += f"\n- 防御: {min_defense}~{max_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_defense = defense + bonus
                description += f"\n- 防御: {total_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 魔法
        if "magic" in equipment:
            magic = equipment["magic"]
            bonus = quality_bonus.get("magic", 0)
            if isinstance(magic, list) and len(magic) >= 2:
                min_magic = magic[0] + bonus
                max_magic = magic[1] + bonus
                description += f"\n- 魔法: {min_magic}~{max_magic}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_magic = magic + bonus
                description += f"\n- 魔法: {total_magic}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 道术
        if "taoism" in equipment:
            taoism = equipment["taoism"]
            bonus = quality_bonus.get("taoism", 0)
            if isinstance(taoism, list) and len(taoism) >= 2:
                min_taoism = taoism[0] + bonus
                max_taoism = taoism[1] + bonus
                description += f"\n- 道术: {min_taoism}~{max_taoism}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_taoism = taoism + bonus
                description += f"\n- 道术: {total_taoism}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 魔法防御
        if "magic_defense" in equipment:
            magic_defense = equipment["magic_defense"]
            bonus = quality_bonus.get("magic_defense", 0)
            if isinstance(magic_defense, list) and len(magic_defense) >= 2:
                min_magic_defense = magic_defense[0] + bonus
                max_magic_defense = magic_defense[1] + bonus
                description += f"\n- 魔防: {min_magic_defense}~{max_magic_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_magic_defense = magic_defense + bonus
                description += f"\n- 魔防: {total_magic_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 准确度 (品质通常不加成这些)
        if "accuracy" in equipment:
            description += f"\n- 准确: +{equipment['accuracy']}"

        # 敏捷 (品质通常不加成这些)
        if "agility" in equipment:
            description += f"\n- 敏捷: +{equipment['agility']}"

        # 幸运 (品质通常不加成这些)
        if "luck" in equipment:
            if isinstance(equipment["luck"], str) and equipment["luck"] == "random":
                # 按指定概率生成0-2之间的随机幸运值
                # 0的概率为85%，1的概率为14%，2的概率为1%
                luck_values = [0, 1, 2]
                luck_weights = [85, 14, 1]
                random_luck = random.choices(luck_values, weights=luck_weights, k=1)[0]
                description += f"\n- 幸运: +{random_luck}"
            elif equipment["luck"] > 0:  # 只有当幸运值大于0时才显示
                description += f"\n- 幸运: +{equipment['luck']}"

        # 添加攻击速度 (品质通常不加成这些)
        if "attack_speed" in equipment:
            description += f"\n- 攻速: +{int(equipment['attack_speed']*100)}%"

        # 吸血属性 (品质通常不加成这些)
        if "lifesteal" in equipment:
            description += f"\n- 吸血: {equipment['lifesteal']}%"

        # 固定值吸血属性 (品质通常不加成这些)
        if "flat_lifesteal" in equipment:
            description += f"\n- 固定吸血: {equipment['flat_lifesteal']}点"

        # 魔法闪避 (品质通常不加成这些)
        if "magic_avoidance" in equipment:
            description += f"\n- 魔法闪避: +{equipment['magic_avoidance']}%"

        # 装备爆率加成 (品质通常不加成这些)
        if "drop_rate_bonus" in equipment:
            description += f"\n- 爆率加成: +{int(equipment['drop_rate_bonus']*100)}%"

        # 添加特殊效果
        if "special" in equipment:
            special = equipment.get("special", "")
            special_desc = ""

            if special == "暴击" and "crit_damage" in equipment:
                try:
                    crit_damage = equipment.get("crit_damage", 0)
                    special_desc = f"暴击伤害增加 {int(crit_damage*100)}%"
                except (TypeError, ValueError):
                    special_desc = "暴击伤害增加"
            elif special == "攻速" and "attack_speed" in equipment:
                try:
                    attack_speed = equipment.get("attack_speed", 0)
                    special_desc = f"攻击速度提高 {int(attack_speed*100)}%"
                except (TypeError, ValueError):
                    special_desc = "攻击速度提高"
            elif special == "生命偷取" and "life_steal" in equipment:
                special_desc = f"攻击时偷取目标 {int(equipment['life_steal']*100)}% 生命值"
            elif special == "吸血" and "lifesteal" in equipment:
                special_desc = f"攻击时吸取目标 {equipment['lifesteal']}% 生命值"
            elif special == "固定吸血" and "flat_lifesteal" in equipment:
                special_desc = f"攻击时固定吸取 {equipment['flat_lifesteal']} 点生命值"
            elif special == "魔法穿透" and "magic_penetration" in equipment:
                special_desc = f"无视目标 {int(equipment['magic_penetration']*100)}% 魔法防御"
            elif special == "麻痹" and "description" in equipment:
                special_desc = equipment["description"]

            if special_desc:
                description += f"\n\n特殊效果: {special_desc}"

        # 添加装备描述文本
        if "description" in equipment:
            description += f"\n\n{equipment['description']}"

        return description

    def _use_blessing_oil(self, player):
        """使用祝福油

        提升玩家当前武器的幸运值，成功率和降级率根据当前幸运值变化：
        - 从0到1点：100%成功，0%降级
        - 从1到2点：50%成功，20%降级，30%不变
        - 从2到3点：30%成功，30%降级，40%不变
        - 从3到4点：20%成功，40%降级，40%不变
        - 从4到5点：10%成功，40%降级，50%不变
        - 从5到6点：10%成功，40%降级，50%不变
        - 从6到7点：10%成功，40%降级，50%不变

        最高可以提升到7点幸运值

        返回：
            bool: 是否成功消耗物品
        """
        # 检查玩家是否装备了武器
        weapon = player.equipment.get("武器")
        if not weapon:
            self.ui_manager.show_message("无法使用", "你需要先装备一把武器才能使用祝福油")
            logger.warning("尝试使用祝福油，但玩家未装备武器")
            return False

        # 获取当前武器的幸运值
        current_luck = weapon.get("luck", 0)
        if isinstance(current_luck, str) and current_luck == "random":
            # 如果是随机幸运值，则生成一个实际值
            import random
            luck_values = [0, 1, 2]
            luck_weights = [85, 14, 1]
            current_luck = random.choices(luck_values, weights=luck_weights, k=1)[0]
            weapon["luck"] = current_luck  # 将随机值转换为固定值

        # 检查是否已达到最大幸运值
        if current_luck >= 7:
            self.ui_manager.show_message("无法使用", "武器幸运值已达到最大值")
            logger.info(f"无法使用祝福油：武器 {weapon.get('name', '未知')} 幸运值已是最大值")
            return False

        # 根据当前幸运值确定成功率和降级率
        success_rates = [100, 50, 30, 20, 10, 10, 10]
        downgrade_rates = [0, 20, 30, 40, 40, 40, 40]

        success_rate = success_rates[current_luck]
        downgrade_rate = downgrade_rates[current_luck]

        # 随机决定结果
        import random
        roll = random.randint(1, 100)

        result_text = ""
        if roll <= success_rate:
            # 成功提升幸运值
            weapon["luck"] = current_luck + 1
            result_text = f"成功！武器幸运值 +1 (当前: {weapon['luck']})"
            logger.info(f"祝福油使用成功：{weapon.get('name', '未知')} 幸运值从 {current_luck} 提升到 {weapon['luck']}")
        elif roll <= success_rate + downgrade_rate:
            # 幸运值降低
            weapon["luck"] = current_luck - 1
            result_text = f"失败！武器幸运值 -1 (当前: {weapon['luck']})"
            logger.info(f"祝福油使用失败导致降级：{weapon.get('name', '未知')} 幸运值从 {current_luck} 降低到 {weapon['luck']}")
        else:
            # 幸运值不变
            result_text = f"没有变化，武器幸运值保持 {current_luck}"
            logger.info(f"祝福油使用后无变化：{weapon.get('name', '未知')} 幸运值保持 {current_luck}")

        # 显示结果并重新计算玩家属性
        self.ui_manager.show_message("使用祝福油", result_text)
        player.recalculate_stats()

        # 刷新装备界面（如果打开）
        if "equipment" in self.ui_manager.screens:
            equipment_screen = self.ui_manager.screens["equipment"]
            if hasattr(equipment_screen, "_refresh_equipment_view"):
                equipment_screen._refresh_equipment_view()

        return True  # 消耗物品

    def _calculate_item_sell_price(self, item):
        """计算物品售价（包装Player.calculate_sell_price方法）

        Args:
            item: 要计算售价的物品

        Returns:
            int: 计算出的物品基础售价
        """
        player = self.game_manager.player
        if not player:
            # 如果无法获取玩家对象，返回默认价格
            logger.warning("计算物品售价时无法获取玩家对象，使用默认价格")
            return 50

        try:
            # 调用Player类中的售价计算方法
            return player.calculate_sell_price(item)
        except Exception as e:
            # 捕获可能的错误，确保不会崩溃
            logger.error(f"调用物品售价计算方法失败: {e}")
            # 返回一个默认值
            return 50