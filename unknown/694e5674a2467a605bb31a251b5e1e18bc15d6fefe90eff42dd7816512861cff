import json
import random
import time
import pygame
from pathlib import Path
from datetime import datetime, timedelta
from ui.components import Panel, Button, Text, UIComponent
from ui.ui_manager import Screen
import traceback
from collections import defaultdict
import logging
# 设置日志记录器
logger = logging.getLogger(__name__)

class ShopScreen(Screen):
    """商店界面类"""

    def __init__(self, ui_manager, game_manager):
        """初始化商店界面"""


        super().__init__("shop")
        logger.info("初始化商店界面")

        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.title = "商店"

        # 商店中当前选中的物品
        self.selected_item = None
        self.selected_item_type = None

        # 标记是否首次访问商店
        self.first_visit = self._load_first_visit_flag()

        # 添加上次购买刷新符时间初始化
        self.last_refresh_token_purchase = self._load_last_refresh_token_purchase()

        # 药水物品列表
        self.potion_items = []

        # 初始化药水列表
        self._load_shop_items()

        # 创建UI元素
        self._create_ui_elements()

        logger.info("商店界面初始化完成")

    def _get_shop_state_dir(self) -> Path:
        """获取并确保商店状态目录存在"""
        try:
            # 使用用户主目录下的隐藏文件夹来存储状态
            home_dir = Path.home()
            app_data_dir = home_dir / ".萝卜放置传奇" # 应用数据根目录
            shop_state_dir = app_data_dir / "shop_state" # 商店状态子目录

            # 创建目录 (如果不存在)
            shop_state_dir.mkdir(parents=True, exist_ok=True)
            return shop_state_dir
        except Exception as e:
            logger.error(f"创建或获取商店状态目录失败: {e}")
            # 如果失败，回退到当前工作目录下的临时目录
            fallback_dir = Path("data") / "shop_state_fallback"
            fallback_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"商店状态将保存到回退目录: {fallback_dir.resolve()}")
            return fallback_dir

    def _load_first_visit_flag(self):
        """加载首次访问标志"""
        try:
            state_dir = self._get_shop_state_dir()
            flag_file = state_dir / "shop_visited.json"

            if flag_file.exists():
                with open(flag_file, 'r') as f:
                    data = json.load(f)
                    first_visit = data.get("first_visit", True)
                    logger.info(f"从文件加载商店首次访问标志: {first_visit}")
                    return first_visit
        except Exception as e:
            logger.error(f"加载商店首次访问标志失败: {e}")

        # 如果没有文件或加载失败，返回True（首次访问）
        return True

    def _save_first_visit_flag(self, value=False):
        """保存首次访问标志"""
        try:
            state_dir = self._get_shop_state_dir()
            flag_file = state_dir / "shop_visited.json"

            # 保存标志
            with open(flag_file, 'w') as f:
                json.dump({"first_visit": value}, f)
                logger.info(f"商店首次访问标志已保存: {value}")
        except Exception as e:
            logger.error(f"保存商店首次访问标志失败: {e}")

    def _load_shop_items(self):
        """加载商店物品数据"""
        logger.info("开始加载商店物品数据")

        # 确保药品数据已加载
        if not self.potion_items:
            self._load_potion_items()

        logger.info(f"商店加载完成，共 {len(self.potion_items)} 种药品")

    def _load_potion_items(self):
        """加载药品数据"""
        try:
            # 使用资源管理器加载消耗品数据
            from utils.resource_manager import resources
            config_path = "data/configs/consumables.json"
            consumables_data = resources.load_json_data(config_path, {})

            logger.info(f"从{config_path}加载消耗品数据")

            # 清空当前药品列表
            self.potion_items = []

            # 生命药水和魔法药水
            if "consumables" in consumables_data and "补给" in consumables_data["consumables"]:
                for item in consumables_data["consumables"]["补给"]:
                    # 根据药水类型分类
                    if "effect" in item:
                        potion_item = {
                            "name": item["name"],
                            "description": item["description"],
                            "price": item.get("price", 0),  # 使用配置文件中的价格，如果没有则为0
                            "type": "消耗品",
                            "quality": "普通"
                        }

                        # 根据恢复效果确定效果
                        if item["effect"] == "恢复生命":
                            potion_item["effect"] = {"type": "heal", "value": item["effect_value"]}
                            # 如果配置中没有价格，则按恢复量计算价格
                            if potion_item["price"] == 0:
                                potion_item["price"] = item["effect_value"] * 70  # 恢复原价
                        elif item["effect"] == "恢复魔法":
                            potion_item["effect"] = {"type": "mana", "value": item["effect_value"]}
                            # 如果配置中没有价格，则按恢复量计算价格
                            if potion_item["price"] == 0:
                                potion_item["price"] = item["effect_value"] * 70  # 恢复原价
                        elif item["effect"] == "双重恢复":
                            potion_item["effect"] = {
                                "type": "dual",
                                "hp_value": item.get("hp_restore", 0),
                                "mp_value": item.get("mp_restore", 0)
                            }
                            # 如果配置中没有价格，则按恢复量计算价格
                            if potion_item["price"] == 0:
                                potion_item["price"] = (item.get("hp_restore", 0) + item.get("mp_restore", 0)) * 50  # 恢复原价

                        # 添加到药品列表
                        self.potion_items.append(potion_item)

            # 添加特殊道具 - 商店刷新符
            if "consumables" in consumables_data and "任务道具" in consumables_data["consumables"]:
                for item in consumables_data["consumables"]["任务道具"]:
                    if item["name"] == "商店刷新符":
                        special_item = {
                            "name": "商店刷新符",
                            "description": "使用后可立即刷新商店装备，不影响刷新倒计时",
                            "price": 10,  # 保持10倍价格
                            "currency": "yuanbao",  # 使用元宝作为货币
                            "type": "消耗品",
                            "effect": {"type": "special", "value": 0},
                            "quality": "稀有"
                        }
                        self.potion_items.append(special_item)

            # 维持现有的技能书数据，因为这部分可能与技能配置相关联
            skill_books = [
                {
                    "name": "基本剑术",
                    "description": "学习或提升基本剑术技能，使攻击更准确",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "basic_sword", "class": "warrior"},
                },
                {
                    "name": "火球术",
                    "description": "学习火球术，向敌人投掷火球造成伤害",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "fireball", "class": "mage"},
                },
                {
                    "name": "治愈术",
                    "description": "学习治愈术，恢复自身生命值",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "healing", "class": "priest"},
                }
            ]

            # 添加技能书
            self.potion_items.extend(skill_books)

            logger.info(f"已加载 {len(self.potion_items)} 种物品")
        except Exception as e:
            logger.error(f"加载消耗品数据失败: {e}")

            # 如果加载失败，使用默认药品数据
            # 生命药水
            health_potions = [
                {
                    "name": "金创药(小量)",
                    "description": "缓慢恢复少量生命值",
                    "price": 60,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "heal", "value": 20},
                },
                {
                    "name": "金创药(中量)",
                    "description": "缓慢恢复中量生命值",
                    "price": 150,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "heal", "value": 50},
                },
                {
                    "name": "强效金创药",
                    "description": "缓慢恢复大量生命值",
                    "price": 320,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "heal", "value": 90},
                }
            ]

            # 魔法药水
            mana_potions = [
                {
                    "name": "魔法药(小量)",
                    "description": "缓慢恢复少量魔法值",
                    "price": 100,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "mana", "value": 30},
                },
                {
                    "name": "魔法药(中量)",
                    "description": "缓慢恢复中量魔法值",
                    "price": 220,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "mana", "value": 80},
                },
                {
                    "name": "强效魔法药",
                    "description": "缓慢恢复大量魔法值",
                    "price": 480,  # 恢复原价
                    "type": "消耗品",
                    "effect": {"type": "mana", "value": 150},
                }
            ]

            # 技能书
            skill_books = [
                {
                    "name": "基本剑术",
                    "description": "学习或提升基本剑术技能，使攻击更准确",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "basic_sword", "class": "warrior"},
                },
                {
                    "name": "火球术",
                    "description": "学习火球术，向敌人投掷火球造成伤害",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "fireball", "class": "mage"},
                },
                {
                    "name": "治愈术",
                    "description": "学习治愈术，恢复自身生命值",
                    "price": 1000,  # 价格调整为1000
                    "type": "技能书",
                    "effect": {"type": "learn_skill", "skill_id": "healing", "class": "priest"},
                }
            ]

            # 特殊道具
            special_items = [
                {
                    "name": "商店刷新符",
                    "description": "使用后可立即刷新商店装备，不影响刷新倒计时",
                    "price": 10,  # 保持10倍价格
                    "currency": "yuanbao",  # 使用元宝作为货币
                    "type": "消耗品",
                    "effect": {"type": "special", "value": 0},
                    "quality": "稀有"
                }
            ]

            # 将所有药品添加到列表
            self.potion_items.extend(health_potions)
            self.potion_items.extend(mana_potions)
            self.potion_items.extend(skill_books)
            self.potion_items.extend(special_items)

            logger.info(f"使用默认数据，已加载 {len(self.potion_items)} 种物品")

    def _create_ui_elements(self):
        """创建商店界面元素"""
        logger.info("开始创建商店界面组件")

        # 创建背景
        self.background = self.ui_manager.create_panel(
            pygame.Rect(0, 0, 1280, 720),
            "panel_dark"
        )
        self.add_component(self.background)

        # 创建返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 20, 100, 40),
            "返回",
            self._on_back_click
        )
        self.add_component(back_button)

        # 创建商店标题
        shop_title = self.ui_manager.create_text(
            pygame.Rect(640, 30, 0, 40),
            "商店",
            "chinese_large",
            pygame.Color(255, 255, 255),
            "center"
        )
        self.add_component(shop_title)

        # 获取默认货币值
        gold = 0
        yuanbao = 0
        if self.game_manager and hasattr(self.game_manager, 'player') and self.game_manager.player:
            gold = self.game_manager.player.gold
            yuanbao = self.game_manager.player.yuanbao

        # 创建金币和元宝显示
        # 使用文本替代图标
        gold_label = self.ui_manager.create_text(
            pygame.Rect(950, 20, 70, 24),
            "金币:",
            "chinese_normal",
            pygame.Color(255, 215, 0),
            "left"
        )
        self.add_component(gold_label)

        self.gold_text = self.ui_manager.create_text(
            pygame.Rect(1000, 20, 150, 24),
            f"{gold}",
            "chinese_normal",
            pygame.Color(255, 215, 0),
            "left"
        )
        self.add_component(self.gold_text)

        yuanbao_label = self.ui_manager.create_text(
            pygame.Rect(950, 50, 70, 24),
            "元宝:",
            "chinese_normal",
            pygame.Color(0, 191, 255),
            "left"
        )
        self.add_component(yuanbao_label)

        self.yuanbao_text = self.ui_manager.create_text(
            pygame.Rect(1000, 50, 150, 24),
            f"{yuanbao}",
            "chinese_normal",
            pygame.Color(0, 191, 255),
            "left"
        )
        self.add_component(self.yuanbao_text)

        # 定义内容区域
        content_x = 40
        content_y = 80
        content_width = 1200
        content_height = 600

        # 创建内容面板 - 使用更柔和的颜色
        content_panel = self.ui_manager.create_panel(
            pygame.Rect(content_x, content_y, content_width, content_height),
            color=(45, 45, 65),  # 更柔和的背景色
            border_color=(65, 65, 85)  # 更柔和的边框色
        )
        self.add_component(content_panel)

        # 药水区域 (左侧) - 扩展为占据左侧2/3的空间
        potion_panel_width = content_width * 2/3 - 20
        potion_panel = self.ui_manager.create_panel(
            pygame.Rect(content_x + 10, content_y + 10, potion_panel_width, content_height - 20),
            color=(35, 35, 55),  # 更柔和的背景色
            border_color=(55, 55, 75)  # 更柔和的边框色
        )
        self.add_component(potion_panel)

        potion_title = self.ui_manager.create_text(
            pygame.Rect(content_x + 10 + potion_panel_width/2, content_y + 20, 0, 30),
            "商品列表",
            "chinese_normal",
            pygame.Color(220, 220, 240),  # 更柔和的文字颜色
            "center"
        )
        self.add_component(potion_title)

        # 创建药水按钮区域 - 使用更大的区域
        potion_button_area_height = content_height - 70
        self.potion_button_area = self.ui_manager.create_panel(
            pygame.Rect(content_x + 20, content_y + 60, potion_panel_width - 20, potion_button_area_height),
            color=(35, 35, 55),  # 与药水面板相同的颜色
            border_color=(55, 55, 75)  # 与药水面板相同的边框色
        )
        self.add_component(self.potion_button_area)

        # 商品详情面板 (右侧) - 调整为占据右侧1/3的空间，并且占据整个高度
        detail_panel_width = content_width * 1/3 - 20
        detail_panel_height = content_height - 20
        detail_panel_x = content_x + potion_panel_width + 20
        detail_panel_y = content_y + 10

        self.detail_panel = self.ui_manager.create_panel(
            pygame.Rect(detail_panel_x, detail_panel_y, detail_panel_width, detail_panel_height),
            color=(35, 35, 55),  # 更柔和的背景色
            border_color=(55, 55, 75)  # 更柔和的边框色
        )
        self.add_component(self.detail_panel)

        detail_title = self.ui_manager.create_text(
            pygame.Rect(detail_panel_x + detail_panel_width/2, detail_panel_y + 20, 0, 30),
            "商品详情",
            "chinese_normal",
            pygame.Color(220, 220, 240),  # 更柔和的文字颜色
            "center"
        )
        self.add_component(detail_title)

        # -- 创建详情文本组件 (初始隐藏) --
        # 商品名称文本 - 使用更大的字体和更醒目的颜色
        self.item_name = self.ui_manager.create_text(
            pygame.Rect(
                self.detail_panel.rect.left + 10,
                self.detail_panel.rect.top + 60,
                self.detail_panel.rect.width - 20,
                40  # 增加高度
            ),
            "", # 初始为空
            "chinese_normal",
            (230, 230, 250),  # 更亮的文字颜色
            "center"  # 居中显示
        )
        self.item_name.visible = False # 初始隐藏
        self.add_component(self.item_name)

        # 商品描述文本 - 增加高度，显示更多内容
        self.item_description = self.ui_manager.create_text(
            pygame.Rect(
                self.detail_panel.rect.left + 15,
                self.detail_panel.rect.top + 110, # 名称下方留出更多空间
                self.detail_panel.rect.width - 30,
                self.detail_panel.rect.height - 180 # 预留更多空间给购买区域
            ),
            "", # 初始为空
            "chinese_small",
            (200, 200, 220),  # 更柔和的文字颜色
            "left"
        )
        self.item_description.max_width = self.detail_panel.rect.width - 30
        self.item_description.visible = False # 初始隐藏
        self.add_component(self.item_description)
        # -- 详情文本组件创建结束 --

        # 创建购买按钮 - 使用更醒目的颜色和更大的尺寸
        buy_button_width = 180
        buy_button_height = 50

        # 添加调试信息
        logger.info("准备创建购买按钮")

        # 创建按钮
        self.buy_button = self.ui_manager.create_button(
            pygame.Rect(detail_panel_x + detail_panel_width/2 - buy_button_width/2,
                      detail_panel_y + detail_panel_height - 70,
                      buy_button_width, buy_button_height),
            "购买",
            self._on_buy_item,
            "chinese_normal"
        )

        # 添加调试信息
        logger.info("购买按钮创建成功")
        # 手动设置按钮颜色
        self.buy_button.colors.update({
            "normal": (60, 120, 190),  # 蓝色按钮
            "hover": (80, 140, 210),   # 悬停时更亮
            "pressed": (40, 100, 170)  # 点击时更暗
        })
        self.buy_button.visible = False
        self.add_component(self.buy_button)

        # 装备商店已移除，设置兼容性属性以避免错误
        self.equipment_button_area = None
        self.equipment_buttons = []

        logger.info("商店界面组件创建完成")

    def _on_back_click(self):
        """处理返回按钮点击"""
        logger.info("点击了返回按钮，返回游戏界面")
        self.ui_manager.show_screen("game")

    def _on_potion_click(self, potion):
        """处理药水点击事件"""
        if not potion:
            logger.warning("点击了不存在的药水")
            return

        logger.info(f"点击了药水: {potion.get('name', '未知药水')}")

        # 设置选中状态
        self.selected_item = potion
        self.selected_item_type = "potion"

        # 显示详情面板
        self.detail_panel.visible = True

        # 更新物品详情显示
        self._update_item_details(potion)

    def _on_equipment_click(self, equipment):
        """处理装备点击事件 - 保留为空方法以保证兼容性"""
        pass

    def _update_item_details(self, item):
        """更新物品详情显示"""
        # 首先清空并隐藏旧的详情
        self._clear_item_details()

        if not item:
            logger.warning("尝试更新空物品详情")
            # 确保面板也隐藏
            if hasattr(self, 'detail_panel'):
                 self.detail_panel.visible = False
            return


        # 显示详情面板 (包含背景)
        self.detail_panel.visible = True

        # 获取物品信息
        name = item.get('name', '未知物品')
        description = item.get('description', '暂无描述')
        price = item.get('price', 0)
        currency = item.get('currency', 'gold')
        quality = item.get('quality', '普通')

        # 设置物品名称，带品质标识
        self.item_name.set_text(f"{name} [{quality}]")
        self.item_name.color = self.ui_manager.get_quality_color(quality) # 根据品质设置颜色

        # --- 构建详细描述文本 ---
        detailed_desc_lines = []
            # 1. 添加基础描述 (让 Text 组件自动换行)
        if description:

            processed_description = description.replace("\\\\n", "\\n")
            detailed_desc_lines.append(processed_description)
        else:
            detailed_desc_lines.append("暂无描述")

        detailed_desc_lines.append("")

        # 2. 添加药水效果
        if 'hp_restore' in item:
            detailed_desc_lines.append(f"回复生命值: {item.get('hp_restore', 0)}")
        if 'mp_restore' in item:
            detailed_desc_lines.append(f"回复魔法值: {item.get('mp_restore', 0)}")

        # 3. 添加价格信息
        if currency == "yuanbao":
            detailed_desc_lines.append(f"价格: {price} 元宝")
        else:
            detailed_desc_lines.append(f"价格: {price} 金币")

        # --- 合并所有行并设置文本 ---
        final_desc = "\n".join(detailed_desc_lines)
        self.item_description.set_text(final_desc)

        # --- 设置组件可见 ---
        self.item_name.visible = True
        self.item_description.visible = True
        # --- 组件可见性设置结束 ---

        # 更新购买按钮状态和文本
        player = self.game_manager.player
        can_afford = False
        if player:
             if currency == "yuanbao":
                 can_afford = player.yuanbao >= price
                 self.buy_button.set_text(f"购买 ({price} 元宝)")
             else:
                 can_afford = player.gold >= price
                 self.buy_button.set_text(f"购买 ({price} 金币)")
        else:
             # 没有玩家数据，禁用购买
             can_afford = False
             self.buy_button.set_text("购买")


        self.buy_button.set_active(can_afford)
        self.buy_button.visible = True

        logger.info(f"更新物品详情: {name}, 价格: {price} {currency}")

    def _on_buy_item(self):
        """处理购买物品"""
        if not self.selected_item:
            logger.warning("没有选中物品，无法购买")
            return

        # 获取物品信息
        item = self.selected_item
        item_name = item.get('name', '未知物品')
        item_price = item.get('price', 0)
        item_currency = item.get('currency', 'gold')  # 获取货币类型，默认为金币
        item_type = item.get('type', '') # 获取物品类型

        logger.info(f"尝试购买物品: {item_name}, 类型: {item_type}, 价格: {item_price}, 货币类型: {item_currency}")

        # 确保玩家存在
        player = self.game_manager.player
        if not player:
            logger.error("玩家不存在，无法购买物品")
            return

        # 检查物品类型是否为技能书
        if item_type == "技能书":
            logger.info(f"购买技能书: {item_name}, 直接购买数量1")
            # 临时保存物品信息，以便_buy_potion_with_quantity能够获取
            self.quantity_selector_item = item
            self.quantity_selector_item_type = item_type # 传递类型
            self._buy_potion_with_quantity(1) # 直接购买1个
        else:
            # 对于非技能书物品，显示数量选择器
            self._show_potion_quantity_selector()

    def _show_potion_quantity_selector(self):
        """显示药水数量选择器 - 使用进度条拖拽选择"""
        logger.info("显示药水数量选择器")

        # 检查是否有选中的物品
        if not self.selected_item:
            logger.warning("没有选中物品，无法显示数量选择器")
            return

        # 保存当前选中的物品，防止在过程中丢失
        self.quantity_selector_item = self.selected_item
        self.quantity_selector_item_type = self.selected_item_type

        # 如果已经存在数量选择器相关的组件，先移除
        if hasattr(self, 'quantity_panel'):
            self.remove_component(self.quantity_panel)
        if hasattr(self, 'qty_btn_10'):
            self.remove_component(self.qty_btn_10)
        if hasattr(self, 'qty_btn_100'):
            self.remove_component(self.qty_btn_100)
        if hasattr(self, 'qty_btn_500'):
            self.remove_component(self.qty_btn_500)
        if hasattr(self, 'qty_cancel_btn'):
            self.remove_component(self.qty_cancel_btn)
        if hasattr(self, 'slider_handle'):
            self.remove_component(self.slider_handle)
        if hasattr(self, 'slider_bg'):
            self.remove_component(self.slider_bg)
        if hasattr(self, 'quantity_text'):
            self.remove_component(self.quantity_text)
        if hasattr(self, 'confirm_btn'):
            self.remove_component(self.confirm_btn)

        # 创建数量选择面板 - 使用更大的面板以容纳进度条
        panel_width = 400
        panel_height = 250

        # 使用pygame获取屏幕尺寸
        screen_width, screen_height = pygame.display.get_surface().get_size()
        panel_x = screen_width / 2 - panel_width / 2
        panel_y = screen_height / 2 - panel_height / 2

        self.quantity_panel = self.ui_manager.create_panel(
            pygame.Rect(panel_x, panel_y, panel_width, panel_height),
            color=(35, 35, 55),  # 更柔和的背景色
            border_color=(55, 55, 75)  # 更柔和的边框色
        )
        self.add_component(self.quantity_panel)

        # 创建标题
        self.qty_title = self.ui_manager.create_text(
            pygame.Rect(panel_x + panel_width / 2, panel_y + 20, 0, 30),
            "选择购买数量",
            "chinese_normal",
            pygame.Color(220, 220, 240),  # 更柔和的文字颜色
            "center"
        )
        self.add_component(self.qty_title)

        # 创建物品名称显示
        item_name = self.quantity_selector_item.get('name', '未知物品')
        item_price = self.quantity_selector_item.get('price', 0)
        currency = self.quantity_selector_item.get('currency', 'gold')
        currency_text = "元宝" if currency == "yuanbao" else "金币"

        self.item_text = self.ui_manager.create_text(
            pygame.Rect(panel_x + panel_width / 2, panel_y + 50, 0, 30),
            f"{item_name} ({item_price} {currency_text}/个)",
            "chinese_normal",
            pygame.Color(200, 200, 220),  # 更柔和的文字颜色
            "center"
        )
        self.add_component(self.item_text)

        # 创建进度条背景
        slider_width = panel_width - 60
        slider_height = 30
        slider_x = panel_x + 30
        slider_y = panel_y + 100

        self.slider_bg = self.ui_manager.create_panel(
            pygame.Rect(slider_x, slider_y, slider_width, slider_height),
            color=(25, 25, 45),  # 更暗的背景色
            border_color=(45, 45, 65)  # 更暗的边框色
        )
        self.add_component(self.slider_bg)

        # 创建进度条滑块
        self.slider_handle_width = 20
        self.slider_handle = self.ui_manager.create_panel(
            pygame.Rect(slider_x, slider_y, self.slider_handle_width, slider_height),
            color=(60, 120, 190),  # 蓝色滑块
            border_color=(80, 140, 210)  # 更亮的边框色
        )
        self.add_component(self.slider_handle)

        # 保存进度条相关参数，用于拖拽计算
        self.slider_min_x = slider_x
        self.slider_max_x = slider_x + slider_width - self.slider_handle_width
        self.slider_dragging = False

        # 设置最大数量和当前数量
        self.max_quantity = 1000  # 最大购买数量
        self.current_quantity = 1  # 默认数量

        # 创建数量显示文本
        self.quantity_text = self.ui_manager.create_text(
            pygame.Rect(panel_x + panel_width / 2, slider_y + slider_height + 20, 0, 30),
            f"数量: {self.current_quantity}  总价: {self.current_quantity * item_price} {currency_text}",
            "chinese_normal",
            pygame.Color(220, 220, 240),  # 更柔和的文字颜色
            "center"
        )
        self.add_component(self.quantity_text)

        # 创建确认和取消按钮
        button_width = 120
        button_height = 40
        button_spacing = 40
        buttons_y = panel_y + panel_height - 60

        # 确认按钮
        self.confirm_btn = self.ui_manager.create_button(
            pygame.Rect(panel_x + panel_width/2 - button_width - button_spacing/2, buttons_y, button_width, button_height),
            "确认购买",
            lambda: self._buy_potion_with_quantity(self.current_quantity),
            "chinese_normal"
        )
        # 手动设置按钮颜色
        self.confirm_btn.colors.update({
            "normal": (60, 120, 190),  # 蓝色按钮
            "hover": (80, 140, 210),   # 悬停时更亮
            "pressed": (40, 100, 170)  # 点击时更暗
        })
        self.add_component(self.confirm_btn)

        # 取消按钮
        self.qty_cancel_btn = self.ui_manager.create_button(
            pygame.Rect(panel_x + panel_width/2 + button_spacing/2, buttons_y, button_width, button_height),
            "取消",
            self._close_quantity_selector,
            "chinese_normal"
        )
        # 手动设置按钮颜色
        self.qty_cancel_btn.colors.update({
            "normal": (80, 80, 100),  # 灰色按钮
            "hover": (100, 100, 120),   # 悬停时更亮
            "pressed": (60, 60, 80)  # 点击时更暗
        })
        self.add_component(self.qty_cancel_btn)

    def _close_quantity_selector(self):
        """关闭数量选择器"""
        logger.info("关闭数量选择器")

        # 移除数量选择器相关的组件
        if hasattr(self, 'quantity_panel'):
            self.remove_component(self.quantity_panel)
            delattr(self, 'quantity_panel')

        # 移除旧的按钮组件（兼容性）
        if hasattr(self, 'qty_btn_10'):
            self.remove_component(self.qty_btn_10)
            delattr(self, 'qty_btn_10')
        if hasattr(self, 'qty_btn_100'):
            self.remove_component(self.qty_btn_100)
            delattr(self, 'qty_btn_100')
        if hasattr(self, 'qty_btn_500'):
            self.remove_component(self.qty_btn_500)
            delattr(self, 'qty_btn_500')

        # 移除新的进度条组件
        if hasattr(self, 'slider_handle'):
            self.remove_component(self.slider_handle)
            delattr(self, 'slider_handle')
        if hasattr(self, 'slider_bg'):
            self.remove_component(self.slider_bg)
            delattr(self, 'slider_bg')
        if hasattr(self, 'quantity_text'):
            self.remove_component(self.quantity_text)
            delattr(self, 'quantity_text')
        if hasattr(self, 'confirm_btn'):
            self.remove_component(self.confirm_btn)
            delattr(self, 'confirm_btn')

        # 移除取消按钮
        if hasattr(self, 'qty_cancel_btn'):
            self.remove_component(self.qty_cancel_btn)
            delattr(self, 'qty_cancel_btn')

        # 重置拖拽状态
        self.slider_dragging = False

        # 移除标题文本
        if hasattr(self, 'qty_title'):
            self.remove_component(self.qty_title)
            delattr(self, 'qty_title')

        # 移除物品名称文本
        if hasattr(self, 'item_text'):
            self.remove_component(self.item_text)
            delattr(self, 'item_text')

        # 清除保存的物品数据
        if hasattr(self, 'quantity_selector_item'):
            delattr(self, 'quantity_selector_item')
        if hasattr(self, 'quantity_selector_item_type'):
            delattr(self, 'quantity_selector_item_type')

    def _buy_potion_with_quantity(self, quantity):
        """以指定数量购买药水"""
        # 使用保存在数量选择器中的物品，而不是依赖self.selected_item
        if hasattr(self, 'quantity_selector_item') and self.quantity_selector_item:
            potion = self.quantity_selector_item
            item_type = getattr(self, 'quantity_selector_item_type', 'potion')
        else:
            # 如果没有保存的物品，尝试使用当前选中物品
            if not self.selected_item:
                logger.warning("没有选中物品，无法购买")
                self._close_quantity_selector()
                return
            potion = self.selected_item
            item_type = self.selected_item_type

        player = self.game_manager.player
        if not player:
            self._close_quantity_selector()
            return

        # 获取药水信息
        potion_name = potion.get('name', '未知药水')
        potion_price = potion.get('price', 0)
        potion_currency = potion.get('currency', 'gold')

        # 检查背包是否已满 - 添加这个检查
        if hasattr(player, "inventory"):
            consumable_types = ["消耗品", "恢复消耗品", "药剂", "药水", "任务道具"]
            item_type = potion.get("type", "")
            max_slots = getattr(player, "max_inventory_slots", 360)  # 默认360个物品槽位（10页*36个物品）

            # 如果不是可堆叠的消耗品，检查背包是否有足够空间
            if item_type not in consumable_types:
                if len(player.inventory) + quantity > max_slots:
                    self.ui_manager.show_message("背包已满", "无法购买更多物品，请先清理背包")
                    self._close_quantity_selector()
                    return
            # 如果是消耗品，检查是否已有相同物品可堆叠
            else:
                existing_item = False
                for inv_item in player.inventory:
                    if (isinstance(inv_item, dict) and
                        inv_item.get("name", "") == potion_name and
                        inv_item.get("type", "") in consumable_types):
                        existing_item = True
                        break

                # 如果背包中没有相同物品且背包已满，无法添加
                if not existing_item and len(player.inventory) >= max_slots:
                    self.ui_manager.show_message("背包已满", "无法购买更多物品，请先清理背包")
                    self._close_quantity_selector()
                    return

        # 计算总价
        total_price = potion_price * quantity

        # 检查玩家金币/元宝是否足够
        if potion_currency == "yuanbao":
            if player.yuanbao < total_price:
                logger.warning(f"元宝不足，需要: {total_price}, 拥有: {player.yuanbao}")
                self.ui_manager.show_message("元宝不足", f"购买 {quantity}个 {potion_name} 需要 {total_price} 元宝")
                self._close_quantity_selector()
                return
            # 扣除元宝
            player.yuanbao -= total_price
            logger.info(f"扣除 {total_price} 元宝，剩余: {player.yuanbao}")
        else:  # 默认使用金币
            if player.gold < total_price:
                logger.warning(f"金币不足，需要: {total_price}, 拥有: {player.gold}")
                self.ui_manager.show_message("金币不足", f"购买 {quantity}个 {potion_name} 需要 {total_price} 金币")
                self._close_quantity_selector()
                return
            # 扣除金币
            player.gold -= total_price
            logger.info(f"扣除 {total_price} 金币，剩余: {player.gold}")

        # 如果是商店刷新符，记录购买时间并添加刷新符
        if potion_name == "商店刷新符":
            # 记录购买刷新符的时间
            current_time = time.time()
            self.last_refresh_token_purchase = current_time
            self._save_last_refresh_token_purchase(current_time)
            logger.info(f"玩家购买了刷新符，记录购买时间: {current_time}")

            # 增加刷新符数量
            if hasattr(player, 'refresh_tokens'):
                player.refresh_tokens += quantity
                logger.info(f"增加{quantity}个刷新符，当前数量: {player.refresh_tokens}")
            else:
                player.refresh_tokens = quantity
                logger.info(f"首次获得刷新符，设置数量为{quantity}")
            add_success = True
        else:
            # 创建要添加到背包的物品副本
            potion_copy = potion.copy()

            # 确保物品有正确的类型信息
            if "type" not in potion_copy or not potion_copy["type"]:
                # 根据物品效果或名称推断类型
                if "effect" in potion_copy:
                    effect = potion_copy["effect"]
                    if effect in ["恢复生命", "恢复魔法", "双重恢复"]:
                        potion_copy["type"] = "恢复消耗品"
                    else:
                        potion_copy["type"] = "消耗品"
                elif "药" in potion_name or "丹" in potion_name or "水" in potion_name:
                    potion_copy["type"] = "恢复消耗品"
                else:
                    potion_copy["type"] = "消耗品"

                logger.info(f"为物品设置类型: {potion_name}, 类型: {potion_copy['type']}")

            # 添加普通药水到玩家背包
            if hasattr(player, 'add_item'):
                add_success = player.add_item(potion_copy, quantity)
                if add_success:
                    logger.info(f"已将药水 {potion_name} 添加到玩家背包，数量: {quantity}")
                else:
                    logger.error(f"添加药水 {potion_name} 到背包失败")
                    # 如果添加失败，退还货币
                    if potion_currency == "yuanbao":
                        player.yuanbao += total_price
                        logger.info(f"退还 {total_price} 元宝，当前: {player.yuanbao}")
                    else:
                        player.gold += total_price
                        logger.info(f"退还 {total_price} 金币，当前: {player.gold}")
                    self._close_quantity_selector()
                    self.ui_manager.show_message("添加失败", f"无法将 {potion_name} 添加到背包")
                    return
            else:
                logger.warning("玩家对象没有add_item方法")
                # 如果货币已扣除但添加失败，退还货币
                if potion_currency == "yuanbao":
                    player.yuanbao += total_price
                else:
                    player.gold += total_price
                self._close_quantity_selector()
                self.ui_manager.show_message("添加失败", "无法添加物品到背包")
                return

        # 只有添加成功才显示购买成功信息
        if add_success:
            if potion_currency == "yuanbao":
                self.ui_manager.show_message("购买成功", f"使用 {total_price} 元宝购买了 {quantity}个 {potion_name}")
            else:
                self.ui_manager.show_message("购买成功", f"使用 {total_price} 金币购买了 {quantity}个 {potion_name}")

        # 关闭数量选择器
        self._close_quantity_selector()

        # 更新货币显示
        self._update_gold_display()

        # 清空选中状态
        self.selected_item = None
        self.selected_item_type = None
        self._clear_item_details()

    def _buy_equipment(self, equipment, currency="gold"):
        """购买装备"""
        player = self.game_manager.player
        if not player:
            return

        # 获取装备信息
        equipment_name = equipment.get('name', '未知装备')
        equipment_price = equipment.get('price', 0)

        # 检查背包是否已满
        max_slots = getattr(player, "max_inventory_slots", 360)  # 默认360个物品槽位（10页*36个物品）
        if hasattr(player, "inventory") and len(player.inventory) >= max_slots:
            self.ui_manager.show_message("背包已满", "无法购买更多装备，请先清理背包")
            return

        # 根据货币类型扣除相应的金币/元宝
        if currency == "yuanbao":
            player.yuanbao -= equipment_price
            logger.info(f"扣除 {equipment_price} 元宝，剩余: {player.yuanbao}")
        else:  # 默认使用金币
            player.gold -= equipment_price
            logger.info(f"扣除 {equipment_price} 金币，剩余: {player.gold}")

        # 添加装备到玩家背包
        add_success = False
        if hasattr(player, 'add_equipment'):
            add_success = player.add_equipment(equipment)
            if add_success:
                logger.info(f"已将装备 {equipment_name} 添加到玩家背包")
            else:
                logger.error(f"添加装备 {equipment_name} 到背包失败")
        elif hasattr(player, 'inventory'):
            # 如果没有add_equipment方法，但有inventory属性，直接添加到背包
            try:
                # 确保装备有正确的类型信息
                equipment_copy = equipment.copy()
                if "type" not in equipment_copy or not equipment_copy["type"]:
                    if "equipment_type" in equipment_copy:
                        equipment_copy["type"] = equipment_copy["equipment_type"]
                    else:
                        # 尝试从名称或其他属性推断类型
                        equipment_copy["type"] = "装备"

                player.inventory.append(equipment_copy)
                logger.info(f"已将装备 {equipment_name} 添加到玩家背包")
                add_success = True
            except Exception as e:
                logger.error(f"直接添加装备到背包时出错: {e}")
                add_success = False
        else:
            logger.warning("玩家对象没有装备相关属性")
            add_success = False

        # 如果添加失败，退还货币
        if not add_success:
            if currency == "yuanbao":
                player.yuanbao += equipment_price
                logger.info(f"退还 {equipment_price} 元宝，当前: {player.yuanbao}")
            else:
                player.gold += equipment_price
                logger.info(f"退还 {equipment_price} 金币，当前: {player.gold}")
            self.ui_manager.show_message("添加失败", f"无法将装备 {equipment_name} 添加到背包")
            return

        # 显示购买成功信息
        if currency == "yuanbao":
            self.ui_manager.show_message("购买成功", f"使用 {equipment_price} 元宝购买了 {equipment_name}")
        else:
            self.ui_manager.show_message("购买成功", f"使用 {equipment_price} 金币购买了 {equipment_name}")

        # 如果是限时商店装备，从列表中移除
        if equipment in self.equipment_items:
            self.equipment_items.remove(equipment)
            logger.info(f"已从商店移除装备: {equipment_name}")

            # 保存更新后的装备列表
            self._save_equipment_items()

            # 如果商店为空，刷新装备
            if len(self.equipment_items) == 0:
                logger.info("商店装备已售罄，自动刷新")
                self._load_equipment_items(force_refresh=True)

            # 更新装备按钮显示
            self._update_equipment_buttons()

        # 更新货币显示
        self._update_gold_display()

    def _clear_item_details(self):
        """清空并隐藏商品详情显示"""
        # 确保组件已创建
        if hasattr(self, 'item_name'):
            self.item_name.set_text("") # 清空文本是个好习惯
            self.item_name.visible = False # 隐藏
        if hasattr(self, 'item_description'):
            self.item_description.set_text("") # 清空文本
            self.item_description.visible = False # 隐藏
        if hasattr(self, 'buy_button'):
            self.buy_button.set_active(False)
            self.buy_button.visible = False # 确保按钮也被隐藏
        # 隐藏详情面板背景
        if hasattr(self, 'detail_panel'):
            self.detail_panel.visible = False

    def _update_gold_display(self):
        """更新金币和元宝显示"""
        if not hasattr(self, 'gold_text') or not hasattr(self, 'yuanbao_text'):
            logger.warning("金币或元宝文本控件未创建")
            return

        # 获取默认货币值
        gold = 0
        yuanbao = 0
        if self.game_manager and hasattr(self.game_manager, 'player') and self.game_manager.player:
            gold = self.game_manager.player.gold
            yuanbao = self.game_manager.player.yuanbao

        # 更新显示
        self.gold_text.set_text(f"{gold}")
        self.yuanbao_text.set_text(f"{yuanbao}")

        logger.debug(f"更新货币显示: 金币={gold}, 元宝={yuanbao}")

    def _load_last_refresh_token_purchase(self):
        """加载上次购买刷新符的时间"""
        try:
            state_dir = self._get_shop_state_dir()
            purchase_file = state_dir / "refresh_token_purchase.json"

            if purchase_file.exists():
                with open(purchase_file, 'r') as f:
                    data = json.load(f)
                    last_time = data.get("last_purchase_time", 0)
                    logger.info(f"从文件加载上次购买刷新符时间: {last_time}")
                    return last_time
        except Exception as e:
            logger.error(f"加载上次购买刷新符时间失败: {e}")

        # 如果没有文件或加载失败，返回0（从未购买过）
        return 0

    def _save_last_refresh_token_purchase(self, purchase_time):
        """保存上次购买刷新符的时间"""
        try:
            state_dir = self._get_shop_state_dir()
            purchase_file = state_dir / "refresh_token_purchase.json"

            # 保存购买时间
            with open(purchase_file, 'w') as f:
                json.dump({"last_purchase_time": purchase_time}, f)
                logger.info(f"上次购买刷新符时间已保存: {purchase_time}")
        except Exception as e:
            logger.error(f"保存上次购买刷新符时间失败: {e}")

    def _can_purchase_refresh_token(self):
        """检查今天是否已经购买过刷新符"""
        current_time = time.time()
        last_purchase = self.last_refresh_token_purchase

        # 如果从未购买过，允许购买
        if last_purchase == 0:
            return True

        # 转换为日期对象比较是否是同一天
        from datetime import datetime
        current_date = datetime.fromtimestamp(current_time).date()
        last_purchase_date = datetime.fromtimestamp(last_purchase).date()

        # 如果不是同一天，允许购买
        return current_date != last_purchase_date

    def show(self):
        """显示商店界面"""
        super().show()
        logger.info("显示商店界面")

        # 更新货币显示
        self._update_gold_display()

        # 加载药水物品
        self._load_potion_items()

        # 显示药水物品
        self._populate_potion_items()

        # 隐藏详情面板
        if hasattr(self, 'detail_panel'):
            self.detail_panel.visible = False

        # 隐藏购买按钮
        if hasattr(self, 'buy_button'):
            self.buy_button.visible = False

    def update(self, dt):
        """更新商店界面"""
        super().update(dt)

        # 更新金币和元宝显示
        self._update_gold_display()

    def draw(self, surface):
        """绘制商店界面"""
        super().draw(surface)

    def _save_equipment_items(self):
        """保存当前装备列表到文件，确保重启游戏后装备不会变化"""
        try:
            state_dir = self._get_shop_state_dir()
            file_path = state_dir / "shop_equipment.json"

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.equipment_items, f, ensure_ascii=False, indent=2)

            logger.info(f"成功保存 {len(self.equipment_items)} 件装备到文件")
        except Exception as e:
            logger.error(f"保存装备列表失败: {e}")

    def _load_equipment_items_from_file(self):
        """从文件加载装备列表"""
        try:
            state_dir = self._get_shop_state_dir()
            file_path = state_dir / "shop_equipment.json"

            # 检查文件是否存在
            if not file_path.exists():
                logger.warning(f"装备商店文件不存在: {file_path}")
                return []

            # 打开并读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                import json
                try:
                    equipment_items = json.load(f)

                    # 确保加载的数据是列表类型
                    if not isinstance(equipment_items, list):
                        logger.error(f"装备数据格式错误，期望列表但获得了 {type(equipment_items).__name__}")
                        return []

                    logger.info(f"从文件加载了 {len(equipment_items)} 件装备")
                    return equipment_items
                except json.JSONDecodeError as e:
                    logger.error(f"解析装备JSON文件失败: {e}")
                    return []
        except Exception as e:
            logger.error(f"加载装备文件失败: {e}")
        return []

    def _load_shop_config(self):
        """加载商店配置文件"""
        try:
            # 使用资源管理器加载商店配置
            from utils.resource_manager import resources
            config_path = "data/configs/shop_items.json"
            shop_config = resources.load_json_data(config_path, {})

            if "shop_config" in shop_config:
                logger.info(f"成功加载商店配置文件")
                # 更新刷新间隔
                if "refresh_interval" in shop_config["shop_config"]:
                    self.refresh_interval = shop_config["shop_config"]["refresh_interval"]
                    logger.info(f"设置商店刷新间隔为: {self.refresh_interval}秒")
                return shop_config["shop_config"]
            else:
                logger.warning(f"商店配置文件格式不正确，使用默认配置")
                return {}
        except Exception as e:
            logger.error(f"加载商店配置文件失败: {e}")
            return {}

    def refresh_shop(self):
        """刷新商店物品 - 简化版，不需要支付费用"""
        logger.info("刷新商店物品...")

        # 重新加载药水商店
        self._load_potion_items()
        self._populate_potion_items()

        # 更新金币显示
        self._update_gold_display()

        # 提示刷新成功
        self.ui_manager.show_message("刷新成功", "商店物品已更新")

    def _buy_potion(self, potion):
        """购买药水 - 弹出数量选择器"""
        self._show_potion_quantity_selector()

    def _populate_potion_items(self):
        """在界面上显示药水物品 - 按类型分组显示"""
        logger.info("开始填充药水物品显示")

        # 确保药水按钮区域存在
        if not hasattr(self, 'potion_button_area'):
            logger.error("药水按钮区域不存在")
            return

        # 清除当前所有药水按钮和分类标签
        for component in list(self.components):
            if hasattr(component, 'potion_data') or hasattr(component, 'category_label'):
                self.remove_component(component)

        # 如果没有药水数据，加载药水数据
        if not self.potion_items:
            self._load_potion_items()

        if not self.potion_items:
            logger.warning("没有可用的药水物品")
            return

        # 计算按钮位置和大小
        panel_width = self.potion_button_area.rect.width
        panel_height = self.potion_button_area.rect.height

        # 使用网格布局，每行显示3个物品
        columns = 3
        button_width = (panel_width - (columns + 1) * 10) // columns  # 每列之间留10px间距
        button_height = 40
        button_spacing_h = 10  # 水平间距
        button_spacing_v = 15  # 垂直间距

        # 按类型对药水进行分组
        potion_categories = {
            "生命药水": [],
            "魔法药水": [],
            "特殊道具": [],
            "技能书": []
        }

        # 对药水进行分类
        for potion in self.potion_items:
            # 获取药水信息
            effect = potion.get('effect', {})
            effect_type = effect.get('type', '')
            item_type = potion.get('type', '')

            # 根据类型分组
            if item_type == '技能书':
                potion_categories["技能书"].append(potion)
            elif effect_type == 'heal':
                potion_categories["生命药水"].append(potion)
            elif effect_type == 'mana':
                potion_categories["魔法药水"].append(potion)
            else:
                potion_categories["特殊道具"].append(potion)

        # 计算每个分类的起始位置
        current_y = self.potion_button_area.rect.top + 10

        # 遍历每个分类
        for category_name, potions in potion_categories.items():
            if not potions:  # 跳过空分类
                continue

            # 创建分类标题
            category_label = self.ui_manager.create_text(
                pygame.Rect(
                    self.potion_button_area.rect.left + 10,
                    current_y,
                    panel_width - 20,
                    30
                ),
                category_name,
                "chinese_normal",
                pygame.Color(220, 220, 240),  # 使用柔和的文字颜色
                "left"
            )
            category_label.category_label = True  # 添加标记以便后续清除
            self.add_component(category_label)

            # 更新垂直位置
            current_y += 35

            # 计算该分类需要的行数
            rows_needed = (len(potions) + columns - 1) // columns

            # 创建该分类的物品按钮
            for i, potion in enumerate(potions):
                # 计算行和列
                row = i // columns
                col = i % columns

                # 获取药水信息
                name = potion.get('name', '未知物品')
                price = potion.get('price', 0)
                currency = potion.get('currency', 'gold')
                quality = potion.get('quality', '普通')

                # 根据药水效果类型设置颜色
                effect = potion.get('effect', {})
                effect_type = effect.get('type', '')
                item_type = potion.get('type', '')

                # 设置按钮文本颜色
                if category_name == "生命药水":
                    color = (255, 80, 80)  # 红色 - 生命药水
                elif category_name == "魔法药水":
                    color = (80, 80, 255)  # 蓝色 - 魔法药水
                elif category_name == "技能书":
                    color = (80, 200, 80)  # 绿色 - 技能书
                else:
                    color = (220, 220, 100)  # 黄色 - 其他物品

                # 根据品质调整颜色亮度
                if quality == '稀有':
                    # 使用品质颜色
                    quality_colors = {
                        "普通": (200, 200, 200),
                        "精良": (100, 200, 100),
                        "稀有": (100, 100, 255),
                        "史诗": (200, 100, 255),
                        "传说": (255, 165, 0)
                    }
                    color = quality_colors.get(quality, (200, 200, 200))

                # 设置按钮文本
                if currency == 'yuanbao':
                    button_text = f"{name} - {price}元宝"
                else:
                    button_text = f"{name} - {price}金币"

                # 计算按钮位置
                button_x = self.potion_button_area.rect.left + button_spacing_h + col * (button_width + button_spacing_h)
                button_y = current_y + row * (button_height + button_spacing_v)

                # 创建药水按钮
                potion_button = self.ui_manager.create_button(
                    pygame.Rect(button_x, button_y, button_width, button_height),
                    button_text,
                    lambda p=potion: self._on_potion_click(p),
                    "chinese_small"  # 使用小一点的字体
                )
                # 手动设置按钮颜色
                potion_button.colors.update({
                    "normal": (40, 40, 60),  # 更柔和的背景色
                    "hover": (50, 50, 70),   # 悬停时更亮
                    "pressed": (30, 30, 50)  # 点击时更暗
                })

                # 为按钮保存药水数据
                potion_button.potion_data = potion
                potion_button.text_color = color

                # 添加按钮到界面
                self.add_component(potion_button)

            # 更新垂直位置，为下一个分类留出空间
            current_y += rows_needed * (button_height + button_spacing_v) + 15

        # 更新详情面板可见性
        self.detail_panel.visible = False

        # 更新滚动面板的高度，确保可以滚动
        total_items = len(self.potion_items)
        total_height = total_items * (button_height + button_spacing_v) + button_spacing_v
        if hasattr(self.potion_button_area, 'content_height'):
            self.potion_button_area.content_height = total_height

        logger.info(f"成功创建 {len(self.potion_items)} 个药水按钮")

    def handle_event(self, event):
        """处理事件，特别是进度条拖拽"""
        # 调用父类的事件处理
        super().handle_event(event)

        # 处理进度条拖拽
        if hasattr(self, 'slider_handle') and self.slider_handle:
            # 鼠标按下事件 - 开始拖拽
            if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # 左键点击
                mouse_pos = pygame.mouse.get_pos()
                if self.slider_handle.rect.collidepoint(mouse_pos):
                    self.slider_dragging = True
                    logger.debug("开始拖拽进度条")

            # 鼠标释放事件 - 结束拖拽
            elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:  # 左键释放
                if self.slider_dragging:
                    self.slider_dragging = False
                    logger.debug("结束拖拽进度条")

            # 鼠标移动事件 - 更新进度条位置
            elif event.type == pygame.MOUSEMOTION and self.slider_dragging:
                mouse_pos = pygame.mouse.get_pos()
                # 限制滑块在进度条范围内移动
                new_x = max(self.slider_min_x, min(mouse_pos[0] - self.slider_handle_width // 2, self.slider_max_x))

                # 更新滑块位置
                self.slider_handle.rect.x = new_x

                # 计算当前数量
                slider_range = self.slider_max_x - self.slider_min_x
                if slider_range > 0:
                    progress = (new_x - self.slider_min_x) / slider_range
                    self.current_quantity = max(1, min(int(progress * self.max_quantity), self.max_quantity))

                    # 更新数量显示
                    if hasattr(self, 'quantity_text') and self.quantity_text and hasattr(self, 'quantity_selector_item'):
                        item_price = self.quantity_selector_item.get('price', 0)
                        currency = self.quantity_selector_item.get('currency', 'gold')
                        currency_text = "元宝" if currency == "yuanbao" else "金币"
                        total_price = item_price * self.current_quantity
                        self.quantity_text.set_text(f"数量: {self.current_quantity}  总价: {total_price} {currency_text}")
                        logger.debug(f"更新数量: {self.current_quantity}, 总价: {total_price}")

                # 防止滑块超出范围
                if self.slider_handle.rect.x < self.slider_min_x:
                    self.slider_handle.rect.x = self.slider_min_x
                elif self.slider_handle.rect.x > self.slider_max_x:
                    self.slider_handle.rect.x = self.slider_max_x
