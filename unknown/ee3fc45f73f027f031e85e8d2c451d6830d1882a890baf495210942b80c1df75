import os
import sys
import json
import time
import pygame
import traceback
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
from ui.components import Panel, Button, Text, ScrollableList
from ui.ui_manager import Screen
from core.game import Game
from core.player import Player
from core.config import GameConfig
from utils.resource_manager import resources
import inspect
import shutil

# 添加缺失的 logging 导入
import logging

# 移除旧的常量
# SAVE_DIR = "data/saves"

logger = logging.getLogger(__name__)

class LoadGame(Screen):
    """游戏加载界面"""
    
    def __init__(self, ui_manager, game_manager, app_instance):
        """
        初始化加载游戏界面
        
        参数:
            ui_manager: UI管理器实例
            game_manager: 游戏管理器实例 (core.game.Game)
            app_instance: 游戏应用实例 (main.GameApp)
        """
        super().__init__("load_game")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.app_instance = app_instance
        self.save_files = []
        self.selected_save = None
        
        self._create_components()
    
    def _create_components(self):
        """创建界面组件"""
        screen_size = pygame.display.get_surface().get_size()
        
        # 顶部面板
        top_panel_height = 60
        top_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(top_panel)
        
        # 标题文本
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            "加载游戏",
            "chinese_title",
            (220, 220, 220),
            "center"
        )
        self.add_component(title_text)
        
        # 返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 10, 100, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)
        # 确保返回按钮一直可点击
        back_button.set_active(True)
        back_button.force_active_render(True)
        
        # 主内容区域
        panel_width = min(800, screen_size[0] - 40)
        panel_height = min(600, screen_size[1] - top_panel_height - 40)
        panel_rect = pygame.Rect(
            (screen_size[0] - panel_width) // 2,
            top_panel_height + 20,
            panel_width,
            panel_height
        )
        main_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 50),  # 深色背景
            border_color=(60, 60, 90),  # 边框颜色
            border_width=2,
            alpha=180  # 半透明
        )
        self.add_component(main_panel)
        
        # 存档列表区域
        list_height = panel_height - 200
        list_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + 20,
            panel_width - 40,
            list_height
        )
        
        # 创建存档列表面板
        self.save_list_panel = Panel(
            list_rect,
            color=(30, 30, 50),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(self.save_list_panel)
        logger.info(f"创建存档列表面板: {list_rect}")
        
        # 初始化存档按钮列表
        self.save_buttons = []
        
        # 存档信息面板
        info_panel_rect = pygame.Rect(
            panel_rect.x + 20,
            panel_rect.y + panel_height - 80,
            panel_width - 40,
            60
        )
        self.info_panel = Panel(
            info_panel_rect,
            color=(30, 30, 50),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(self.info_panel)
        
        # 存档信息文本
        self.info_text = self.ui_manager.create_text(
            pygame.Rect(
                info_panel_rect.x + 10,
                info_panel_rect.y + 10,
                info_panel_rect.width - 20,
                info_panel_rect.height - 20
            ),
            "请选择一个存档文件",
            "chinese_normal",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.info_text)
        
        # 加载按钮
        load_btn_rect = pygame.Rect(
            panel_rect.x + 150,
            panel_rect.y + panel_height - 140,
            120,
            40
        )
        self.load_btn = self.ui_manager.create_button(
            load_btn_rect,
            "加载",
            self._on_load_click,
            "chinese_large"
        )
        self.add_component(self.load_btn)
        # 强制按钮激活
        self.load_btn.set_active(True)
        self.load_btn.force_active_render(True)
        
        # 删除按钮
        delete_btn_rect = pygame.Rect(
            panel_rect.x + panel_width // 2 - 60,
            panel_rect.y + panel_height - 140,
            120,
            40
        )
        self.delete_btn = self.ui_manager.create_button(
            delete_btn_rect,
            "删除",
            self._on_delete_click,
            "chinese_large"
        )
        self.add_component(self.delete_btn)
        # 强制按钮激活
        self.delete_btn.set_active(True)
        self.delete_btn.force_active_render(True)
        
        # 添加修复存档按钮
        repair_btn_rect = pygame.Rect(
            panel_rect.x + panel_width - 270,
            panel_rect.y + panel_height - 140,
            120,
            40
        )
        self.repair_btn = self.ui_manager.create_button(
            repair_btn_rect,
            "修复存档",
            self._on_repair_click,
            "chinese_large"
        )
        self.add_component(self.repair_btn)
        # 强制按钮激活
        self.repair_btn.set_active(True)
        self.repair_btn.force_active_render(True)
        
        # 刷新存档列表
        self._refresh_save_list()
    
    def _get_save_game_dir(self) -> Path:
        """获取并确保角色存档目录存在 (与core/game.py中定义一致)"""
        try:
            home_dir = Path.home()
            app_data_dir = home_dir / ".萝卜放置传奇"
            save_dir = app_data_dir / "saves"
            save_dir.mkdir(parents=True, exist_ok=True)
            return save_dir
        except Exception as e:
            logger.error(f"创建或获取角色存档目录失败: {e}")
            fallback_dir = Path("data") / "saves_fallback"
            fallback_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"角色存档将保存到回退目录: {fallback_dir.resolve()}")
            return fallback_dir

    def _list_save_files(self):
        """列出所有存档文件"""
        save_dir = self._get_save_game_dir()
        logger.info(f"开始扫描存档目录: {save_dir}")
        save_files_info = []
        
        try:
            # 使用 pathlib 查找文件
            potential_files = list(save_dir.glob('*.json')) + list(save_dir.glob('*.save'))
            save_files = [f for f in potential_files if f.is_file() and not f.name.endswith('.bak')]
            
            if not save_files:
                logger.info("未找到存档文件")
                return []
            
            # 按修改时间排序
            save_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for file_path in save_files:
                try:
                    logger.info(f"尝试加载存档: {file_path}")
                    # 读取文件内容
                    content = file_path.read_bytes()
                    if not content:
                        logger.warning(f"存档文件为空: {file_path}")
                        continue
                        
                    # 尝试解码
                    save_text = None
                    file_encoding = 'utf-8' # 默认编码
                    for encoding in ['utf-8', 'latin1', 'cp936', 'gbk']:
                        try:
                            save_text = content.decode(encoding)
                            file_encoding = encoding
                            logger.info(f"使用 {encoding} 编码成功加载存档: {file_path.name}")
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if save_text is None:
                        logger.warning(f"无法解码存档文件: {file_path}")
                        continue
                        
                    # 解析JSON
                    save_data = json.loads(save_text)
                    
                    # 检查是否是有效存档（至少包含player信息或旧格式的关键信息）
                    if not isinstance(save_data, dict) or \
                       ('player' not in save_data and not any(k in save_data for k in ['name', 'level', 'character_class'])):
                       logger.warning(f"存档文件格式无效或缺少关键信息: {file_path}")
                       continue
                       
                    # 提取必要信息
                    save_info = save_data.copy() # 复制一份，避免修改原始加载的数据
                    save_info['filename'] = str(file_path) # 存储完整路径
                    save_info['modified_time'] = file_path.stat().st_mtime
                    save_info['encoding'] = file_encoding # 记录成功解码的编码
                    save_files_info.append(save_info)
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析错误: {file_path}, 错误: {e}")
                except Exception as e:
                    logger.error(f"处理存档文件时出错: {file_path}, 错误: {e}")
            
            logger.info(f"成功加载 {len(save_files_info)} 个存档文件")
            return save_files_info
            
        except Exception as e:
            logger.error(f"扫描存档目录时出错: {save_dir}, 错误: {e}")
            return []

    def _on_save_selected(self, save_data):
        """当用户点击存档列表中的项时调用"""
        self.selected_save = save_data
        logger.info(f"选择存档: {save_data}")
        
        # 获取信息用于显示
        player_data = save_data.get('player', {})
        name = player_data.get('name', '未知')
        level = player_data.get('level', 1)
        char_class = player_data.get('character_class', '未知职业')
        save_time_str = save_data.get('save_time', '未知时间')
        
        # 确保datetime已导入
        try:
            # 格式化时间戳
            save_timestamp = save_data.get('modified_time')
            if save_timestamp:
                 save_dt = datetime.fromtimestamp(save_timestamp)
                 save_time_str = save_dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 尝试解析旧的 save_time 字符串
                save_dt = datetime.strptime(save_time_str, "%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError, ImportError, NameError) as e:
             logger.warning(f"时间格式化失败: {e}")
             pass # 如果解析失败，保留原始字符串
        
        info = f"角色: {name} ({char_class} Lv.{level})\n上次保存: {save_time_str}"
        self.info_text.set_text(info)
        
        # 激活加载和删除按钮
        self.load_btn.set_active(True)
        self.load_btn.force_active_render(True)
        self.delete_btn.set_active(True)
        self.delete_btn.force_active_render(True)
        self.repair_btn.set_active(True)
        self.repair_btn.force_active_render(True)
        logger.info(f"已选择存档文件: {save_data.get('filename')}")
        logger.info("已激活所有按钮")

    def _on_load_click(self):
        """处理加载按钮点击"""
        logger.info("加载按钮被点击")
        if not self.selected_save:
            logger.warning("尝试加载，但未选择存档")
            return
            
        # 获取文件名
        filename = self.selected_save.get('filename')
        if not filename or not os.path.exists(filename):
            logger.warning(f"存档文件不存在: {filename}")
            self.ui_manager.show_message("错误", "所选存档文件不存在或已被删除", None)
            self._refresh_save_list()
            return
            
        logger.info(f"准备加载存档文件: {filename}")
        logger.info(f"存档数据结构: {list(self.selected_save.keys())}")
        
        try:
            # 读取存档文件
            try:
                with open(filename, 'rb') as f:
                    content = f.read()
                
                # 尝试解码
                save_text = None
                for encoding in ['utf-8', 'latin1', 'cp936', 'gbk']:
                    try:
                        save_text = content.decode(encoding)
                        logger.info(f"成功用{encoding}编码解析存档文件")
                        break
                    except UnicodeDecodeError:
                        continue
                
                if not save_text:
                    logger.error("无法解码存档文件")
                    self.ui_manager.show_message("错误", "无法解码存档文件，文件可能已损坏", None)
                    return
                    
                # 解析JSON
                save_data = json.loads(save_text)
                logger.info(f"存档数据包含的键: {', '.join(save_data.keys())}")
                
                # 导入版本管理模块检查版本兼容性
                try:
                    from core.version import GAME_VERSION, is_compatible
                    
                    save_version = save_data.get("version", "1.0")
                    logger.info(f"存档版本: {save_version}, 游戏版本: {GAME_VERSION}")
                    
                    if not is_compatible(save_version, GAME_VERSION):
                        logger.error(f"存档版本({save_version})高于当前游戏版本({GAME_VERSION})，无法加载")
                        self.ui_manager.show_message(
                            "版本不兼容",
                            f"存档版本({save_version})高于当前游戏版本({GAME_VERSION})，无法加载。\n\n请升级游戏或使用较低版本的存档。",
                            None
                        )
                        return
                    
                    # 如果版本低于当前版本，在加载前更新存档版本
                    if save_version != GAME_VERSION:
                        logger.info(f"存档版本({save_version})低于当前游戏版本({GAME_VERSION})，将更新存档版本")
                        save_data["version"] = GAME_VERSION
                        
                        # 可能需要执行数据迁移代码（根据版本差异）
                        # ... 数据迁移代码 ...
                        
                        # 将更新后的数据写回文件（备份原文件）
                        backup_filename = f"{filename}.bak.{int(time.time())}"
                        try:
                            # 备份原始文件
                            shutil.copy2(filename, backup_filename)
                            logger.info(f"已备份原始存档到: {backup_filename}")
                            
                            # 写入更新后的数据
                            with open(filename, 'w', encoding='utf-8') as f:
                                json.dump(save_data, f, ensure_ascii=False, indent=2)
                            logger.info(f"已将更新后的存档写入文件: {filename}")
                        except Exception as e:
                            logger.warning(f"更新存档文件失败: {e}")
                            # 继续处理，不中断加载流程
                except ImportError:
                    logger.warning("无法导入版本管理模块，跳过版本检查")
                except Exception as e:
                    logger.error(f"版本检查过程中出现错误: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                self.ui_manager.show_message("错误", f"解析存档文件失败: {e}", None)
                return
            except Exception as e:
                logger.error(f"读取存档文件时出错: {e}")
                self.ui_manager.show_message("错误", f"读取存档文件时出错: {e}", None)
                return
                
            # 检查是否是旧版存档格式
            if 'player_data' in save_data and 'player' not in save_data:
                logger.warning("检测到旧版存档格式，尝试转换")
                try:
                    # 转换格式
                    save_data['player'] = save_data.pop('player_data')
                    
                    # 添加必要字段
                    if 'version' not in save_data:
                        save_data['version'] = '1.0'
                    
                    if 'timestamp' not in save_data:
                        save_data['timestamp'] = time.time()
                    
                    if 'save_time' not in save_data:
                        save_data['save_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
                    
                    logger.info("成功转换旧版存档格式")
                except Exception as e:
                    logger.error(f"转换旧版存档格式失败: {e}")
                
                # 将修复后的数据写回文件
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(save_data, f, ensure_ascii=False, indent=2)
                    logger.info("已将旧格式存档转换为新格式并保存")
                except Exception as e:
                    logger.warning(f"保存修复后的存档失败: {e}")
                    # 继续处理，不中断加载流程
            
            # 使用主界面的方法加载游戏
            logger.info("方法1：使用传入的游戏应用实例加载游戏")
            success = False
            try:
                # 检查 self.app_instance 是否存在
                if self.app_instance is None:
                    logger.error("游戏应用实例(app_instance)未传入或为None")
                    # 这里可以尝试使用 game_manager 作为备用，但更推荐修复实例传递
                    logger.warning("尝试使用当前游戏管理器(Game对象)作为备用加载")
                    # 注意: game_manager 是 Game 对象，没有 load_game 或 load_game_from_data 方法
                    # success = self.game_manager.load_game(filename) # 这会失败
                    # 需要调用 Game 对象的内部加载方法，但这通常不应该从UI层直接调用
                    # 更好的做法是确保 app_instance 正确传递
                    raise ValueError("app_instance is None")
                else:
                    logger.info("使用传入的 app_instance 加载游戏")
                    # 首先尝试使用 load_game 方法（传递解析后的数据）
                    if hasattr(self.app_instance, 'load_game') and len(inspect.signature(self.app_instance.load_game).parameters) == 1:
                        logger.info("使用 app_instance.load_game(data) 方法")
                        success = self.app_instance.load_game(save_data)
                    # 尝试使用 load_game_from_data 方法（如果存在）
                    elif hasattr(self.app_instance, 'load_game_from_data'):
                        logger.info("使用 app_instance.load_game_from_data 方法")
                        success = self.app_instance.load_game_from_data(save_data)
                    # 尝试使用 game 对象的 load_game_from_data 方法 (通过 app_instance 访问)
                    elif hasattr(self.app_instance.game, 'load_game_from_data'):
                        logger.info("使用 app_instance.game.load_game_from_data 方法")
                        success = self.app_instance.game.load_game_from_data(save_data)
                    else:
                        logger.warning("传入的游戏应用实例缺少适当的加载方法，检查 GameApp 类定义")
                        # 如果 GameApp 也没有，再尝试 game_manager (Game 对象) 的内部加载
                        if hasattr(self.game_manager, '_load_from_data'): # 假设Game对象有内部加载方法
                           logger.warning("尝试通过 game_manager._load_from_data 加载")
                           success = self.game_manager._load_from_data(save_data)
                           if success:
                               self.ui_manager.show_screen("game") # 如果Game对象加载成功，切换屏幕
                               return
                        else:
                           logger.error("所有尝试的加载方法均未找到")
                
                logger.info(f"方法1结果: {success}")
                if success:
                    logger.info(f"成功加载存档: {filename}")
                    # 切换屏幕的操作应该由 GameApp.load_game/load_game_from_data 内部处理
                    # 或者在这里显式调用
                    # self.ui_manager.show_screen("game") # 确认 GameApp 方法是否处理了切换
                    return # 加载成功后直接返回
            except Exception as e:
                logger.error(f"使用传入的 app_instance 加载存档失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # 不要在这里尝试备用方法，因为 app_instance 失败通常意味着更严重的问题

            # 如果使用 app_instance 加载失败 (success is False)
            logger.error(f"使用 app_instance 加载失败: {filename}")
            # 移除之前使用 game_manager 加载的备用逻辑，因为它不应该从这里调用
            # logger.info("备用方法：使用游戏对象的load_game方法")
            # success = self.game_manager.load_game(filename) # Game 对象没有 load_game(filename)
            # logger.info(f"备用方法结果: {success}")
            
            # 如果所有方法都失败，显示修复选项
            logger.error(f"所有主要加载方法均失败: {filename}")
            self.ui_manager.show_confirmation(
                "加载失败",
                "无法加载存档文件，可能已损坏或有编码问题。\n\n是否尝试修复此存档文件？",
                lambda: self._start_repair_save(filename),
                None
            )
        except Exception as e:
            logger.error(f"加载存档过程中出现异常: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            self.ui_manager.show_message("错误", f"加载存档时出错: {str(e)}", None)
    
    def _on_delete_click(self):
        """删除所选存档"""
        if not self.selected_save:
            logger.warning("尝试删除，但未选择存档")
            return
            
        try:
            # 从选中的存档信息中获取完整路径
            filename = self.selected_save.get('filename')
            if not filename:
                 logger.error("选中的存档信息缺少文件名")
                 return
                 
            file_path = Path(filename)
            save_dir = self._get_save_game_dir()
            
            # 安全性检查：确保文件确实在存档目录下
            if not str(file_path.resolve()).startswith(str(save_dir.resolve())):
                 logger.error(f"尝试删除非存档目录中的文件: {file_path}")
                 self.ui_manager.show_message("错误", "无法删除指定文件 (路径错误)")
                 return

            if not file_path.exists():
                logger.warning(f"尝试删除的文件不存在: {file_path}")
                self.ui_manager.show_message("错误", "存档文件已被删除或移动")
                self._refresh_save_list()
                return
                
            logger.info(f"准备删除存档: {file_path}")
            
            # 确认对话框
            message = f"确定要删除存档 \n{file_path.name} \n({self.info_text.text}) 吗? \n此操作无法撤销！"
            self.ui_manager.show_confirmation(
                 "确认删除",
                 message,
                 lambda: self._confirm_delete(file_path),
                 lambda: logger.info("用户取消删除")
             )

        except Exception as e:
            logger.error(f"删除操作准备失败: {str(e)}")
            self.ui_manager.show_message("错误", f"删除操作失败: {str(e)}")

    def _confirm_delete(self, file_path: Path):
         """确认并执行删除操作"""
         try:
            # 执行删除
            file_path.unlink() # 使用unlink删除文件
            logger.info(f"成功删除存档: {file_path}")
            
            # 清除选择状态并刷新列表
            self.selected_save = None
            self._refresh_save_list()
            self.info_text.set_text("存档已删除")
            
            # 强制激活所有按钮 (删除后可能列表为空，但按钮本身应可用)
            self.load_btn.set_active(True)
            self.load_btn.force_active_render(True)
            self.delete_btn.set_active(True)
            self.delete_btn.force_active_render(True)
            self.repair_btn.set_active(True)
            self.repair_btn.force_active_render(True)
            
            logger.info("界面刷新完成，所有按钮已重新激活")
            self.ui_manager.show_message("删除成功", f"存档 {file_path.name} 已被删除")
            
         except Exception as e:
            logger.error(f"删除存档文件时出错: {file_path}, 错误: {e}")
            self.ui_manager.show_message("删除失败", f"删除存档时出错: {e}")
    
    def _on_back_click(self):
        """处理返回按钮点击"""
        self.ui_manager.show_screen("main_menu")
    
    def show(self):
        """显示界面时加载存档列表"""
        logger.info("显示加载游戏界面")
        super().show()
        
        # 重置选择状态
        self.selected_save = None
        self.info_text.set_text("请选择一个存档文件")
        
        # 确保按钮可见
        self.load_btn.visible = True
        self.delete_btn.visible = True
        self.repair_btn.visible = True
        
        # 强制激活所有按钮
        self.load_btn.set_active(True)
        self.load_btn.force_active_render(True)
        self.delete_btn.set_active(True)
        self.delete_btn.force_active_render(True)
        self.repair_btn.set_active(True)
        self.repair_btn.force_active_render(True)
        
        # 刷新存档列表
        self._refresh_save_list()
        
        # 设置列表项选择回调
        if hasattr(self.save_list_panel, '_on_item_clicked'):
            old_callback = self.save_list_panel._on_item_clicked
            logger.info(f"当前回调类型: {type(old_callback)}")
        
        self.save_list_panel._on_item_clicked = self._on_save_selected
        logger.info("设置存档选择回调")
        
        # 强制一次更新来确保UI组件状态正确
        pygame.event.post(pygame.event.Event(pygame.USEREVENT))
    
    def activate(self):
        """激活界面时设置回调"""
        super().activate()
        logger.info("激活加载游戏界面")
        
        # 设置列表项选择回调
        if hasattr(self.save_list_panel, '_on_item_clicked'):
            old_callback = self.save_list_panel._on_item_clicked
            logger.info(f"当前回调类型: {type(old_callback)}")
        
        self.save_list_panel._on_item_clicked = self._on_save_selected
        logger.info("设置存档选择回调")
        
        # 强制激活所有按钮
        self.load_btn.set_active(True)
        self.load_btn.force_active_render(True)
        self.delete_btn.set_active(True)
        self.delete_btn.force_active_render(True)
        self.repair_btn.set_active(True)
        self.repair_btn.force_active_render(True)
        
        # 更新界面
        if self.selected_save:
            self._on_save_selected(self.selected_save)
    
    def _on_repair_click(self):
        """处理修复存档按钮点击"""
        # 检查是否有选中的存档
        if self.selected_save:
            filename = self.selected_save.get("filename")
            if filename:
                self._start_repair_save(filename)
                return
        
        # 检查是否有选中的存档
        if self.selected_save:
            filename = self.selected_save.get("filename")
            if filename:
                self._start_repair_save(filename)
                return
        
        # 如果没有选中存档，询问是否修复所有存档
        self.ui_manager.show_message(
            "修复存档",
            "是否尝试修复所有存档文件？\n这将检查并修复可能的编码问题。",
            [
                {
                    "text": "修复所有", 
                    "action": self._repair_all_saves
                },
                {
                    "text": "取消", 
                    "action": None
                }
            ]
        )
    
    def _start_repair_save(self, filename):
        """开始修复存档文件 (确保使用完整路径)"""
        logger.info(f"开始尝试修复存档文件: {filename}")
        # 确保 filename 是 Path 对象
        file_path = Path(filename)
        save_dir = self._get_save_game_dir()

        # 安全性检查
        if not str(file_path.resolve()).startswith(str(save_dir.resolve())):
             logger.error(f"尝试修复非存档目录中的文件: {file_path}")
             self.ui_manager.show_message("错误", "无法修复指定文件 (路径错误)")
             return
             
        if not file_path.exists():
             logger.warning(f"尝试修复的文件不存在: {file_path}")
             self.ui_manager.show_message("错误", "无法找到需要修复的存档文件")
             return

        # ... (修复逻辑，使用 file_path) ...
        try:
             # 备份原文件
             backup_path = file_path.with_suffix(file_path.suffix + '.bak_repair')
             try:
                 shutil.copy2(file_path, backup_path)
                 logger.info(f"已创建修复前的备份: {backup_path}")
             except Exception as copy_e:
                 logger.warning(f"创建修复备份失败: {copy_e}，将继续尝试修复")

             # 读取文件内容
             content = file_path.read_bytes()
             repaired_data = None
             used_encoding = None

             # 尝试不同编码解码
             for encoding in ['utf-8', 'gbk', 'latin1', 'cp936']:
                 try:
                     text = content.decode(encoding)
                     # 尝试解析 JSON (基本检查)
                     json.loads(text) 
                     # 如果解码和初步解析成功，认为编码正确
                     repaired_data = json.loads(text) # 重新解析以获取数据
                     used_encoding = encoding
                     logger.info(f"使用 {encoding} 成功解码并解析JSON")
                     break
                 except UnicodeDecodeError:
                     continue
                 except json.JSONDecodeError:
                     logger.warning(f"使用 {encoding} 解码后JSON解析失败")
                     # 尝试修复常见的JSON错误 (例如末尾逗号)，但这很复杂，暂时跳过
                     continue 
                 except Exception as load_e:
                     logger.warning(f"使用 {encoding} 加载时发生其他错误: {load_e}")
                     continue

             if repaired_data is None:
                 logger.error("无法使用任何已知编码成功解析JSON数据")
                 self.ui_manager.show_message("修复失败", "文件编码错误或JSON结构严重损坏")
                 return

             # (可选) 进行更深入的数据结构验证和修复
             # 例如，检查 player 字段，添加缺失的默认值等
             # ...

             # 写回修复后的文件 (使用原始成功解码的编码或UTF-8)
             write_encoding = used_encoding if used_encoding else 'utf-8'
             try:
                 file_path.write_text(json.dumps(repaired_data, ensure_ascii=False, indent=2), encoding=write_encoding)
                 logger.info(f"成功将修复后的数据写回文件: {file_path} (使用 {write_encoding} 编码)")
                 self.ui_manager.show_message("修复成功", f"存档 {file_path.name} 可能已修复。\n请尝试重新加载。")
                 self._refresh_save_list() # 刷新列表以更新信息
             except Exception as write_e:
                 logger.error(f"写入修复后的存档失败: {write_e}")
                 self.ui_manager.show_message("修复失败", f"写入修复后的文件时出错: {write_e}")
                 # 尝试恢复备份
                 if backup_path.exists():
                     try:
                         shutil.move(backup_path, file_path)
                         logger.info("已从修复备份恢复原文件")
                     except Exception as restore_e:
                         logger.error(f"恢复修复备份失败: {restore_e}")

        except Exception as e:
            logger.error(f"修复存档时发生意外错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.ui_manager.show_message("修复失败", f"修复过程中发生未知错误: {e}")
    
    def _repair_all_saves(self):
        """修复所有存档文件"""
        try:
            # 构建存档目录路径
            save_dir = "data/saves"
            
            if not os.path.exists(save_dir):
                self.ui_manager.show_message("信息", "存档目录不存在。")
                return
            
            # 获取所有JSON文件
            save_files = [f for f in os.listdir(save_dir) if f.endswith(".json")]
            
            if not save_files:
                self.ui_manager.show_message("信息", "未找到存档文件。")
                return
            
            # 创建进度对话框
            self.ui_manager.show_message(
                "正在修复",
                f"正在修复 {len(save_files)} 个存档文件，请稍候...",
                None
            )
            
            # 修复每个文件
            success_count = 0
            for filename in save_files:
                try:
                    self._start_repair_save(filename)
                    success_count += 1
                except Exception as e:
                    logger.error(f"修复存档 {filename} 失败: {e}")
            
            # 显示结果
            self.ui_manager.show_message(
                "修复完成",
                f"成功修复 {success_count}/{len(save_files)} 个存档文件。",
                None
            )
            
            # 刷新列表
            self._refresh_save_list()
            
        except Exception as e:
            logger.error(f"批量修复存档时出错: {e}")
            self.ui_manager.show_message("错误", f"修复过程中出错: {str(e)}")
    

    def _refresh_save_list(self):
        """刷新存档列表"""
        logger.info("开始刷新存档列表")
        
        # 清空现有的存档按钮
        for button in self.save_buttons:
            self.remove_component(button)
        self.save_buttons = []
        
        # 重置选择状态
        self.selected_save = None
        self.info_text.set_text("请选择一个存档文件")
        
        # 强制激活所有按钮
        self.load_btn.set_active(True)
        self.load_btn.force_active_render(True)
        self.delete_btn.set_active(True)
        self.delete_btn.force_active_render(True)
        self.repair_btn.set_active(True)
        self.repair_btn.force_active_render(True)
        
        # 重新加载存档文件
        self.save_files = self._list_save_files()
        
        if not self.save_files:
            logger.warning("没有找到存档文件")
            # 创建一个空项说明
            empty_text = self.ui_manager.create_text(
                pygame.Rect(
                    self.save_list_panel.rect.x + 20,
                    self.save_list_panel.rect.y + 20,
                    self.save_list_panel.rect.width - 40,
                    40
                ),
                "没有找到存档文件",
                "chinese_normal",
                (200, 200, 200),
                "center"
            )
            self.add_component(empty_text)
            self.save_buttons.append(empty_text)
            
            self.info_text.set_text("没有可用的存档文件")
            return
        
        # 添加存档按钮
        button_height = 50
        button_margin = 5
        current_y = self.save_list_panel.rect.y + 10
        
        for i, save in enumerate(self.save_files):
            try:
                # 检查 save 是否为 None
                if save is None:
                    logger.error(f"存档 #{i} 为None，跳过")
                    continue
                
                # 从player对象中提取数据
                player_data = save.get("player", {})
                if player_data is None:
                    player_data = {}
                    logger.warning(f"存档 #{i} 的player_data为None，使用空字典")
                
                # 格式化显示文本
                character_class = player_data.get("character_class", "未知")
                level = player_data.get("level", 0)
                modified_time = save.get("modified_time", 0)
                
                # 尝试获取文件名
                filename = save.get("filename", "未知存档")
                if isinstance(filename, str):
                    base_filename = os.path.basename(filename)
                else:
                    base_filename = "未知存档"
                
                # 转换时间戳为可读格式
                time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(modified_time))
                
                # 创建按钮的显示文本
                display_text = f"{base_filename} - {character_class} Lv.{level} - {time_str}"
                logger.info(f"添加存档按钮: {display_text}")
                
                # 按钮位置和大小
                button_rect = pygame.Rect(
                    self.save_list_panel.rect.x + 10,
                    current_y,
                    self.save_list_panel.rect.width - 20,
                    button_height
                )
                
                # 创建闭包函数以保留当前存档数据的引用
                def create_click_handler(save_data):
                    def click_handler():
                        self._on_save_selected(save_data)
                    return click_handler
                
                # 创建存档按钮
                save_button = self.ui_manager.create_button(
                    button_rect,
                    display_text,
                    create_click_handler(save),
                    "chinese_small"
                )
                
                # 设置按钮颜色和样式
                save_button.colors.update({
                    "normal": (40, 40, 60),
                    "hover": (50, 50, 70),
                    "pressed": (30, 30, 50),
                    "text": (200, 200, 200),
                    "border": (70, 70, 90)
                })
                
                # 强制按钮为活跃状态
                save_button.set_active(True)
                save_button.force_active_render(True)
                
                # 添加到组件和存档按钮列表
                self.add_component(save_button)
                self.save_buttons.append(save_button)
                
                # 更新下一个按钮的Y坐标
                current_y += button_height + button_margin
                
            except Exception as e:
                logger.error(f"添加存档按钮时出错: {e}")
                
                # 创建错误存档按钮
                error_text = ""
                if "filename" in save:
                    error_text = f"[错误的存档] {os.path.basename(save['filename'])}"
                else:
                    error_text = "[错误的存档]"
                    
                button_rect = pygame.Rect(
                    self.save_list_panel.rect.x + 10,
                    current_y,
                    self.save_list_panel.rect.width - 20,
                    button_height
                )
                
                # 创建错误存档按钮
                error_button = self.ui_manager.create_button(
                    button_rect,
                    error_text,
                    lambda: self._on_save_selected(save),
                    "chinese_small"
                )
                
                # 设置错误样式
                error_button.colors.update({
                    "normal": (60, 40, 40),
                    "hover": (70, 50, 50),
                    "pressed": (50, 30, 30),
                    "text": (220, 180, 180),
                    "border": (90, 70, 70)
                })
                
                # 强制按钮为活跃状态
                error_button.set_active(True)
                error_button.force_active_render(True)
                
                # 添加到组件和存档按钮列表
                self.add_component(error_button)
                self.save_buttons.append(error_button)
                
                # 更新下一个按钮的Y坐标
                current_y += button_height + button_margin
        
        # 强制更新UI
        pygame.event.post(pygame.event.Event(pygame.USEREVENT))
        
        logger.info(f"存档列表刷新完成，共加载 {len(self.save_files)} 个存档")
    
    def update(self, dt):
        """更新界面状态"""
        super().update(dt)
        
        # 确保按钮始终处于活跃状态
        if hasattr(self, 'load_btn'):
            self.load_btn.set_active(True)
            self.load_btn.force_active_render(True)
        if hasattr(self, 'delete_btn'):
            self.delete_btn.set_active(True)
            self.delete_btn.force_active_render(True)
        if hasattr(self, 'repair_btn'):
            self.repair_btn.set_active(True)
            self.repair_btn.force_active_render(True)
            
        # 确保所有存档按钮也是活跃的
        for button in self.save_buttons:
            if isinstance(button, Button):
                button.set_active(True)
                button.force_active_render(True)

    def _load_from_data(self, save_data):
        """直接从解析好的存档数据加载游戏
        
        当Game类没有提供_load_from_data方法时使用这个方法
        这个方法会尝试将已解析的数据传递给game.load_game方法
        
        参数:
            save_data: 已解析的存档数据字典
            
        返回:
            bool: 是否成功加载
        """
        try:
            logger.info("使用内部方法直接加载解析好的数据")
            
            # 打印详细信息
            if isinstance(save_data, dict):
                logger.info(f"数据是字典类型，包含键: {', '.join(save_data.keys())}")
                player_data = {} # 初始化player_data变量
                if 'player' in save_data:
                    player_data = save_data['player']
                    if isinstance(player_data, dict):
                        logger.info(f"玩家数据包含键: {', '.join(player_data.keys())}")
                    else:
                        logger.warning(f"玩家数据不是字典类型: {type(player_data)}")
            else:
                logger.warning(f"数据不是字典类型: {type(save_data)}")
                player_data = {} # 确保player_data变量被定义
            
            # 创建临时JSON文件
            temp_dir = os.path.join("data", "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_file = os.path.join(temp_dir, f"temp_save_{int(time.time())}.json")
            
            # 将数据写入临时文件
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已将解析好的数据写入临时文件: {temp_file}")
            
            # 尝试使用临时文件加载游戏
            success = self.game_manager.load_game(temp_file)
            
            # 删除临时文件
            try:
                os.remove(temp_file)
                logger.info("已删除临时文件")
            except Exception as e:
                logger.warning(f"无法删除临时文件: {temp_file}, 错误: {e}")
                
            return success
            
        except Exception as e:
            logger.error(f"直接加载数据出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False 