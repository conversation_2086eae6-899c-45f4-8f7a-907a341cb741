import pygame
import logging
from copy import deepcopy
from ui.components import Panel, Button, Text, UIComponent
from ui.ui_manager import Screen
import random

# 设置日志记录器
logger = logging.getLogger(__name__)

class EquipmentScreen(Screen):
    """装备界面类"""

    def __init__(self, ui_manager, game_manager):
        """初始化装备界面"""
        super().__init__("equipment")
        logger.info("初始化装备界面")

        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.title = "角色装备"

        # 背包中当前选中的装备
        self.selected_equipment = None

        # 装备槽位
        self.equipment_slots = {}

        # 创建界面元素
        self._create_ui_elements()
        logger.info("装备界面初始化完成")

    def _create_ui_elements(self):
        """创建界面元素"""
        logger.info("开始创建装备界面元素")
        screen_size = pygame.display.get_surface().get_size()

        # 顶部面板（标题）
        top_panel_height = 60
        top_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            color=(40, 40, 60),
            border_color=(60, 60, 80),
            border_width=1
        )
        self.add_component(top_panel)

        # 标题文本
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 0, screen_size[0], top_panel_height),
            self.title,
            "chinese_title",
            (220, 220, 220),
            "center"
        )
        self.add_component(title_text)

        # 返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, 10, 100, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)

        # 主内容区域
        content_area = Panel(
            pygame.Rect(20, top_panel_height + 20, screen_size[0] - 40, screen_size[1] - top_panel_height - 80),
            color=(30, 30, 50),
            border_color=(50, 50, 70),
            border_width=1
        )
        self.add_component(content_area)
        self.content_area = content_area

        # 装备区域面板（左侧）
        equipment_panel_width = content_area.rect.width // 2 - 10
        equipment_panel = Panel(
            pygame.Rect(
                content_area.rect.left + 10,
                content_area.rect.top + 10,
                equipment_panel_width,
                content_area.rect.height - 20
            ),
            color=(25, 25, 45),
            border_color=(45, 45, 65),
            border_width=1
        )
        self.add_component(equipment_panel)
        self.equipment_panel = equipment_panel

        # 装备详情面板（右侧）
        equipment_detail_panel = Panel(
            pygame.Rect(
                equipment_panel.rect.right + 20,
                content_area.rect.top + 10,
                equipment_panel_width,
                content_area.rect.height - 20
            ),
            color=(25, 25, 45),
            border_color=(45, 45, 65),
            border_width=1
        )
        self.add_component(equipment_detail_panel)
        self.equipment_detail_panel = equipment_detail_panel

        # 装备区域标题
        equipment_title = self.ui_manager.create_text(
            pygame.Rect(
                equipment_panel.rect.left,
                equipment_panel.rect.top - 30,
                equipment_panel.rect.width,
                30
            ),
            "角色装备",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(equipment_title)

        # 装备详情标题
        equipment_detail_title = self.ui_manager.create_text(
            pygame.Rect(
                equipment_detail_panel.rect.left,
                equipment_detail_panel.rect.top - 30,
                equipment_detail_panel.rect.width,
                30
            ),
            "装备详情",
            "chinese_normal",
            (200, 200, 200),
            "center"
        )
        self.add_component(equipment_detail_title)

        # 创建装备槽位布局
        self._create_equipment_layout()

        # 装备详情组件
        self.equipment_name = self.ui_manager.create_text(
            pygame.Rect(
                equipment_detail_panel.rect.left + 20,
                equipment_detail_panel.rect.top + 20,
                equipment_detail_panel.rect.width - 40,
                30
            ),
            "",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(self.equipment_name)

        self.equipment_description = self.ui_manager.create_text(
            pygame.Rect(
                equipment_detail_panel.rect.left + 20,
                equipment_detail_panel.rect.top + 60,
                equipment_detail_panel.rect.width - 40,
                200
            ),
            "",
            "chinese_small",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.equipment_description)

        # 装备图片面板
        self.equipment_image_panel = Panel(
            pygame.Rect(
                equipment_detail_panel.rect.left + (equipment_detail_panel.rect.width - 100) // 2,
                equipment_detail_panel.rect.top + 270,
                100,
                100
            ),
            color=(20, 20, 40),
            border_color=(40, 40, 60),
            border_width=1
        )
        self.add_component(self.equipment_image_panel)

        # 卸下装备按钮
        self.unequip_button = self.ui_manager.create_button(
            pygame.Rect(
                equipment_detail_panel.rect.left + 20,
                equipment_detail_panel.rect.bottom - 100,
                equipment_detail_panel.rect.width - 40,
                40
            ),
            "卸下装备",
            self._on_unequip_equipment,
            "chinese_normal"
        )
        self.add_component(self.unequip_button)

        logger.info("装备界面元素创建完成")

    def _create_equipment_layout(self):
        """创建装备槽位布局"""
        # 清空现有装备槽位
        for slot in self.equipment_slots.values():
            if hasattr(slot, 'component_id'):  # 如果是UI组件
                self.remove_component(slot)
        self.equipment_slots = {}

        # 计算装备面板位置和尺寸
        panel_width = self.equipment_panel.rect.width
        panel_height = self.equipment_panel.rect.height

        # 设置槽位大小和间距
        slot_size = 70
        slot_spacing = 20
        start_x = self.equipment_panel.rect.left + (panel_width - (slot_size * 5 + slot_spacing * 4)) // 2
        start_y = self.equipment_panel.rect.top + 80

        logger.info(f"创建装备布局: 面板大小({panel_width}x{panel_height}), 槽位大小:{slot_size}, 起始位置:({start_x},{start_y})")

        # 创建装备槽位
        slot_positions = {
            # 第一列(左)
            "左手镯": (start_x, start_y + (slot_size + slot_spacing) * 2),
            "左戒指": (start_x, start_y + (slot_size + slot_spacing) * 3),

            # 第二列(左中)
            "武器": (start_x + slot_size + slot_spacing, start_y + (slot_size + slot_spacing)),
            "勋章": (start_x + slot_size + slot_spacing, start_y),

            # 第三列(中)
            "头盔": (start_x + (slot_size + slot_spacing) * 2, start_y),
            "防具": (start_x + (slot_size + slot_spacing) * 2, start_y + slot_size + slot_spacing),

            # 第四列(右中)
            "项链": (start_x + (slot_size + slot_spacing) * 3, start_y),

            # 第五列(右)
            "右手镯": (start_x + (slot_size + slot_spacing) * 4, start_y + (slot_size + slot_spacing) * 2),
            "右戒指": (start_x + (slot_size + slot_spacing) * 4, start_y + (slot_size + slot_spacing) * 3),
        }

        # 角色面板已移除，因为它没有实际功能

        # 为每个装备槽位创建按钮和标签
        for slot_name, position in slot_positions.items():
            # 设置按钮颜色
            button_colors = {
                "normal": (30, 30, 50),
                "hover": (40, 40, 60),
                "pressed": (20, 20, 40),
                "text": (200, 200, 200),
                "border": (60, 60, 80)
            }

            # 创建装备槽位按钮，确保传递正确的尺寸和位置
            button_rect = pygame.Rect(position[0], position[1], slot_size, slot_size)
            logger.info(f"创建装备槽位按钮: {slot_name}, 位置和大小: {button_rect}")

            # 为当前槽位创建专用的点击处理函数
            # 使用lambda和额外参数来创建闭包，避免所有槽位共享同一个函数引用
            slot_button = self.ui_manager.create_button(
                button_rect,
                "",  # 初始为空文本
                lambda slot=slot_name: self._on_equipment_slot_click(slot),  # 使用lambda参数固定槽位名
                "chinese_small"
            )
            slot_button.colors.update(button_colors)

            # 添加按钮到组件并存储引用
            self.add_component(slot_button)
            self.equipment_slots[slot_name] = slot_button

            # 创建槽位名称标签
            label = self.ui_manager.create_text(
                pygame.Rect(position[0], position[1] - 25, slot_size, 20),
                slot_name,
                "chinese_small",
                (180, 180, 180),
                "center"
            )
            self.add_component(label)
            self.equipment_slots[f"{slot_name}_label"] = label

            logger.info(f"创建装备槽位: {slot_name}, 位置: {position}")

        logger.info(f"装备界面布局创建完成，共 {len(slot_positions)} 个装备槽位")

    def _on_back_click(self):
        """返回按钮点击处理"""
        logger.info("点击返回按钮")
        self.ui_manager.show_screen("game")

    def _on_equipment_slot_click(self, slot_name):
        """处理装备槽位点击"""
        logger.info(f"点击装备槽位: {slot_name}")

        # 忽略已移除的角色面板点击
        if slot_name == "character":
            logger.info("点击了角色面板，但该功能已移除")
            return

        player = self.game_manager.player
        if not player:
            logger.warning("点击装备槽位但玩家不存在")
            return

        # 获取槽位上的装备
        equipment = player.equipment.get(slot_name)

        if equipment:
            # 选中该装备
            self.selected_equipment = equipment
            self._show_equipment_details(equipment, slot_name)
            logger.info(f"选中槽位 {slot_name} 的装备: {equipment.get('name', '未知装备')}")
        else:
            # 清空选择
            self.selected_equipment = None
            self._clear_equipment_details()
            logger.info(f"槽位 {slot_name} 没有装备")

    def _on_unequip_equipment(self):
        """处理卸下装备按钮点击"""
        if not self.selected_equipment:
            logger.info("没有选中任何装备，无法卸下")
            return

        player = self.game_manager.player
        if not player:
            logger.warning("卸下装备但玩家不存在")
            return

        # 获取槽位名
        slot_name = None
        for name, item in player.equipment.items():
            if item is self.selected_equipment:
                slot_name = name
                break

        if not slot_name:
            logger.warning("未找到选中装备的槽位")
            return

        # 卸下装备
        logger.info(f"开始卸下槽位 {slot_name} 的装备: {self.selected_equipment.get('name', '未知装备')}")

        # 保存原始装备的引用，以便出错时恢复
        original_equipment = self.selected_equipment

        try:
            # 将装备放回背包
            equipment_to_unequip = self.selected_equipment  # 保存引用
            equipment_name = equipment_to_unequip.get('name', '未知装备')
            equipment_type = equipment_to_unequip.get('type', '未知类型')

            logger.info(f"卸下装备详细信息 - 名称: {equipment_name}, 类型: {equipment_type}, 完整数据: {equipment_to_unequip}")

            # 保存装备的深拷贝，避免引用问题
            equipment_copy = None
            if isinstance(equipment_to_unequip, dict):
                equipment_copy = deepcopy(equipment_to_unequip) # 使用深拷贝

                # 修复装备类型 - 处理左右手镯和戒指
                if slot_name in ["左手镯", "右手镯"] and equipment_copy.get("type") != "手镯":
                    equipment_copy["type"] = "手镯"
                    logger.info(f"已修正装备类型: {slot_name} -> 手镯")
                elif slot_name in ["左戒指", "右戒指"] and equipment_copy.get("type") != "戒指":
                    equipment_copy["type"] = "戒指"
                    logger.info(f"已修正装备类型: {slot_name} -> 戒指")

                logger.info(f"已创建装备'{equipment_name}'的深拷贝")
            else:
                raise ValueError(f"装备数据无效，无法创建深拷贝: {equipment_to_unequip}")

            # 从equipment中移除装备
            player.equipment[slot_name] = None
            logger.info(f"已从槽位'{slot_name}'移除装备")

            # 确保添加到背包前装备是一个有效的字典
            if equipment_copy and isinstance(equipment_copy, dict) and 'name' in equipment_copy:
                # 将装备的副本添加到背包
                player.inventory.append(equipment_copy)
                logger.info(f"已将装备'{equipment_name}'添加到背包, 当前背包物品数: {len(player.inventory)}")

                # 记录背包中不同类型物品的数量和详情
                item_types_count = {}
                equipment_items = []
                for item in player.inventory:
                    if isinstance(item, dict):
                        item_type = item.get("type", "未知")
                        item_types_count[item_type] = item_types_count.get(item_type, 0) + 1
                        if item.get("name") == equipment_name:
                            equipment_items.append(item)

                logger.info(f"背包物品类型统计: {item_types_count}")
                logger.info(f"背包中'{equipment_name}'装备数量: {len(equipment_items)}")
            else:
                # 如果装备副本无效，恢复原始装备到槽位
                player.equipment[slot_name] = original_equipment
                logger.error(f"无法将装备添加到背包，装备数据无效: {equipment_to_unequip}")
                self.ui_manager.show_message("错误", "装备数据无效，无法卸下")
                return

            # 重新计算玩家属性
            player.recalculate_stats()

            # 刷新装备视图
            self._refresh_equipment_view()

            # 清空装备详情
            self.selected_equipment = None
            self._clear_equipment_details()

            # 预备背包界面的刷新
            if "inventory" in self.ui_manager.screens:
                # 获取背包界面实例
                inventory_screen = self.ui_manager.screens["inventory"]

                # 强制背包在下次显示时更新
                if hasattr(inventory_screen, "current_page"):
                    inventory_screen.current_page = 0
                    logger.info("已重置背包页码为第1页")

                # 强制创建物品槽位
                if hasattr(inventory_screen, "_create_item_slots"):
                    try:
                        inventory_screen._create_item_slots()
                        logger.info("已重新创建背包物品槽位")
                    except Exception as e:
                        logger.error(f"尝试重新创建背包物品槽位时出错: {e}")

                # 立即刷新背包（如果可能）
                if hasattr(inventory_screen, "refresh_inventory"):
                    try:
                        logger.info(f"正在尝试立即刷新背包界面，已卸下装备'{equipment_name}'应该显示在背包中")
                        inventory_screen.refresh_inventory()
                        logger.info(f"背包界面刷新成功，装备'{equipment_name}'已添加到背包")
                    except Exception as e:
                        logger.error(f"尝试刷新背包界面时出错: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

                logger.info(f"已准备背包界面下次打开时刷新，装备'{equipment_name}'将显示在背包中")

            # 显示提示信息
            self.ui_manager.show_message("卸下装备", f"已将装备'{equipment_name}'放回背包")
        except Exception as e:
            # 发生异常时恢复原始装备状态
            player.equipment[slot_name] = original_equipment
            logger.error(f"卸下装备时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.ui_manager.show_message("错误", f"卸下装备失败: {e}")

            # 恢复后重新计算玩家属性
            player.recalculate_stats()
            # 刷新装备视图
            self._refresh_equipment_view()

    def _show_equipment_details(self, equipment, slot_name):
        """显示装备详情"""
        if not equipment:
            self._clear_equipment_details()
            return

        # 设置装备名称
        name = equipment.get("name", "未知装备")

        # 同时检查 quality 和 tier 属性，优先使用 quality
        quality = equipment.get("quality", equipment.get("tier", "普通"))
        quality_colors = {
            "普通": (200, 200, 200),
            "精良": (100, 200, 100),
            "稀有": (100, 100, 255),
            "史诗": (200, 100, 255),
            "传说": (255, 165, 0)
        }
        color = quality_colors.get(quality, (200, 200, 200))
        logger.debug(f"Showing details for: {name} (Slot: {slot_name}), Quality: {quality}, Determined color: {color}")

        self.equipment_name.set_text(f"{name} [{quality}]")
        self.equipment_name.color = color
        logger.debug(f"Set self.equipment_name.color to: {self.equipment_name.color}")

        # 获取装备描述文本
        description = self._get_equipment_description(equipment)
        self.equipment_description.set_text(description)

        # 设置装备图像
        self._set_equipment_image(equipment)

        # 启用卸下按钮
        self.unequip_button.set_active(True)

        logger.info(f"显示装备详情: {name}")

    def _get_attr_display_name(self, attr):
        """获取属性的中文显示名称"""
        attr_names = {
            "attack": "攻击力",
            "magic": "魔法力",
            "taoism": "道术",
            "defense": "防御力",
            "magic_defense": "魔法防御",
            "strength": "力量",
            "agility": "敏捷",
            "vitality": "体力",
            "spirit": "精神",
            "level": "等级",
            "lifesteal": "吸血",
            "flat_lifesteal": "固定吸血"
        }
        return attr_names.get(attr, attr)

    def _clear_equipment_details(self):
        """清空装备详情"""
        self.equipment_name.set_text("")
        self.equipment_description.set_text("")
        self.unequip_button.set_active(False)

        # 清除装备图像，但保留图像面板
        if hasattr(self, 'equipment_image') and self.equipment_image:
            self.remove_component(self.equipment_image)
            self.equipment_image = None

        # 确保装备图像面板仍然存在
        if not hasattr(self, 'equipment_image_panel') or not self.equipment_image_panel:
            logger.warning("装备图像面板不存在，这可能是一个问题")

    def _refresh_equipment_view(self):
        """刷新装备视图"""
        player = self.game_manager.player
        if not player:
            logger.warning("刷新装备视图失败：玩家数据不存在")
            return

        logger.debug("刷新装备视图开始")

        # 检查装备是否有变化
        if not self._check_equipment_changed():
            logger.debug("装备未发生变化，跳过刷新")
            return

        # 更新装备槽位显示
        any_slots_updated = False
        for slot_name, slot_button in self.equipment_slots.items():
            if slot_name.endswith("_label"):  # 跳过标签
                continue

            # 检查slot_button是否为有效UI组件
            if not hasattr(slot_button, 'set_text'):
                logger.warning(f"槽位 {slot_name} 不是有效的UI组件，跳过更新")
                continue

            equipment = player.equipment.get(slot_name)

            if equipment and isinstance(equipment, dict) and "name" in equipment:
                # 设置装备显示
                slot_button.set_text(equipment["name"])
                logger.debug(f"槽位 {slot_name} 已装备: {equipment['name']}")
                any_slots_updated = True

                # 根据装备品质设置不同的背景颜色
                # 同时检查 quality 和 tier 属性，优先使用 quality
                quality = equipment.get("quality", equipment.get("tier", "普通"))

                # 品质边框颜色
                quality_border_colors = {
                    "普通": (160, 160, 200),  # 浅灰色
                    "精良": (100, 200, 100),  # 绿色
                    "稀有": (100, 140, 255),  # 蓝色
                    "史诗": (200, 100, 255),  # 紫色
                    "传说": (255, 165, 0)     # 橙色
                }

                # 使用统一的背景色，通过边框区分品质
                slot_button.colors["normal"] = (30, 30, 50) # 基础背景色
                slot_button.colors["border"] = quality_border_colors.get(quality, (160, 160, 200))
                slot_button.border_width = 2  # 设置边框宽度
            else:
                # 没有装备，显示为空
                slot_button.set_text("未装备")
                slot_button.colors["normal"] = (30, 30, 50)
                slot_button.colors["border"] = (60, 60, 80)
                slot_button.border_width = 1  # 设置更细的边框
                logger.debug(f"槽位 {slot_name} 未装备")

        if not any_slots_updated:
            logger.debug("没有任何装备槽位更新，可能是玩家装备为空")

        logger.debug("装备视图刷新完成")

    def _check_equipment_changed(self):
        """检查装备是否有变化

        Returns:
            bool: 装备是否有变化
        """
        player = self.game_manager.player
        if not player:
            return False

        # 如果没有保存上次的装备状态，则初始化并返回True
        if not hasattr(self, '_last_equipment'):
            self._last_equipment = {}
            for slot_name, equipment in player.equipment.items():
                if equipment and isinstance(equipment, dict):
                    self._last_equipment[slot_name] = equipment.get('name', '')
                else:
                    self._last_equipment[slot_name] = ''
            return True

        # 检查装备是否有变化
        has_changed = False
        current_equipment = {}

        # 获取当前装备状态
        for slot_name, equipment in player.equipment.items():
            if equipment and isinstance(equipment, dict):
                current_equipment[slot_name] = equipment.get('name', '')
            else:
                current_equipment[slot_name] = ''

        # 检查是否与上次状态不同
        if len(current_equipment) != len(self._last_equipment):
            has_changed = True
        else:
            for slot_name, equip_name in current_equipment.items():
                if slot_name not in self._last_equipment or self._last_equipment[slot_name] != equip_name:
                    has_changed = True
                    break

        # 更新保存的装备状态
        if has_changed:
            self._last_equipment = current_equipment

        return has_changed

    def show(self):
        """显示装备界面"""
        logger.info("显示装备界面")
        super().show()

        # 确保选中项为空
        self.selected_equipment = None
        self._clear_equipment_details()

        # 刷新装备视图
        self._refresh_equipment_view()

        logger.info("装备界面显示完成")

    def update(self, dt):
        """更新装备界面"""
        super().update(dt)

    def draw(self, surface):
        """绘制装备界面"""
        super().draw(surface)

    def _get_equipment_description(self, equipment):
        """生成装备描述文本"""
        if not equipment:
            return ""

        # 初始化描述文本
        description = f"{equipment.get('name', '未知装备')}"

        # 修改装备类型显示，增加左右手区分
        eq_type = equipment.get('type', '')
        slot_name = None

        # 查找该装备所在的槽位名称
        for name, item in self.game_manager.player.equipment.items():
            if item is equipment:
                slot_name = name
                break

        # 如果是左右手装备，在类型后添加位置信息
        if slot_name:
            if slot_name in ["左手镯", "右手镯"] and eq_type == "手镯":
                eq_type = f"{slot_name}"
            elif slot_name in ["左戒指", "右戒指"] and eq_type == "戒指":
                eq_type = f"{slot_name}"

        if eq_type:
            description += f" ({eq_type})"

        # 添加等级需求
        if "level" in equipment:
            description += f"\n等级需求: {equipment['level']}"

        # 添加职业需求
        if "class" in equipment:
            description += f"\n职业需求: {equipment['class']}"

        # 添加其他需求
        if "requirements" in equipment:
            description += "\n需求:"
            for req, value in equipment["requirements"].items():
                req_name = {
                    "level": "等级",
                    "attack": "攻击",
                    "magic": "魔法",
                    "taoism": "道术"
                }.get(req, req)

                # 获取玩家的对应属性值
                player_value = 0
                if self.game_manager.player:
                    if req == "level":
                        player_value = self.game_manager.player.level
                    else:
                        player_value = getattr(self.game_manager.player, req, 0)

                # 如果玩家属性不满足需求，显示红色
                if player_value < value:
                    description += f"\n- {req_name}: {player_value}/{value} ❌"
                else:
                    description += f"\n- {req_name}: {player_value}/{value} ✓"

        # 添加基础属性
        description += "\n\n基础属性:"

        # 同时检查 quality_bonus 和 tier_bonus 属性，优先使用 quality_bonus
        quality_bonus = equipment.get("quality_bonus", equipment.get("tier_bonus", {})) # 获取品质加成字典，默认为空

        # 攻击力
        if "attack" in equipment:
            attack = equipment["attack"]
            bonus = quality_bonus.get("attack", 0)
            if isinstance(attack, list) and len(attack) >= 2:
                # 处理范围攻击
                min_attack = attack[0] + bonus
                max_attack = attack[1] + bonus
                description += f"\n- 攻击: {min_attack}~{max_attack}"
                if bonus > 0:
                    description += f" (+{bonus})" # 可选：显示加成值
            else:
                # 处理单个攻击值
                total_attack = attack + bonus
                description += f"\n- 攻击: {total_attack}"
                if bonus > 0:
                    description += f" (+{bonus})" # 可选：显示加成值

        # 防御力
        if "defense" in equipment:
            defense = equipment["defense"]
            bonus = quality_bonus.get("defense", 0)
            if isinstance(defense, list) and len(defense) >= 2:
                min_defense = defense[0] + bonus
                max_defense = defense[1] + bonus
                description += f"\n- 防御: {min_defense}~{max_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_defense = defense + bonus
                description += f"\n- 防御: {total_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 魔法
        if "magic" in equipment:
            magic = equipment["magic"]
            bonus = quality_bonus.get("magic", 0)
            if isinstance(magic, list) and len(magic) >= 2:
                min_magic = magic[0] + bonus
                max_magic = magic[1] + bonus
                description += f"\n- 魔法: {min_magic}~{max_magic}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_magic = magic + bonus
                description += f"\n- 魔法: {total_magic}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 道术
        if "taoism" in equipment:
            taoism = equipment["taoism"]
            bonus = quality_bonus.get("taoism", 0)
            if isinstance(taoism, list) and len(taoism) >= 2:
                min_taoism = taoism[0] + bonus
                max_taoism = taoism[1] + bonus
                description += f"\n- 道术: {min_taoism}~{max_taoism}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_taoism = taoism + bonus
                description += f"\n- 道术: {total_taoism}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 魔法防御
        if "magic_defense" in equipment:
            magic_defense = equipment["magic_defense"]
            bonus = quality_bonus.get("magic_defense", 0)
            if isinstance(magic_defense, list) and len(magic_defense) >= 2:
                min_magic_defense = magic_defense[0] + bonus
                max_magic_defense = magic_defense[1] + bonus
                description += f"\n- 魔防: {min_magic_defense}~{max_magic_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"
            else:
                total_magic_defense = magic_defense + bonus
                description += f"\n- 魔防: {total_magic_defense}"
                if bonus > 0:
                    description += f" (+{bonus})"

        # 准确度 (品质通常不加成这些)
        if "accuracy" in equipment:
            description += f"\n- 准确: +{equipment['accuracy']}"

        # 敏捷 (品质通常不加成这些)
        if "agility" in equipment:
            description += f"\n- 敏捷: +{equipment['agility']}"

        # 幸运 (品质通常不加成这些)
        if "luck" in equipment:
            try:
                luck_value = int(equipment['luck']) # 尝试将值转为整数
                if luck_value > 0:
                    description += f"\n- 幸运: +{luck_value}"
            except (ValueError, TypeError):
                # 如果值不是有效数字或转换失败，则忽略或记录日志
                logger.warning(f"装备 '{equipment.get('name', '未知')}' 的幸运值无效: {equipment['luck']}")
                pass # 或者不显示

        # 添加攻击速度 (品质通常不加成这些)
        if "attack_speed" in equipment:
            description += f"\n- 攻速: +{int(equipment['attack_speed']*100)}%"

        # 吸血属性 (品质通常不加成这些)
        if "lifesteal" in equipment:
            description += f"\n- 吸血: {equipment['lifesteal']}%"

        # 固定值吸血属性 (品质通常不加成这些)
        if "flat_lifesteal" in equipment:
            description += f"\n- 固定吸血: {equipment['flat_lifesteal']}点"

        # 魔法闪避 (品质通常不加成这些)
        if "magic_avoidance" in equipment:
            description += f"\n- 魔法闪避: +{equipment['magic_avoidance']}%"

        # 装备爆率加成 (品质通常不加成这些)
        if "drop_rate_bonus" in equipment:
            description += f"\n- 爆率加成: +{int(equipment['drop_rate_bonus']*100)}%"

        # 添加特殊效果
        if "special" in equipment:
            special = equipment.get("special", "")
            special_desc = ""

            if special == "暴击" and "crit_damage" in equipment:
                try:
                    crit_damage = equipment.get("crit_damage", 0)
                    special_desc = f"暴击伤害增加 {int(crit_damage*100)}%"
                except (TypeError, ValueError):
                    special_desc = "暴击伤害增加"
            elif special == "攻速" and "attack_speed" in equipment:
                try:
                    attack_speed = equipment.get("attack_speed", 0)
                    special_desc = f"攻击速度提高 {int(attack_speed*100)}%"
                except (TypeError, ValueError):
                    special_desc = "攻击速度提高"
            elif special == "生命偷取" and "life_steal" in equipment:
                special_desc = f"攻击时偷取目标 {int(equipment['life_steal']*100)}% 生命值"
            elif special == "吸血" and "lifesteal" in equipment:
                special_desc = f"攻击时吸取目标 {equipment['lifesteal']}% 生命值"
            elif special == "固定吸血" and "flat_lifesteal" in equipment:
                special_desc = f"攻击时固定吸取 {equipment['flat_lifesteal']} 点生命值"
            elif special == "魔法穿透" and "magic_penetration" in equipment:
                special_desc = f"无视目标 {int(equipment['magic_penetration']*100)}% 魔法防御"
            elif special == "麻痹" and "description" in equipment:
                special_desc = equipment["description"]

            if special_desc:
                description += f"\n\n特殊效果: {special_desc}"

        # 添加装备描述文本
        if "description" in equipment:
            description += f"\n\n{equipment['description']}"

        return description

    def _set_equipment_image(self, equipment):
        """设置装备图像

        参数:
            equipment: 装备数据字典
        """
        if not equipment or not isinstance(equipment, dict):
            # 清空图像面板
            if hasattr(self, 'equipment_image'):
                self.remove_component(self.equipment_image)
                self.equipment_image = None
            return

        # 获取装备名称和类型
        item_name = equipment.get("name", "")
        item_type = equipment.get("type", "")

        # 获取资源管理器实例
        from utils.resource_manager import resources

        # 尝试加载图片
        image = None
        # 根据物品类型确定图片路径
        equipment_types = {
            "武器": "武器",
            "防具": "防具",
            "头盔": "头盔",
            "项链": "项链",
            "手镯": "手镯",
            "左手镯": "手镯",
            "右手镯": "手镯",
            "戒指": "戒指",
            "左戒指": "戒指",
            "右戒指": "戒指",
            "勋章": "勋章"
        }

        try:
            # 获取对应类型的目录
            item_dir = equipment_types.get(item_type, "")
            if item_dir:
                possible_paths = [
                    f"assets/images/equipment/{item_dir}/{item_name}.png",
                    f"assets/images/equipment/{item_type}/{item_name}.png",
                ]

                # 尝试每个可能的路径
                for path in possible_paths:
                    image = resources.load_image(path)
                    if image:
                        logger.debug(f"装备图像加载成功: {path}")
                        break

                # 如果找不到具体装备图像，使用类型图像
                if not image:
                    type_paths = [
                        f"assets/images/equipment/{item_dir}/default.png",
                        f"assets/images/equipment/{item_type}/default.png",
                    ]

                    for path in type_paths:
                        image = resources.load_image(path)
                        if image:
                            logger.debug(f"装备类型图像加载成功: {path}")
                            break


            if not image:
                default_paths = [
                    "assets/images/equipment/unknown.png",
                ]
                for path in default_paths:
                    image = resources.load_image(path)
                    if image:
                        logger.debug("使用默认未知物品图像")
                        break
        except Exception as e:
            logger.error(f"加载装备图像时出错: {e}")
            image = None

        # 如果找到图像，显示到面板上
        if image:
            # 调整图像大小以适应面板
            panel_size = self.equipment_image_panel.rect.width - 10

            # 获取原始图片尺寸
            img_width, img_height = image.get_size()

            # 计算缩放比例，保持原始宽高比
            scale_factor = min(panel_size / img_width, panel_size / img_height)
            new_width = int(img_width * scale_factor)
            new_height = int(img_height * scale_factor)

            # 缩放图片
            scaled_image = pygame.transform.smoothscale(image, (new_width, new_height))

            # 创建一个透明的Surface，大小与面板内部区域相同
            final_image = pygame.Surface((panel_size, panel_size), pygame.SRCALPHA)

            # 计算居中位置
            x_offset = (panel_size - new_width) // 2
            y_offset = (panel_size - new_height) // 2

            # 将缩放后的图片绘制到透明Surface上的居中位置
            final_image.blit(scaled_image, (x_offset, y_offset))

            # 如果已有装备图像组件，移除它
            if hasattr(self, 'equipment_image') and self.equipment_image:
                self.remove_component(self.equipment_image)

            # 创建新的图像组件
            image_rect = pygame.Rect(
                self.equipment_image_panel.rect.left + 5,
                self.equipment_image_panel.rect.top + 5,
                panel_size,
                panel_size
            )
            self.equipment_image = UIComponent(image_rect)
            self.equipment_image.image = final_image
            self.add_component(self.equipment_image)
            logger.info(f"显示装备图像: {item_name} ({item_type})")
        else:
            # 如果没有找到图像，清空图像面板
            if hasattr(self, 'equipment_image') and self.equipment_image:
                self.remove_component(self.equipment_image)
                self.equipment_image = None
            logger.warning(f"未找到装备图像: {item_name} ({item_type})")