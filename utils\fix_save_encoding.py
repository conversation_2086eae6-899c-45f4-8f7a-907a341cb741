import os
import json
import shutil
import chardet
from typing import Dict, Any, List, Tuple, Optional
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入安全文件处理器
from utils.safe_file import SafeFileHandler

# 设置日志
import logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("存档修复工具")

# 存档目录
SAVE_DIR = os.path.join(os.getcwd(), "data", "saves")

def fix_save_file(file_path: str) -> bool:
    """修复单个存档文件的编码

    Args:
        file_path: 存档文件路径

    Returns:
        bool: 是否成功修复
    """
    logger.info(f"开始修复存档: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return False
        
    # 创建备份
    backup_path = f"{file_path}.fix_backup"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"创建备份: {backup_path}")
    except Exception as e:
        logger.error(f"创建备份失败: {e}")
        return False
    
    # 使用SafeFileHandler修复文件编码
    try:
        # 读取文件二进制内容
        with open(file_path, "rb") as f:
            content = f.read()
            
        # 检测编码
        result = chardet.detect(content)
        detected_encoding = result["encoding"]
        confidence = result["confidence"]
        logger.info(f"检测到编码: {detected_encoding}，置信度: {confidence}")
        
        # 使用我们改进的文件处理器修复编码
        success, data = SafeFileHandler._fix_file_encoding(file_path)
        if success:
            logger.info(f"成功修复并保存为UTF-8编码: {file_path}")
            return True
            
        # 尝试额外的修复方法
        try:
            # 如果SafeFileHandler方法失败，尝试直接使用检测到的编码
            if detected_encoding:
                text = content.decode(detected_encoding)
                data = json.loads(text)
                
                # 使用UTF-8保存
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"使用检测到的编码({detected_encoding})成功修复文件")
                return True
        except Exception as e:
            logger.warning(f"额外修复方法失败: {e}")
        
        logger.error(f"无法修复文件: {file_path}")
        return False
    except Exception as e:
        logger.error(f"修复过程出错: {e}")
        # 如果修复失败，恢复备份
        try:
            shutil.copy2(backup_path, file_path)
            logger.info("已恢复原始文件")
        except:
            logger.error("恢复原始文件失败")
        return False

def fix_all_save_files() -> Tuple[int, int]:
    """修复所有存档文件

    Returns:
        Tuple[int, int]: (成功修复数量, 失败数量)
    """
    logger.info(f"开始修复所有存档文件，目录: {SAVE_DIR}")
    
    # 确保存档目录存在
    if not os.path.exists(SAVE_DIR):
        logger.error(f"存档目录不存在: {SAVE_DIR}")
        return 0, 0
    
    success_count = 0
    fail_count = 0
    
    # 找出所有存档文件
    save_files = []
    for filename in os.listdir(SAVE_DIR):
        if filename.endswith('.save') or filename.endswith('.json'):
            # 跳过备份文件
            if '.bak' in filename or '.backup' in filename or '.fix_backup' in filename:
                continue
            save_files.append(os.path.join(SAVE_DIR, filename))
    
    logger.info(f"找到 {len(save_files)} 个存档文件")
    
    # 修复每个存档文件
    for file_path in save_files:
        if fix_save_file(file_path):
            success_count += 1
        else:
            fail_count += 1
    
    logger.info(f"存档修复完成。成功: {success_count}, 失败: {fail_count}")
    return success_count, fail_count

if __name__ == "__main__":
    logger.info("存档修复工具启动")
    fixed, failed = fix_all_save_files()
    logger.info(f"修复完成。成功修复 {fixed} 个文件，{failed} 个文件修复失败。")
    
    if failed > 0:
        logger.warning("有些文件无法修复，请尝试手动修复或重新创建存档。")
    else:
        logger.info("所有存档都已成功修复为UTF-8编码。")
    
    input("按回车键退出...")
