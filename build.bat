@echo off
echo 正在开始打包"萝卜放置传奇"游戏...
echo.

rem 检查Python和PyInstaller是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未检测到Python，请确保已安装Python并添加到系统PATH中。
    goto :end
)

pip show pyinstaller > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：未检测到PyInstaller，正在尝试安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo 错误：PyInstaller安装失败。
        goto :end
    )
)

rem 清理上一次的打包结果
echo 正在清理上一次的打包结果...
if exist "build" (
    rmdir /s /q "build"
    echo 已删除build目录
)
if exist "dist" (
    rmdir /s /q "dist"
    echo 已删除dist目录
)

echo 正在执行打包命令...
pyinstaller build.spec

if %errorlevel% equ 0 (
    echo.
    echo 打包成功完成！
    echo 打包结果位于：%cd%\dist\萝卜放置传奇
) else (
    echo.
    echo 打包过程中出现错误，请查看上方日志。
)

:end
echo.
echo 按任意键退出...
pause > nul 