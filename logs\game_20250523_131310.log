[2025-05-23 13:13:10] [WARNING] [main.py:108] 游戏启动
[2025-05-23 13:13:11] [WARNING] [main.py:234] 开始创建游戏界面
[2025-05-23 13:13:11] [ERROR] [main.py:329] 创建登录界面失败: Screen.__init__() takes 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 326, in _create_screens
    login_screen = LoginScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\login_screen.py", line 22, in __init__
    super().__init__(ui_manager, game_manager)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Screen.__init__() takes 2 positional arguments but 3 were given
[2025-05-23 13:13:11] [ERROR] [main.py:336] 创建在线角色创建界面失败: Screen.__init__() takes 2 positional arguments but 3 were given
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 333, in _create_screens
    character_creation_screen = CharacterCreationScreen(self.ui_manager, self.game)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\character_creation_screen.py", line 21, in __init__
    super().__init__(ui_manager, game_manager)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Screen.__init__() takes 2 positional arguments but 3 were given
[2025-05-23 13:13:11] [WARNING] [main.py:340] 游戏界面创建完成
[2025-05-23 13:13:17] [ERROR] [ui_manager.py:336] 尝试显示不存在的界面: login
[2025-05-23 13:14:11] [ERROR] [ui_manager.py:336] 尝试显示不存在的界面: login
