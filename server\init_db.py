"""
数据库初始化脚本
用于创建必要的集合和索引
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到控制台
        logging.FileHandler("init_db.log")  # 输出到文件
    ]
)
logger = logging.getLogger(__name__)

# MongoDB连接信息
MONGO_URI = "mongodb://localhost:27017"
DATABASE_NAME = "game_db"

async def init_database():
    """初始化数据库"""
    print("开始初始化数据库...")
    try:
        # 连接到MongoDB
        print(f"正在连接到MongoDB: {MONGO_URI}")
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[DATABASE_NAME]
        
        # 创建集合
        collections = ["users", "players", "game_states"]
        for collection_name in collections:
            print(f"正在创建集合: {collection_name}")
            logger.info(f"正在创建集合: {collection_name}")
            # 创建集合（在MongoDB中，集合会在第一次插入文档时自动创建）
            # 这里我们插入一个空文档，然后立即删除它，以确保集合被创建
            result = await db[collection_name].insert_one({"_temp": True})
            await db[collection_name].delete_one({"_id": result.inserted_id})
            print(f"集合 {collection_name} 创建成功")
            logger.info(f"集合 {collection_name} 创建成功")
        
        # 创建索引
        print("正在创建索引...")
        logger.info("正在创建索引...")
        
        # 用户集合索引
        await db["users"].create_index("username", unique=True)
        await db["users"].create_index("email", unique=True)
        print("用户集合索引创建成功")
        logger.info("用户集合索引创建成功")
        
        # 玩家集合索引
        await db["players"].create_index("user_id")
        await db["players"].create_index("name")
        print("玩家集合索引创建成功")
        logger.info("玩家集合索引创建成功")
        
        # 游戏状态集合索引
        await db["game_states"].create_index("player_id", unique=True)
        print("游戏状态集合索引创建成功")
        logger.info("游戏状态集合索引创建成功")
        
        print("数据库初始化完成")
        logger.info("数据库初始化完成")
    except Exception as e:
        print(f"初始化数据库时发生错误: {e}")
        logger.error(f"初始化数据库时发生错误: {e}")
        raise

if __name__ == "__main__":
    # 运行异步函数
    asyncio.run(init_database())
