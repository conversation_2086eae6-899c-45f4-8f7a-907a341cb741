[2025-07-20 21:30:49] [WARNING] [main.py:173] 游戏启动
[2025-07-20 21:30:49] [WARNING] [main.py:313] 开始创建游戏界面
[2025-07-20 21:30:51] [WARNING] [main.py:422] 游戏界面创建完成
[2025-07-20 21:31:00] [ERROR] [main.py:1006] 游戏发生未处理的异常: '>' not supported between instances of 'list' and 'int'
Traceback (most recent call last):
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 1002, in main
    game_instance.run() # run() 现在包含清理逻辑
    ~~~~~~~~~~~~~~~~~^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 511, in run
    self._update(dt)
    ~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\main.py", line 589, in _update
    self.ui_manager.update(dt)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\ui_manager.py", line 393, in update
    self.screens[self.active_screen].update(dt)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 2014, in update
    self.tooltip.show(self.hovered_item, mouse_pos[0], mouse_pos[1])
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 33, in show
    details = self._build_item_details(item)
  File "E:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版 (2)\老版\ui\screens\inventory_screen.py", line 120, in _build_item_details
    if "defense" in item and item["defense"] > 0:
                             ^^^^^^^^^^^^^^^^^^^
TypeError: '>' not supported between instances of 'list' and 'int'
