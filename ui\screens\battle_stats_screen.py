import pygame
import logging
import time
from typing import Dict, List, Tuple, Optional, Any, Union

from ui.components import Panel, Button, Text, ProgressBar
from ui.ui_manager import Screen

logger = logging.getLogger(__name__)

class BattleStatsScreen(Screen):
    """战斗统计界面，用于显示各种战斗统计数据"""
    
    def __init__(self, ui_manager, game_manager=None):
        """初始化战斗统计界面
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
        """
        super().__init__("battle_stats")
        logger.info("初始化战斗统计界面")
        
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        
        # 标题文本
        self.title = None
        
        # 各种统计文本
        self.stat_components = {}
        
        # 装备掉落统计
        self.equipment_stats = {}
        
        # 战斗日志显示组件
        self.battle_logs = []
        
        # 创建UI元素
        self._create_ui()
        
        logger.info("战斗统计界面初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        screen_size = pygame.display.get_surface().get_size()
        
        # 背景面板
        main_panel = Panel(
            pygame.Rect(0, 0, screen_size[0], screen_size[1]),
            color=(20, 20, 30),
            border_color=(20, 20, 30, 0)
        )
        self.add_component(main_panel)
        
        # 标题
        title_rect = pygame.Rect((screen_size[0] - 300) // 2, 30, 300, 40)
        self.title = self.ui_manager.create_text(
            title_rect,
            "战斗统计",
            "chinese_large",
            (220, 220, 240),
            "center"
        )
        self.add_component(self.title)
        
        # 创建基础统计面板
        self._create_basic_stats_panel(screen_size)
        
        # 创建装备掉落统计面板
        self._create_equipment_stats_panel(screen_size)
        
        # 创建战斗日志面板
        self._create_battle_logs_panel(screen_size)
        
        # 创建返回按钮
        back_btn_rect = pygame.Rect(screen_size[0] - 120, screen_size[1] - 60, 100, 40)
        back_btn = self.ui_manager.create_button(
            back_btn_rect,
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_btn)
        
        # 创建重置按钮
        reset_btn_rect = pygame.Rect(20, screen_size[1] - 60, 100, 40)
        reset_btn = self.ui_manager.create_button(
            reset_btn_rect,
            "重置统计",
            self._on_reset_click,
            "chinese_normal"
        )
        self.add_component(reset_btn)
    
    def _create_basic_stats_panel(self, screen_size):
        """创建基础统计面板"""
        
        # 基础统计面板
        panel_width = 320
        panel_height = 260
        panel_rect = pygame.Rect(
            40, 
            100, 
            panel_width, 
            panel_height
        )
        
        stats_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 45),
            border_color=(60, 60, 90),
            border_width=2
        )
        self.add_component(stats_panel)
        
        # 面板标题
        title_rect = pygame.Rect(panel_rect.left + 20, panel_rect.top + 15, panel_width - 40, 30)
        title = self.ui_manager.create_text(
            title_rect,
            "基础统计信息",
            "chinese_normal",
            (200, 200, 240),
            "left"
        )
        self.add_component(title)
        
        # 统计项目
        stats = [
            ("exp_gained", "获得经验"),
            ("gold_gained", "获得金币"),
            ("monsters_killed", "击杀怪物"),
            ("battles_count", "战斗次数"),
            ("auto_battle_time", "自动战斗时长")
        ]
        
        # 统计显示
        start_y = panel_rect.top + 60
        line_height = 35
        
        for i, (stat_id, stat_name) in enumerate(stats):
            # 统计名称
            name_rect = pygame.Rect(
                panel_rect.left + 30, 
                start_y + i * line_height, 
                140, 
                25
            )
            name_text = self.ui_manager.create_text(
                name_rect,
                f"{stat_name}:",
                "chinese_normal",
                (180, 180, 210),
                "left"
            )
            self.add_component(name_text)
            
            # 统计值
            value_rect = pygame.Rect(
                panel_rect.left + 180, 
                start_y + i * line_height, 
                panel_width - 200, 
                25
            )
            value_text = self.ui_manager.create_text(
                value_rect,
                "0",
                "chinese_normal",
                (220, 220, 240),
                "left"
            )
            self.add_component(value_text)
            self.stat_components[stat_id] = value_text
    
    def _create_equipment_stats_panel(self, screen_size):
        """创建装备掉落统计面板"""
        # screen_size = pygame.display.get_surface().get_size() # 这行不再需要
        
        # 装备统计面板
        panel_width = 320
        panel_height = 260
        panel_rect = pygame.Rect(
            380, 
            100, 
            panel_width, 
            panel_height
        )
        
        equip_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 45),
            border_color=(60, 60, 90),
            border_width=2
        )
        self.add_component(equip_panel)
        
        # 面板标题
        title_rect = pygame.Rect(panel_rect.left + 20, panel_rect.top + 15, panel_width - 40, 30)
        title = self.ui_manager.create_text(
            title_rect,
            "装备掉落统计",
            "chinese_normal",
            (200, 200, 240),
            "left"
        )
        self.add_component(title)
        
        # 装备品质统计
        qualities = [
            ("普通", (200, 200, 200)),
            ("精良", (30, 255, 30)),
            ("稀有", (30, 144, 255)),
            ("史诗", (147, 112, 219)),
            ("传说", (255, 140, 0))
        ]
        
        # 统计显示
        start_y = panel_rect.top + 60
        line_height = 35
        
        for i, (quality, color) in enumerate(qualities):
            # 品质名称
            name_rect = pygame.Rect(
                panel_rect.left + 30, 
                start_y + i * line_height, 
                140, 
                25
            )
            name_text = self.ui_manager.create_text(
                name_rect,
                f"{quality}:",
                "chinese_normal",
                color,
                "left"
            )
            self.add_component(name_text)
            
            # 品质数量
            value_rect = pygame.Rect(
                panel_rect.left + 180, 
                start_y + i * line_height, 
                panel_width - 200, 
                25
            )
            value_text = self.ui_manager.create_text(
                value_rect,
                "0",
                "chinese_normal",
                (220, 220, 240),
                "left"
            )
            self.add_component(value_text)
            self.equipment_stats[quality] = value_text
    
    def _create_battle_logs_panel(self, screen_size):
        """创建战斗日志面板"""
        # screen_size = pygame.display.get_surface().get_size() # 这行不再需要
        
        # 日志面板
        panel_width = 660
        panel_height = 250
        panel_rect = pygame.Rect(
            40, 
            380, 
            panel_width, 
            panel_height
        )
        
        logs_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 45),
            border_color=(60, 60, 90),
            border_width=2
        )
        self.add_component(logs_panel)
        
        # 面板标题
        title_rect = pygame.Rect(panel_rect.left + 20, panel_rect.top + 15, panel_width - 40, 30)
        title = self.ui_manager.create_text(
            title_rect,
            "战斗日志 (最近10条)",
            "chinese_normal",
            (200, 200, 240),
            "left"
        )
        self.add_component(title)
        
        # 创建日志条目显示
        start_y = panel_rect.top + 60
        line_height = 18
        max_logs = 10
        
        for i in range(max_logs):
            log_rect = pygame.Rect(
                panel_rect.left + 20, 
                start_y + i * line_height, 
                panel_width - 40, 
                15
            )
            log_text = self.ui_manager.create_text(
                log_rect,
                "",
                "chinese_small",
                (200, 200, 210),
                "left"
            )
            self.add_component(log_text)
            self.battle_logs.append(log_text)
    
    def _on_back_click(self):
        """返回按钮点击事件"""
        logger.info("点击返回按钮，返回游戏界面")
        self.ui_manager.show_screen("game")
    
    def _on_reset_click(self):
        """重置统计按钮点击事件"""
        logger.info("点击重置统计按钮")
        if self.game_manager:
            self.game_manager.reset_battle_stats()
            self.refresh_stats()
    
    def show(self):
        """显示界面时更新数据"""
        super().show()
        logger.info("显示战斗统计界面")
        
        # 刷新数据显示
        self.refresh_stats()
    
    def refresh_stats(self):
        """刷新统计数据显示"""
        if not self.game_manager:
            logger.warning("游戏管理器为空，无法刷新战斗统计")
            return
            
        battle_stats = self.game_manager.battle_stats
        
        # 更新基础统计
        if "exp_gained" in battle_stats:
            self.stat_components["exp_gained"].set_text(f"{battle_stats['exp_gained']}")
            
        if "gold_gained" in battle_stats:
            self.stat_components["gold_gained"].set_text(f"{battle_stats['gold_gained']}")
            
        if "monsters_killed" in battle_stats:
            self.stat_components["monsters_killed"].set_text(f"{battle_stats['monsters_killed']}")
            
        if "battles_count" in battle_stats:
            self.stat_components["battles_count"].set_text(f"{battle_stats['battles_count']}")
            
        if "auto_battle_time" in battle_stats:
            battle_time = battle_stats["auto_battle_time"]
            # 如果当前在自动战斗，需要加上当前时长
            if battle_stats.get("battle_start_time", 0) > 0:
                battle_time += time.time() - battle_stats["battle_start_time"]
                
            hours = int(battle_time // 3600)
            minutes = int((battle_time % 3600) // 60)
            seconds = int(battle_time % 60)
            
            if hours > 0:
                time_text = f"{hours}小时 {minutes}分钟"
            else:
                time_text = f"{minutes}分钟 {seconds}秒"
                
            self.stat_components["auto_battle_time"].set_text(time_text)
        
        # 更新装备掉落统计
        if "equipment_drops" in battle_stats:
            # 确保所有UI组件中的装备掉落品质都被更新
            for quality, component in self.equipment_stats.items():
                count = battle_stats["equipment_drops"].get(quality, 0)
                component.set_text(f"{count}")
        
        # 更新战斗日志
        if "battle_logs" in battle_stats:
            logs = battle_stats["battle_logs"]
            # 取最新的10条日志
            recent_logs = logs[-10:] if len(logs) > 10 else logs
            
            # 清空现有日志显示
            for log_component in self.battle_logs:
                log_component.set_text("")
                
            # 更新日志显示
            for i, log in enumerate(reversed(recent_logs)):
                if i < len(self.battle_logs):
                    self.battle_logs[i].set_text(log)
                    
        logger.debug("战斗统计界面数据已刷新") 