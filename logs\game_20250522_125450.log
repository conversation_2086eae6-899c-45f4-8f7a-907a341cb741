[2025-05-22 12:54:50] [WARNING] [main.py:105] 游戏启动
[2025-05-22 12:54:50] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 12:54:50] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 12:55:08] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 12:55:08] [WARNING] [battle.py:856] 战斗日志: 获得了 64 金币
[2025-05-22 12:55:22] [WARNING] [battle.py:856] 战斗日志: 获得了 6 经验值
[2025-05-22 12:55:22] [WARNING] [battle.py:856] 战斗日志: 获得了 70 金币
[2025-05-22 12:55:22] [WARNING] [battle.py:856] 战斗日志: 掉落装备: [普通]木剑
[2025-05-22 12:55:35] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 12:55:35] [WARNING] [battle.py:856] 战斗日志: 获得了 105 金币
[2025-05-22 12:55:53] [ERROR] [battle.py:841] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 12:55:53] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 12:55:53] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 848, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 12:55:53] [ERROR] [battle.py:390] AttributeError in execute_battle_round: 'NoneType' object has no attribute 'hp'
[2025-05-22 12:55:53] [ERROR] [battle.py:391] Player: None, Monster: None
[2025-05-22 12:55:53] [ERROR] [battle.py:392] Traceback:\nTraceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 371, in execute_battle_round
    if self.player.hp <= 0:
       ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'hp'

[2025-05-22 12:56:10] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 12:56:10] [WARNING] [battle.py:856] 战斗日志: 获得了 84 金币
[2025-05-22 12:56:22] [ERROR] [battle.py:841] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-22 12:56:22] [ERROR] [battle.py:653] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-22 12:56:22] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 647, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 848, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

[2025-05-22 12:56:22] [ERROR] [battle.py:390] AttributeError in execute_battle_round: 'NoneType' object has no attribute 'hp'
[2025-05-22 12:56:22] [ERROR] [battle.py:391] Player: None, Monster: None
[2025-05-22 12:56:22] [ERROR] [battle.py:392] Traceback:\nTraceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 371, in execute_battle_round
    if self.player.hp <= 0:
       ^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'hp'

