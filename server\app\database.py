from motor.motor_asyncio import AsyncIOMotorClient
from app.config import settings
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 创建数据库连接
client: Optional[AsyncIOMotorClient] = None
db = None
users_collection = None
players_collection = None
game_states_collection = None

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    global client, db, users_collection, players_collection, game_states_collection

    try:
        # 设置连接超时
        client = AsyncIOMotorClient(
            settings.MONGO_URI,
            serverSelectionTimeoutMS=settings.MONGO_SERVER_SELECTION_TIMEOUT_MS,
            connectTimeoutMS=settings.MONGO_CONNECT_TIMEOUT_MS
        )

        # 测试连接 - 使用同步方法测试连接
        try:
            # 使用同步方法测试连接
            client.admin.command('ping')
            logger.info("MongoDB ping successful")
        except Exception as ping_error:
            logger.error(f"MongoDB ping failed: {ping_error}")
            raise

        db = client[settings.DATABASE_NAME]

        # 初始化集合
        users_collection = db["users"]
        players_collection = db["players"]
        game_states_collection = db["game_states"]

        # 打印集合状态
        logger.info(f"Collections initialized: users={users_collection is not None}, players={players_collection is not None}, game_states={game_states_collection is not None}")

        logger.info(f"Connected to MongoDB: {settings.MONGO_URI}")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        # 确保在连接失败时也初始化集合变量，避免空引用
        if db is None:
            db = {}
        users_collection = None
        players_collection = None
        game_states_collection = None
        return False

# 创建索引
async def create_indexes():
    """创建数据库索引"""
    if users_collection is None or players_collection is None or game_states_collection is None:
        logger.warning("Cannot create indexes: database collections not initialized")
        return False

    try:
        # 用户集合索引
        await users_collection.create_index("username", unique=True)
        await users_collection.create_index("email", unique=True)

        # 玩家集合索引
        await players_collection.create_index("user_id")
        await players_collection.create_index("name")

        # 游戏状态集合索引
        await game_states_collection.create_index("player_id", unique=True)

        logger.info("Database indexes created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create database indexes: {e}")
        return False
