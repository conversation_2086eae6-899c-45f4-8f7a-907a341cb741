import pygame
from typing import Dict, List, Callable, Any, Optional, Tuple

from ui.ui_manager import Screen
from ui.components import Button, Panel, Text, ImageButton, Dropdown
from utils.logger import logger
from utils.resource_manager import resources

class Settings(Screen):
    """游戏设置界面"""
    
    def __init__(self, ui_manager, game_manager):
        """
        初始化设置界面
        
        参数:
            ui_manager: UI管理器实例
            game_manager: 游戏管理器实例
        """
        super().__init__("settings")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        
        # 自动吃药设置
        self.auto_potion_enabled = False
        self.hp_threshold = 50  # 默认生命值低于50%时吃药
        self.mp_threshold = 30  # 默认魔法值低于30%时吃药
        
        # 药水选择列表
        self.available_hp_potions = []
        self.available_mp_potions = []
        self.available_dual_potions = []
        
        # 选中的药水
        self.selected_hp_potions = []
        self.selected_mp_potions = []
        self.selected_dual_potions = []
        
        # 药水下拉菜单
        self.hp_potion_dropdown = None
        self.mp_potion_dropdown = None
        
        # 自动出售装备设置
        self.auto_sell_enabled = False
        self.sellable_qualities = {
            "普通": False,  
            "精良": False,
            "稀有": False,
            "史诗": False,
            "传说": False
        }
        
        self._create_components()
    
    def _create_components(self):
        """创建界面组件"""
        # 获取屏幕尺寸
        screen_size = pygame.display.get_surface().get_size()
        
        # 创建背景
        self.background = Panel(
            pygame.Rect(0, 0, screen_size[0], screen_size[1]),
            color=(40, 40, 60),
            border_width=0
        )
        self.add_component(self.background)
        
        # 创建标题
        title_rect = pygame.Rect(
            (screen_size[0] - 400) // 2,
            50,
            400,
            60
        )
        title = self.ui_manager.create_text(
            title_rect,
            "游戏设置",
            "chinese_title",
            (255, 255, 220),
            "center"
        )
        self.add_component(title)
        
        # 创建主面板
        panel_width = 600
        panel_height = 750  # 增加高度以容纳自动出售设置
        panel_rect = pygame.Rect(
            (screen_size[0] - panel_width) // 2,
            (screen_size[1] - panel_height) // 2,
            panel_width,
            panel_height
        )
        main_panel = self.ui_manager.create_panel(
            panel_rect,
            color=(30, 30, 50),  # 深色背景
            border_color=(60, 60, 90),  # 边框颜色
            border_width=2,
            alpha=180  # 半透明
        )
        self.add_component(main_panel)
        
        # 自动吃药系统设置标题
        auto_potion_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 30,
            200,
            40
        )
        auto_potion_title = self.ui_manager.create_text(
            auto_potion_title_rect,
            "自动吃药系统",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(auto_potion_title)
        
        # 自动吃药启用/禁用按钮
        auto_potion_btn_rect = pygame.Rect(
            panel_rect.x + 300,
            panel_rect.y + 35,
            100,
            30
        )
        self.auto_potion_btn = self.ui_manager.create_button(
            auto_potion_btn_rect,
            "开启" if self.auto_potion_enabled else "关闭",
            self._toggle_auto_potion,
            "chinese_normal"
        )
        self.add_component(self.auto_potion_btn)
        
        # HP阈值设置
        hp_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 90,
            200,
            30
        )
        hp_title = self.ui_manager.create_text(
            hp_title_rect,
            "HP吃药阈值:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(hp_title)
        
        # HP阈值滑块背景
        hp_slider_bg_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 100,
            200,
            10
        )
        hp_slider_bg = self.ui_manager.create_panel(
            hp_slider_bg_rect,
            color=(80, 80, 100),
            border_width=0
        )
        self.add_component(hp_slider_bg)
        
        # HP阈值滑块
        hp_slider_width = int(200 * (self.hp_threshold / 100))
        hp_slider_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 100,
            hp_slider_width,
            10
        )
        self.hp_slider = self.ui_manager.create_panel(
            hp_slider_rect,
            color=(255, 50, 50),  # 红色，表示HP
            border_width=0
        )
        self.add_component(self.hp_slider)
        
        # HP阈值调整按钮
        hp_down_rect = pygame.Rect(
            panel_rect.x + 170,
            panel_rect.y + 95,
            20,
            20
        )
        hp_down_btn = self.ui_manager.create_button(
            hp_down_rect,
            "-",
            lambda: self._adjust_hp_threshold(-5),
            "chinese_normal"
        )
        self.add_component(hp_down_btn)
        
        hp_up_rect = pygame.Rect(
            panel_rect.x + 410,
            panel_rect.y + 95,
            20,
            20
        )
        hp_up_btn = self.ui_manager.create_button(
            hp_up_rect,
            "+",
            lambda: self._adjust_hp_threshold(5),
            "chinese_normal"
        )
        self.add_component(hp_up_btn)
        
        # HP阈值文本
        self.hp_text = self.ui_manager.create_text(
            pygame.Rect(
                panel_rect.x + 440,
                panel_rect.y + 95,
                50,
                20
            ),
            f"{self.hp_threshold}%",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(self.hp_text)
        
        # MP阈值设置
        mp_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 140,
            200,
            30
        )
        mp_title = self.ui_manager.create_text(
            mp_title_rect,
            "MP吃药阈值:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(mp_title)
        
        # MP阈值滑块背景
        mp_slider_bg_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 150,
            200,
            10
        )
        mp_slider_bg = self.ui_manager.create_panel(
            mp_slider_bg_rect,
            color=(80, 80, 100),
            border_width=0
        )
        self.add_component(mp_slider_bg)
        
        # MP阈值滑块
        mp_slider_width = int(200 * (self.mp_threshold / 100))
        mp_slider_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 150,
            mp_slider_width,
            10
        )
        self.mp_slider = self.ui_manager.create_panel(
            mp_slider_rect,
            color=(50, 50, 255),  # 蓝色，表示MP
            border_width=0
        )
        self.add_component(self.mp_slider)
        
        # MP阈值调整按钮
        mp_down_rect = pygame.Rect(
            panel_rect.x + 170,
            panel_rect.y + 145,
            20,
            20
        )
        mp_down_btn = self.ui_manager.create_button(
            mp_down_rect,
            "-",
            lambda: self._adjust_mp_threshold(-5),
            "chinese_normal"
        )
        self.add_component(mp_down_btn)
        
        mp_up_rect = pygame.Rect(
            panel_rect.x + 410,
            panel_rect.y + 145,
            20,
            20
        )
        mp_up_btn = self.ui_manager.create_button(
            mp_up_rect,
            "+",
            lambda: self._adjust_mp_threshold(5),
            "chinese_normal"
        )
        self.add_component(mp_up_btn)
        
        # MP阈值文本
        self.mp_text = self.ui_manager.create_text(
            pygame.Rect(
                panel_rect.x + 440,
                panel_rect.y + 145,
                50,
                20
            ),
            f"{self.mp_threshold}%",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(self.mp_text)
        
        # 药水选择标题
        potion_select_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 200,
            500,
            30
        )
        potion_select_title = self.ui_manager.create_text(
            potion_select_title_rect,
            "药水选择设置",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(potion_select_title)
        
        # HP药水选择
        hp_potion_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 240,
            150,
            30
        )
        hp_potion_title = self.ui_manager.create_text(
            hp_potion_title_rect,
            "生命药水:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(hp_potion_title)
        
        # HP药水下拉菜单
        hp_dropdown_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 240,
            250,
            30
        )
        self.hp_potion_dropdown = self.ui_manager.create_dropdown(
            hp_dropdown_rect,
            ["无可用生命药水"],
            0,
            self._on_hp_potion_select
        )
        self.add_component(self.hp_potion_dropdown)
        
        # MP药水选择
        mp_potion_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 280,
            150,
            30
        )
        mp_potion_title = self.ui_manager.create_text(
            mp_potion_title_rect,
            "魔法药水:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(mp_potion_title)
        
        # MP药水下拉菜单
        mp_dropdown_rect = pygame.Rect(
            panel_rect.x + 200,
            panel_rect.y + 280,
            250,
            30
        )
        self.mp_potion_dropdown = self.ui_manager.create_dropdown(
            mp_dropdown_rect,
            ["无可用魔法药水"],
            0,
            self._on_mp_potion_select
        )
        self.add_component(self.mp_potion_dropdown)
        
        # 已选HP药水显示
        self.hp_potions_text = self.ui_manager.create_text(
            pygame.Rect(
                panel_rect.x + 50,
                panel_rect.y + 320,
                500,
                20
            ),
            "当前选择的生命药水: 无",
            "chinese_small",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.hp_potions_text)
        
        # 已选MP药水显示
        self.mp_potions_text = self.ui_manager.create_text(
            pygame.Rect(
                panel_rect.x + 50,
                panel_rect.y + 350,
                500,
                20
            ),
            "当前选择的魔法药水: 无",
            "chinese_small",
            (200, 200, 200),
            "left"
        )
        self.add_component(self.mp_potions_text)
        
        # 创建说明文本
        desc_text_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 380,
            500,
            40
        )
        desc_text = self.ui_manager.create_text(
            desc_text_rect,
            "自动吃药系统会在您的HP或MP低于设定阈值时\n自动使用选择的生命药水或魔法药水",
            "chinese_normal",
            (180, 180, 200),
            "left"
        )
        self.add_component(desc_text)
        
        # 添加自动出售装备设置标题
        auto_sell_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 430,
            200,
            40
        )
        auto_sell_title = self.ui_manager.create_text(
            auto_sell_title_rect,
            "自动出售装备",
            "chinese_large",
            (220, 220, 220),
            "left"
        )
        self.add_component(auto_sell_title)
        
        # 自动出售启用/禁用按钮
        auto_sell_btn_rect = pygame.Rect(
            panel_rect.x + 300,
            panel_rect.y + 435,
            100,
            30
        )
        self.auto_sell_btn = self.ui_manager.create_button(
            auto_sell_btn_rect,
            "开启" if self.auto_sell_enabled else "关闭",
            self._toggle_auto_sell,
            "chinese_normal"
        )
        self.add_component(self.auto_sell_btn)
        
        # 自动出售说明文本
        sell_desc_text_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 470,
            500,
            20
        )
        sell_desc_text = self.ui_manager.create_text(
            sell_desc_text_rect,
            "自动出售系统会根据装备品质自动出售符合条件的装备",
            "chinese_normal",
            (180, 180, 200),
            "left"
        )
        self.add_component(sell_desc_text)
        
        # 自动出售品质选择
        quality_title_rect = pygame.Rect(
            panel_rect.x + 50,
            panel_rect.y + 500,
            200,
            30
        )
        quality_title = self.ui_manager.create_text(
            quality_title_rect,
            "自动出售品质设置:",
            "chinese_normal",
            (220, 220, 220),
            "left"
        )
        self.add_component(quality_title)
        
        # 品质复选框
        qualities = ["普通", "精良", "稀有", "史诗", "传说"]
        quality_colors = {
            "普通": (200, 200, 200),
            "精良": (0, 200, 0),
            "稀有": (0, 100, 255),
            "史诗": (180, 0, 255),
            "传说": (255, 140, 0)
        }
        
        # 创建每个品质的复选框
        checkbox_y = panel_rect.y + 530
        checkbox_spacing = 30
        self.quality_checkboxes = {}
        
        for i, quality in enumerate(qualities):
            checkbox_rect = pygame.Rect(
                panel_rect.x + 70,
                checkbox_y + i * checkbox_spacing,
                20,
                20
            )
            
            # 创建一个Panel作为复选框
            checkbox = self.ui_manager.create_panel(
                checkbox_rect,
                color=(60, 60, 80),
                border_color=(120, 120, 150),
                border_width=2
            )
            
            # 如果该品质被选中，则显示复选标记
            if self.sellable_qualities.get(quality, False):
                check_rect = pygame.Rect(
                    checkbox_rect.x + 4,
                    checkbox_rect.y + 4,
                    12,
                    12
                )
                checkbox_check = self.ui_manager.create_panel(
                    check_rect,
                    color=(100, 255, 100),
                    border_width=0
                )
                self.add_component(checkbox_check)
                # 保存复选标记引用
                checkbox.check_mark = checkbox_check
            else:
                checkbox.check_mark = None
                
            # 为复选框添加点击事件 - 修复闭包问题
            # 使用默认参数捕获当前的 quality 和 checkbox 实例
            checkbox.handle_event = lambda event, q=quality, cb=checkbox: self._on_quality_checkbox_click(event, q, cb)
            
            self.add_component(checkbox)
            self.quality_checkboxes[quality] = checkbox
            
            # 添加品质文本标签
            quality_label_rect = pygame.Rect(
                panel_rect.x + 100,
                checkbox_y + i * checkbox_spacing,
                100,
                20
            )
            quality_label = self.ui_manager.create_text(
                quality_label_rect,
                quality,
                "chinese_normal",
                quality_colors.get(quality, (220, 220, 220)),
                "left"
            )
            self.add_component(quality_label)
        
        # 创建保存按钮
        save_btn_rect = pygame.Rect(
            panel_rect.x + panel_width - 200,
            panel_rect.y + panel_height - 60,
            120,
            40
        )
        save_btn = self.ui_manager.create_button(
            save_btn_rect,
            "保存设置",
            self._on_save_click,
            "chinese_large"
        )
        self.add_component(save_btn)
        
        # 创建返回按钮
        back_btn_rect = pygame.Rect(
            panel_rect.x + 80,
            panel_rect.y + panel_height - 60,
            120,
            40
        )
        back_btn = self.ui_manager.create_button(
            back_btn_rect,
            "返回",
            self._on_back_click,
            "chinese_large"
        )
        self.add_component(back_btn)
    
    def _toggle_auto_potion(self):
        """切换自动吃药设置"""
        self.auto_potion_enabled = not self.auto_potion_enabled
        self._update_ui()
    
    def _adjust_hp_threshold(self, delta):
        """调整HP阈值"""
        self.hp_threshold = max(5, min(95, self.hp_threshold + delta))
        self._update_ui()
    
    def _adjust_mp_threshold(self, delta):
        """调整MP阈值"""
        self.mp_threshold = max(5, min(95, self.mp_threshold + delta))
        self._update_ui()
    
    def _on_hp_potion_select(self, index):
        """处理生命药水选择"""
        if 0 <= index < len(self.available_hp_potions):
            selected_potion = self.available_hp_potions[index]
            
            # 更新选择的药水列表 - 修改为替换逻辑 (假设单选或优先选第一个)
            if selected_potion != "无可用生命药水":
                # 如果只允许选一个，就这样写：
                # self.selected_hp_potions = [selected_potion]
                
                # 如果允许多选，但选择一项就重置为这一项作为最高优先级 (更像下拉菜单逻辑)
                # 或者，如果需要维护一个优先级列表，这里的交互需要重新设计
                # 暂时修改为只选择一个药水
                self.selected_hp_potions = [selected_potion] 
                logger.debug(f"HP Potion selected (replaced): {selected_potion}")
            else:
                # 如果选择了 "无可用..."，则清空选择
                self.selected_hp_potions = []
                logger.debug("HP Potion selection cleared.")
                
            self._update_potion_texts()
    
    def _on_mp_potion_select(self, index):
        """处理魔法药水选择"""
        if 0 <= index < len(self.available_mp_potions):
            selected_potion = self.available_mp_potions[index]
            
            # 更新选择的药水列表 - 修改为替换逻辑
            if selected_potion != "无可用魔法药水":
                # 暂时修改为只选择一个药水
                self.selected_mp_potions = [selected_potion]
                logger.debug(f"MP Potion selected (replaced): {selected_potion}")
            else:
                # 如果选择了 "无可用..."，则清空选择
                self.selected_mp_potions = []
                logger.debug("MP Potion selection cleared.")
                
            self._update_potion_texts()
    
    def _update_potion_texts(self):
        """更新药水选择文本显示"""
        # 更新生命药水文本
        if self.selected_hp_potions:
            hp_text = "当前选择的生命药水: " + ", ".join(self.selected_hp_potions)
        else:
            hp_text = "当前选择的生命药水: 无"
        self.hp_potions_text.set_text(hp_text)
        
        # 更新魔法药水文本
        if self.selected_mp_potions:
            mp_text = "当前选择的魔法药水: " + ", ".join(self.selected_mp_potions)
        else:
            mp_text = "当前选择的魔法药水: 无"
        self.mp_potions_text.set_text(mp_text)
    
    def _load_available_potions(self):
        """加载可用的药水列表"""
        if not hasattr(self.game_manager, 'auto_potion'):
            logger.warning("Settings screen: game_manager.auto_potion not found!")
            return
            
        logger.debug("Settings screen: Calling get_available_potions()")
        # 获取玩家背包中的药水
        potions = self.game_manager.auto_potion.get_available_potions()
        logger.debug(f"Settings screen: Potions received: {potions}")
        
        # 更新可用药水列表
        self.available_hp_potions = potions.get("hp_potions", [])
        if not self.available_hp_potions:
            self.available_hp_potions = ["无可用生命药水"]
            
        self.available_mp_potions = potions.get("mp_potions", [])
        if not self.available_mp_potions:
            self.available_mp_potions = ["无可用魔法药水"]
            
        self.available_dual_potions = potions.get("dual_potions", [])
        
        # 更新下拉菜单选项
        if self.hp_potion_dropdown:
            logger.debug(f"Settings screen: Setting HP dropdown options: {self.available_hp_potions}")
            self.hp_potion_dropdown.set_options(self.available_hp_potions)
            
        if self.mp_potion_dropdown:
            logger.debug(f"Settings screen: Setting MP dropdown options: {self.available_mp_potions}")
            self.mp_potion_dropdown.set_options(self.available_mp_potions)
    
    def _update_ui(self):
        """更新UI显示"""
        # 更新自动吃药按钮文本
        self.auto_potion_btn.set_text("开启" if self.auto_potion_enabled else "关闭")
        
        # 更新HP阈值滑块宽度
        hp_slider_width = int(200 * (self.hp_threshold / 100))
        self.hp_slider.rect.width = hp_slider_width # 直接修改 rect.width
        self.hp_text.set_text(f"{self.hp_threshold}%")
        
        # 更新MP阈值滑块宽度
        mp_slider_width = int(200 * (self.mp_threshold / 100))
        self.mp_slider.rect.width = mp_slider_width # 直接修改 rect.width
        self.mp_text.set_text(f"{self.mp_threshold}%")
        
        # 更新自动出售按钮文本
        if hasattr(self, 'auto_sell_btn'):
            self.auto_sell_btn.set_text("开启" if self.auto_sell_enabled else "关闭")
            
        # 更新品质复选框
        if hasattr(self, 'quality_checkboxes'):
            for quality, checkbox in self.quality_checkboxes.items():
                if self.sellable_qualities.get(quality, False):
                    # 如果该品质被选中但没有复选标记，则添加
                    if not hasattr(checkbox, 'check_mark') or not checkbox.check_mark:
                        check_rect = pygame.Rect(
                            checkbox.rect.x + 4,
                            checkbox.rect.y + 4,
                            12,
                            12
                        )
                        checkbox_check = self.ui_manager.create_panel(
                            check_rect,
                            color=(100, 255, 100),
                            border_width=0
                        )
                        self.add_component(checkbox_check)
                        checkbox.check_mark = checkbox_check
                else:
                    # 如果该品质未被选中但有复选标记，则移除
                    if hasattr(checkbox, 'check_mark') and checkbox.check_mark:
                        self.remove_component(checkbox.check_mark)
                        checkbox.check_mark = None
    
    def _toggle_auto_sell(self):
        """切换自动出售装备设置"""
        self.auto_sell_enabled = not self.auto_sell_enabled
        self._update_ui()
        
    def _on_quality_checkbox_click(self, event, quality, checkbox):
        """处理品质复选框点击"""
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            mouse_pos = pygame.mouse.get_pos()
            if checkbox.rect.collidepoint(mouse_pos):
                # 只切换该品质的自动出售状态
                self.sellable_qualities[quality] = not self.sellable_qualities.get(quality, False)
                logger.debug(f"Toggled quality '{quality}' to {self.sellable_qualities[quality]}")

                # 不再直接修改UI，而是调用 _update_ui 来同步显示
                self._update_ui() 
                        
                return True  # 事件已处理

        # 如果事件不是相关的鼠标点击，返回False
        return False
    
    def _on_save_click(self):
        """处理保存设置按钮点击"""
        player = self.game_manager.player
        if not player:
            return
            
        # 保存自动吃药设置
        player.auto_potion_enabled = self.auto_potion_enabled
        player.hp_potion_threshold = self.hp_threshold
        player.mp_potion_threshold = self.mp_threshold
        
        # 保存选中的药水
        player.selected_hp_potions = self.selected_hp_potions.copy()
        player.selected_mp_potions = self.selected_mp_potions.copy()
        
        # 保存自动出售设置
        player.auto_sell_enabled = self.auto_sell_enabled
        player.sellable_qualities = self.sellable_qualities.copy()
        
        # 保存游戏
        self.game_manager.save_game()
        
        # 显示保存成功消息
        self.ui_manager.show_message("保存成功", "设置已保存")
        
        # 返回到上一个界面
        self._on_back_click()
    
    def _on_back_click(self):
        """处理返回按钮点击"""
        self.ui_manager.show_screen("game")
    
    def show(self):
        """显示设置界面"""
        super().show()
        
        # 加载玩家设置
        player = self.game_manager.player
        if player:
            # 加载自动吃药设置
            self.auto_potion_enabled = getattr(player, 'auto_potion_enabled', False)
            self.hp_threshold = getattr(player, 'hp_potion_threshold', 50)
            self.mp_threshold = getattr(player, 'mp_potion_threshold', 30)
            
            # 加载选中的药水
            self.selected_hp_potions = getattr(player, 'selected_hp_potions', []).copy()
            self.selected_mp_potions = getattr(player, 'selected_mp_potions', []).copy()
            
            # 加载自动出售设置
            self.auto_sell_enabled = getattr(player, 'auto_sell_enabled', False)
            
            # 获取玩家的可出售品质设置或使用默认值
            default_qualities = {
                "普通": True,
                "精良": False,
                "稀有": False,
                "史诗": False,
                "传说": False
            }
            self.sellable_qualities = getattr(player, 'sellable_qualities', self.sellable_qualities).copy()
        
        # --- 新增：加载可用药水列表填充下拉菜单 ---
        self._load_available_potions() 
        # ---------------------------------------

        # 更新UI以反映加载的设置
        self._update_ui() 
        self._update_potion_texts() # 确保初始加载时也更新已选药水文本