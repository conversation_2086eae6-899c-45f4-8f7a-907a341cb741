[2025-05-22 19:28:50] [WARNING] [main.py:106] 游戏启动
[2025-05-22 19:28:50] [WARNING] [main.py:232] 开始创建游戏界面
[2025-05-22 19:28:50] [WARNING] [main.py:324] 游戏界面创建完成
[2025-05-22 19:29:03] [WARNING] [game_screen.py:3427] 无法获取技能配置: teleport
[2025-05-22 19:29:05] [WARNING] [game_screen.py:3427] 无法获取技能配置: teleport
[2025-05-22 19:29:06] [WARNING] [game_screen.py:3427] 无法获取技能配置: teleport
[2025-05-22 19:30:03] [WARNING] [game_screen.py:3652] 使用火球术失败: 没有目标
[2025-05-22 19:30:23] [ERROR] [main.py:601] 游戏发生未处理的异常: 'Player' object has no attribute 'get_skill_cooldown'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 597, in main
    game_instance.run() # run() 现在包含清理逻辑
    ^^^^^^^^^^^^^^^^^^^
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 348, in run
    self._update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\main.py", line 418, in _update
    self.ui_manager.update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\ui_manager.py", line 385, in update
    self.screens[self.active_screen].update(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 2140, in update
    self._auto_cast_skills(dt)
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\game_screen.py", line 3343, in _auto_cast_skills
    cooldown_remaining = player.get_skill_cooldown(skill_id)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Player' object has no attribute 'get_skill_cooldown'. Did you mean: 'get_skill_cooldown_info'?
