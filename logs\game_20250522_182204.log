[2025-05-22 18:22:04] [WARNING] [main.py:105] 游戏启动
[2025-05-22 18:22:04] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 18:22:04] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 18:22:08] [ERROR] [dungeon_screen.py:245] 创建副本界面组件时发生错误: UIManager.create_button() got an unexpected keyword argument 'transparent'
Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\ui\screens\dungeon_screen.py", line 162, in _create_components
    tab_button = self.ui_manager.create_button(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: UIManager.create_button() got an unexpected keyword argument 'transparent'
[2025-05-22 18:22:08] [ERROR] [components.py:344] 按钮回调函数出错: 按钮[副本挑战] - AttributeError: 'DungeonScreen' object has no attribute 'dungeon_panels'
