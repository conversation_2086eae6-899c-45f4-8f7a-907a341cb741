import json
import os
import shutil
import traceback
import time
from utils.logger import logger
from utils.translation_manager import TranslationManager
from utils.safe_file import SafeFileHandler

class SaveManager:
    def __init__(self):
        self.save_dir = "data/saves"
        self.translation_manager = TranslationManager()
        self._ensure_save_dir()
        
        # 初始化时自动修复所有存档
        logger.info("===== 初始化存档管理器，开始检查所有存档 =====")
        self.fix_all_save_files()
    
    def _ensure_save_dir(self):
        """确保存档目录存在"""
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
            logger.info(f"创建存档目录: {self.save_dir}")
    
    def save_game(self, game_data, save_name):
        """保存游戏数据
        
        Args:
            game_data: 游戏数据
            save_name: 存档名
            
        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info("开始执行存档过程")
            
            # 检查游戏数据是否有效
            if not game_data:
                logger.error("保存游戏失败：游戏数据为空")
                return False
                
            # 准备文件路径
            save_path = os.path.join(self.save_dir, f"{save_name}_save.json")
            logger.info(f"准备保存游戏到: {save_path}")
            
            # 确保存档目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 使用 SafeFileHandler 进行安全保存
            return SafeFileHandler.safe_save(save_path, game_data)
                    
        except Exception as e:
            logger.error(f"保存游戏过程中出现异常: {e}")
            logger.error(f"保存错误详情: {traceback.format_exc()}")
            return False
    
    def load_game(self, save_name):
        """加载游戏数据
        
        Args:
            save_name: 存档名
            
        Returns:
            dict or None: 加载的游戏数据，失败则返回None
        """
        # 构建存档文件路径
        save_path = os.path.join(self.save_dir, f"{save_name}_save.json")
        logger.info(f"尝试加载存档: {save_path}")
        
        # 检查文件是否存在
        if not os.path.exists(save_path):
            logger.error(f"存档文件不存在: {save_path}")
            return None
        
        # 读取存档文件
        data = None
        save_file_data = None
        
        try:
            with open(save_path, "rb") as f:
                save_file_data = f.read()
        except Exception as e:
            logger.error(f"读取存档文件时出错: {e}")
            return None
            
        if not save_file_data:
            logger.error("存档文件内容为空")
            return None
            
        # 尝试多种编码读取
        for encoding in ["utf-8", "gbk", "gb2312", "big5", "latin1"]:
            try:
                text = save_file_data.decode(encoding)
                data = json.loads(text)
                logger.info(f"使用 {encoding} 编码成功加载存档: {save_name}_save.json")
                break
            except UnicodeDecodeError:
                logger.warning(f"使用 {encoding} 编码解析失败")
                continue
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，编码:{encoding}, 错误: {e}")
                continue
            except Exception as e:
                logger.warning(f"解析数据时出现其他错误，编码:{encoding}, 错误: {e}")
                continue
        
        # 如果所有编码尝试都失败
        if not data:
            logger.error("无法解析存档数据，所有编码尝试都失败")
            # 尝试使用SafeFileHandler修复
            logger.info("尝试使用SafeFileHandler修复并加载...")
            success, fixed_data, error_msg = SafeFileHandler.safe_load(save_path)
            if success:
                logger.info("SafeFileHandler成功修复并加载了存档")
                return fixed_data
            return None
        
        # 验证数据格式
        if not isinstance(data, dict):
            logger.error(f"数据不是字典类型: {type(data)}")
            return None
            
        logger.info(f"存档数据包含键: {', '.join(data.keys())}")
        
        # 检查并修复数据结构
        if 'player' not in data and any(key in data for key in ['character_class', 'level', 'name']):
            # 旧格式数据，转换为新格式
            logger.info("检测到旧格式存档数据，转换为新格式")
            player_data = {}
            for key in list(data.keys()):
                if key not in ['current_map', 'save_time', 'game_state']:
                    player_data[key] = data.pop(key)
            data['player'] = player_data
            
            # 将修复后的数据写回文件
            try:
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info("已将旧格式存档转换为新格式并保存")
            except Exception as e:
                logger.warning(f"保存修复后的存档失败: {e}")
        
        # 记录加载成功
        try:
            player_info = f"玩家名称: {data.get('player', {}).get('name', 'Unknown')}, 等级: {data.get('player', {}).get('level', 'Unknown')}"
            logger.info(f"加载数据摘要: {player_info}")
        except:
            logger.info("无法显示数据摘要")
        
        return data
    
    def get_save_files(self):
        """获取所有存档文件"""
        try:
            files = [f for f in os.listdir(self.save_dir) if f.endswith('_save.json')]
            logger.info(f"找到 {len(files)} 个存档文件")
            return [f.replace('_save.json', '') for f in files]
        except Exception as e:
            logger.error(f"获取存档文件列表失败: {e}")
            logger.error(traceback.format_exc())
            return []
            
    def fix_save_file(self, file_path):
        """修复单个存档文件的编码

        Args:
            file_path: 存档文件路径

        Returns:
            bool: 是否成功修复
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"要修复的文件不存在: {file_path}")
                return False
                
            # 读取文件内容
            try:
                with open(file_path, 'rb') as f:
                    save_file_data = f.read()
                
                if not save_file_data:
                    logger.error("存档文件内容为空")
                    return False
                    
                # 尝试多种编码读取
                for encoding in ["utf-8", "gbk", "gb2312", "big5", "latin1"]:
                    try:
                        text = save_file_data.decode(encoding)
                        data = json.loads(text)
                        logger.info(f"使用 {encoding} 编码成功读取文件")
                        
                        # 如果不是UTF-8编码，重新保存为UTF-8
                        if encoding != "utf-8":
                            logger.info(f"将文件从 {encoding} 编码转换为 UTF-8 编码")
                            # 创建备份
                            backup_path = f"{file_path}.bak"

                            # 使用 SafeFileHandler 进行安全保存
                            if SafeFileHandler.safe_save(file_path, data):
                                logger.info(f"文件已成功通过 SafeFileHandler 转换为UTF-8编码: {file_path}")
                            else:
                                logger.error(f"使用 SafeFileHandler 保存UTF-8编码失败: {file_path}")
                                # 如果安全保存失败，可能需要考虑恢复备份或者其他错误处理
                                return False # 保存失败，修复未完成

                        return True
                    except UnicodeDecodeError:
                        continue
                    except json.JSONDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"解析数据时出现其他错误，编码:{encoding}, 错误: {e}")
                        continue
                
                # 如果所有编码尝试都失败
                logger.warning(f"无法修复存档文件，所有编码尝试都失败: {file_path}")
                return False
                
            except Exception as read_err:
                logger.error(f"读取文件内容失败: {read_err}")
                return False
                
        except Exception as e:
            logger.error(f"修复存档文件时出错: {e}")
            logger.error(traceback.format_exc())
            return False
            
    def fix_all_save_files(self):
        """修复所有存档文件的编码"""
        logger.info("开始检查所有存档文件的编码...")
        if not os.path.exists(self.save_dir):
            logger.warning(f"存档目录不存在: {self.save_dir}")
            return
            
        # 查找所有存档文件
        save_files = []
        for filename in os.listdir(self.save_dir):
            # 统一文件后缀检查
            if filename.endswith('_save.json'):

                file_path = os.path.join(self.save_dir, filename)
                save_files.append(file_path)
        
        logger.info(f"找到 {len(save_files)} 个存档文件")
        for file_path in save_files:
            logger.info(f"发现存档文件: {file_path}")
        
        # 修复每个存档文件
        success_count = 0
        fail_count = 0
        
        for file_path in save_files:
            logger.info(f"检查文件: {file_path}")
            if self.fix_save_file(file_path):
                success_count += 1
            else:
                fail_count += 1
                
        # 更新日志信息使其更清晰
        logger.info(f"存档检查完成。成功处理: {success_count}, 处理失败: {fail_count}")
        return success_count, fail_count