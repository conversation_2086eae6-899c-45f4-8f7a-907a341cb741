# -*- coding: utf-8 -*-
"""
玩家面板
显示玩家的基本信息、属性、生命值、魔法值、经验值等
"""

import pygame
from typing import Dict, Any, Optional, TYPE_CHECKING
from ui.screens.game_ui_base import GameUIPanel, GameUIConstants, UIComponentFactory
from core.class_stats import get_class_stats, get_level_stats
from utils.logger import logger

if TYPE_CHECKING:
    from core.game import Game

class PlayerPanel(GameUIPanel):
    """玩家面板类"""
    
    def __init__(self, ui_manager, game_manager: 'Game', panel_rect: pygame.Rect):
        """
        初始化玩家面板
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
            panel_rect: 面板区域
        """
        super().__init__(ui_manager, game_manager, "player_panel")
        self.panel_rect = panel_rect
        self.player_stats_components = {}
        self.update_interval = GameUIConstants.UPDATE_INTERVALS["player_stats"]
        
        self.create_components()
    
    def create_components(self):
        """创建玩家面板组件"""
        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            self.panel_rect,
            color=GameUIConstants.COLORS["panel_bg"],
            border_color=GameUIConstants.COLORS["panel_border"],
            border_width=GameUIConstants.SIZES["border_width"]
        )
        self.add_component(main_panel)
        self.components_map["main_panel"] = main_panel
        
        # 获取默认属性（用于初始化）
        default_stats = self._get_default_stats()
        
        # 创建玩家名称和等级
        self._create_player_name_level(default_stats)
        
        # 创建生命值条和文本
        self._create_hp_display(default_stats)
        
        # 创建魔法值条和文本
        self._create_mp_display(default_stats)
        
        # 创建经验条和文本
        self._create_exp_display(default_stats)
        
        # 创建属性显示
        self._create_stats_display(default_stats)
        
        # 创建状态显示
        self._create_status_display()
        
        logger.debug("玩家面板组件创建完成")
    
    def _get_default_stats(self) -> Dict[str, Any]:
        """获取默认属性"""
        default_class = "战士"
        class_stats = get_class_stats(default_class)
        level_stats = get_level_stats(default_class, 1)
        
        return {
            "name": "未命名",
            "class": default_class,
            "level": 1,
            "hp": level_stats[0],
            "max_hp": level_stats[0],
            "mp": level_stats[1],
            "max_mp": level_stats[1],
            "exp": 0,
            "required_exp": 100,
            "attack": level_stats[2],
            "defense": level_stats[4] if len(level_stats) > 4 else 0,
            "magic_attack": 0,
            "magic_defense": class_stats.get('magic_defense', 0),
            "attack_speed": 1.0,
            "luck": 0,
            "accuracy": class_stats.get('accuracy', 0),
            "agility": class_stats.get('agility', 0)
        }
    
    def _create_player_name_level(self, stats: Dict[str, Any]):
        """创建玩家名称和等级显示"""
        name_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 20,
            self.panel_rect.width - 40,
            30
        )
        name_text = self.ui_manager.create_text(
            name_rect,
            f"{stats['name']} {stats['class']} Lv.{stats['level']}",
            GameUIConstants.FONTS["large"],
            GameUIConstants.COLORS["text_primary"],
            "left"
        )
        self.add_component(name_text)
        self.player_stats_components["name"] = name_text
    
    def _create_hp_display(self, stats: Dict[str, Any]):
        """创建生命值显示"""
        # 生命值条
        hp_bar_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 60,
            self.panel_rect.width - 40,
            GameUIConstants.SIZES["bar_height"]
        )
        hp_bar = UIComponentFactory.create_progress_bar(
            self.ui_manager, hp_bar_rect, stats["hp"], stats["max_hp"], "hp"
        )
        self.add_component(hp_bar)
        self.player_stats_components["hp_bar"] = hp_bar
        
        # 生命值文本
        hp_text_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 85,
            self.panel_rect.width - 40,
            20
        )
        hp_text = self.ui_manager.create_text(
            hp_text_rect,
            f"HP: {stats['hp']}/{stats['max_hp']}",
            GameUIConstants.FONTS["small"],
            GameUIConstants.COLORS["text_primary"],
            "left"
        )
        self.add_component(hp_text)
        self.player_stats_components["hp_text"] = hp_text
    
    def _create_mp_display(self, stats: Dict[str, Any]):
        """创建魔法值显示"""
        # 魔法值条
        mp_bar_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 115,
            self.panel_rect.width - 40,
            GameUIConstants.SIZES["bar_height"]
        )
        mp_bar = UIComponentFactory.create_progress_bar(
            self.ui_manager, mp_bar_rect, stats["mp"], stats["max_mp"], "mp"
        )
        self.add_component(mp_bar)
        self.player_stats_components["mp_bar"] = mp_bar
        
        # 魔法值文本
        mp_text_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 140,
            self.panel_rect.width - 40,
            20
        )
        mp_text = self.ui_manager.create_text(
            mp_text_rect,
            f"MP: {stats['mp']}/{stats['max_mp']}",
            GameUIConstants.FONTS["small"],
            GameUIConstants.COLORS["text_primary"],
            "left"
        )
        self.add_component(mp_text)
        self.player_stats_components["mp_text"] = mp_text
    
    def _create_exp_display(self, stats: Dict[str, Any]):
        """创建经验值显示"""
        # 经验值条
        exp_bar_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 170,
            self.panel_rect.width - 40,
            GameUIConstants.SIZES["bar_height"]
        )
        exp_bar = UIComponentFactory.create_progress_bar(
            self.ui_manager, exp_bar_rect, stats["exp"], stats["required_exp"], "exp"
        )
        self.add_component(exp_bar)
        self.player_stats_components["exp_bar"] = exp_bar
        
        # 经验值文本
        exp_text_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 195,
            self.panel_rect.width - 40,
            20
        )
        exp_text = self.ui_manager.create_text(
            exp_text_rect,
            f"经验: {stats['exp']}/{stats['required_exp']}",
            GameUIConstants.FONTS["small"],
            GameUIConstants.COLORS["text_primary"],
            "left"
        )
        self.add_component(exp_text)
        self.player_stats_components["exp_text"] = exp_text
    
    def _create_stats_display(self, stats: Dict[str, Any]):
        """创建属性显示"""
        # 属性标题
        stats_title_rect = pygame.Rect(
            self.panel_rect.x + 20,
            self.panel_rect.y + 230,
            self.panel_rect.width - 40,
            25
        )
        stats_title = self.ui_manager.create_text(
            stats_title_rect,
            "属性",
            GameUIConstants.FONTS["normal"],
            GameUIConstants.COLORS["text_secondary"],
            "left"
        )
        self.add_component(stats_title)
        
        # 属性列表
        stats_info = [
            ("攻击", str(stats["attack"])),
            ("防御", str(stats["defense"])),
            ("魔法攻击", str(stats["magic_attack"])),
            ("道术攻击", "0"),
            ("魔法防御", str(stats["magic_defense"])),
            ("攻击速度", str(stats["attack_speed"])),
            ("幸运", str(stats["luck"])),
            ("准确", str(stats["accuracy"])),
            ("敏捷", str(stats["agility"]))
        ]
        
        y_offset = 260
        for i, (stat_name, stat_value) in enumerate(stats_info):
            stat_rect = pygame.Rect(
                self.panel_rect.x + 20,
                self.panel_rect.y + y_offset + i * GameUIConstants.SIZES["text_height"],
                self.panel_rect.width - 40,
                20
            )
            
            label_text, value_text = UIComponentFactory.create_stat_display(
                self.ui_manager, stat_rect, stat_name, stat_value
            )
            
            self.add_component(label_text)
            self.add_component(value_text)
            self.player_stats_components[f"{stat_name}_text"] = value_text
    
    def _create_status_display(self):
        """创建状态显示"""
        # 计算状态显示位置（在属性列表下方）
        status_y = self.panel_rect.y + 260 + 9 * GameUIConstants.SIZES["text_height"] + 10
        
        status_rect = pygame.Rect(
            self.panel_rect.x + 20,
            status_y,
            self.panel_rect.width - 40,
            30
        )
        status_text = self.ui_manager.create_text(
            status_rect,
            "状态: 无",
            GameUIConstants.FONTS["small"],
            (200, 200, 255),  # 淡蓝色
            "left"
        )
        self.add_component(status_text)
        self.player_stats_components["status_text"] = status_text
    
    def update_data(self):
        """更新玩家数据"""
        if not hasattr(self.game_manager, 'player') or not self.game_manager.player:
            return
        
        player = self.game_manager.player
        
        try:
            # 更新名称和等级
            if "name" in self.player_stats_components:
                name_text = f"{player.name} {player.character_class} Lv.{player.level}"
                self.player_stats_components["name"].set_text(name_text)
            
            # 更新生命值
            if "hp_bar" in self.player_stats_components:
                self.player_stats_components["hp_bar"].update_value(player.current_hp, player.max_hp)
            if "hp_text" in self.player_stats_components:
                self.player_stats_components["hp_text"].set_text(f"HP: {player.current_hp}/{player.max_hp}")
            
            # 更新魔法值
            if "mp_bar" in self.player_stats_components:
                self.player_stats_components["mp_bar"].update_value(player.current_mp, player.max_mp)
            if "mp_text" in self.player_stats_components:
                self.player_stats_components["mp_text"].set_text(f"MP: {player.current_mp}/{player.max_mp}")
            
            # 更新经验值
            if "exp_bar" in self.player_stats_components:
                required_exp = player.get_required_exp()
                self.player_stats_components["exp_bar"].update_value(player.exp, required_exp)
            if "exp_text" in self.player_stats_components:
                required_exp = player.get_required_exp()
                self.player_stats_components["exp_text"].set_text(f"经验: {player.exp}/{required_exp}")
            
            # 更新属性
            self._update_player_stats(player)
            
            # 更新状态
            self._update_player_status(player)
            
        except Exception as e:
            logger.error(f"更新玩家面板数据时出错: {e}")
    
    def _update_player_stats(self, player):
        """更新玩家属性"""
        stats_mapping = {
            "攻击": player.get_total_attack(),
            "防御": player.get_total_defense(),
            "魔法攻击": player.get_total_magic_attack(),
            "魔法防御": player.get_total_magic_defense(),
            "攻击速度": player.get_attack_speed(),
            "幸运": player.get_total_luck(),
            "准确": player.get_total_accuracy(),
            "敏捷": player.get_total_agility()
        }
        
        for stat_name, stat_value in stats_mapping.items():
            component_key = f"{stat_name}_text"
            if component_key in self.player_stats_components:
                self.player_stats_components[component_key].set_text(str(stat_value))
    
    def _update_player_status(self, player):
        """更新玩家状态"""
        if "status_text" in self.player_stats_components:
            # 获取玩家当前状态
            status_list = []
            
            # 检查各种状态
            if hasattr(player, 'buffs') and player.buffs:
                for buff_name in player.buffs.keys():
                    status_list.append(buff_name)
            
            if hasattr(player, 'is_poisoned') and player.is_poisoned:
                status_list.append("中毒")
            
            if hasattr(player, 'is_stunned') and player.is_stunned:
                status_list.append("眩晕")
            
            # 更新状态显示
            if status_list:
                status_text = "状态: " + ", ".join(status_list)
            else:
                status_text = "状态: 无"
            
            self.player_stats_components["status_text"].set_text(status_text)
