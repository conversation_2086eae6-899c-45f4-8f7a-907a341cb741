from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # 基本设置
    APP_NAME: str = "RPG Game Server"
    DEBUG: bool = True

    # 安全设置
    SECRET_KEY: str = "your-secret-key-for-jwt"  # 在生产环境中应使用环境变量
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 24小时

    # 数据库设置
    MONGO_URI: str = "mongodb://localhost:27017"
    DATABASE_NAME: str = "rpg_game"
    MONGO_CONNECT_TIMEOUT_MS: int = 5000  # 连接超时时间（毫秒）
    MONGO_SERVER_SELECTION_TIMEOUT_MS: int = 5000  # 服务器选择超时时间（毫秒）

    class Config:
        env_file = ".env"

settings = Settings()
