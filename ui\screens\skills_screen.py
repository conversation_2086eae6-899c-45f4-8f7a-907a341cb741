import pygame
from typing import Dict, List, Optional, Any, Tuple

from ui.ui_manager import Screen
from utils.logger import logger
from core.game import Game
from utils.resource_manager import resources
from core.config import GameConfig

class SkillsScreen(Screen):
    """技能界面"""

    def __init__(self, ui_manager, game_manager: Game):
        """
        初始化技能界面

        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
        """
        super().__init__("skills")
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.components_map = {}  # 组件映射
        self.selected_skill_id = None  # 当前选中的技能ID
        self.skill_buttons = []  # 技能按钮列表

        # 创建界面组件
        self._create_components()

    def _create_components(self):
        """创建界面组件"""
        screen_size = pygame.display.get_surface().get_size()

        # 创建标题
        title_text = self.ui_manager.create_text(
            pygame.Rect(0, 20, screen_size[0], 40),
            "技能系统",
            "chinese_title",
            align="center"
        )
        self.add_component(title_text)

        # 创建返回按钮
        back_button = self.ui_manager.create_button(
            pygame.Rect(20, screen_size[1] - 60, 120, 40),
            "返回",
            self._on_back_click,
            "chinese_normal"
        )
        self.add_component(back_button)

        # 创建主面板
        main_panel = self.ui_manager.create_panel(
            pygame.Rect(50, 80, screen_size[0] - 100, screen_size[1] - 160),
            color=(30, 30, 50),
            border_color=(60, 60, 90),
            border_width=2,
            alpha=180
        )
        self.add_component(main_panel)

        # 创建技能列表区域面板
        skills_list_panel = self.ui_manager.create_panel(
            pygame.Rect(70, 100, 300, screen_size[1] - 200),
            color=(25, 25, 45),
            border_color=(50, 50, 80),
            border_width=1
        )
        self.add_component(skills_list_panel)

        # 添加技能列表标题
        skills_list_title = self.ui_manager.create_text(
            pygame.Rect(80, 110, 280, 30),
            "可用技能",
            "chinese_large",
            (220, 220, 220),
            align="center"
        )
        self.add_component(skills_list_title)

        # 添加职业标签
        character_class = "战士"
        if hasattr(self.game_manager, 'player') and self.game_manager.player is not None:
            player = self.game_manager.player
            if hasattr(player, 'character_class'):
                character_class = player.character_class

        class_text = self.ui_manager.create_text(
            pygame.Rect(80, 150, 200, 30),
            f"职业: {character_class}",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(class_text)
        self.components_map["class_text"] = class_text

        # 创建技能信息面板
        skill_info_panel = self.ui_manager.create_panel(
            pygame.Rect(screen_size[0] - 350, 100, 300, screen_size[1] - 200),
            color=(25, 25, 45),
            border_color=(50, 50, 80),
            border_width=1
        )
        self.add_component(skill_info_panel)

        # 添加技能信息标题
        skill_info_title = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 110, 280, 30),
            "技能详情",
            "chinese_large",
            (220, 220, 220),
            align="center"
        )
        self.add_component(skill_info_title)

        # 添加技能名称文本
        skill_name_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 150, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_name_text)
        self.components_map["skill_name"] = skill_name_text

        # 添加技能等级文本
        skill_level_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 180, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_level_text)
        self.components_map["skill_level"] = skill_level_text

        # 添加技能类型文本
        skill_type_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 210, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_type_text)
        self.components_map["skill_type"] = skill_type_text

        # 添加技能消耗文本
        skill_cost_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 240, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_cost_text)
        self.components_map["skill_cost"] = skill_cost_text

        # 添加技能冷却文本
        skill_cooldown_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 270, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_cooldown_text)
        self.components_map["skill_cooldown"] = skill_cooldown_text

        # 添加技能效果文本
        skill_effect_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 300, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_effect_text)
        self.components_map["skill_effect"] = skill_effect_text

        # 添加技能等级需求文本
        skill_req_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 330, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_req_text)
        self.components_map["skill_req"] = skill_req_text

        # 添加技能描述文本
        skill_desc_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, 370, 280, 120),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_desc_text)
        self.components_map["skill_desc"] = skill_desc_text

        # 添加技能升级按钮
        upgrade_button = self.ui_manager.create_button(
            pygame.Rect(screen_size[0] - 250, screen_size[1] - 140, 150, 40),
            "升级技能",
            self._on_upgrade_click,
            "chinese_normal"
        )
        self.add_component(upgrade_button)
        self.components_map["upgrade_button"] = upgrade_button

        # 添加技能熟练度需求文本 (原 skill_book_req_text)
        skill_proficiency_req_text = self.ui_manager.create_text(
            pygame.Rect(screen_size[0] - 340, screen_size[1] - 95, 280, 30),
            "",
            "chinese_normal",
            (200, 200, 200)
        )
        self.add_component(skill_proficiency_req_text)
        self.components_map["skill_proficiency_req"] = skill_proficiency_req_text

        # 默认禁用升级按钮
        upgrade_button.set_active(False)

        # 创建技能列表
        self._create_skill_list()

    def _create_skill_list(self):
        """创建技能列表"""
        # 清除之前的技能按钮
        for button in self.skill_buttons:
            self.remove_component(button)

        self.skill_buttons = []

        # 获取角色职业
        character_class = "战士"
        player = None
        if hasattr(self.game_manager, 'player') and self.game_manager.player is not None:
            player = self.game_manager.player
            if hasattr(player, 'character_class'):
                character_class = player.character_class
            else:
                logger.warning("玩家对象没有character_class属性")

        # 获取该职业的所有技能
        skills_data = GameConfig.get_skills_for_class(character_class)

        # 如果没有技能配置，显示提示信息
        if not skills_data:
            empty_text = self.ui_manager.create_text(
                pygame.Rect(80, 190, 280, 30),
                "暂无可用技能",
                "chinese_normal",
                (200, 200, 200)
            )
            self.add_component(empty_text)
            self.skill_buttons.append(empty_text)
            return

        # 创建技能按钮
        y_offset = 190
        button_height = 30
        button_gap = 5
        button_width = 200  # 减小技能按钮宽度，为开关按钮留出空间

        for skill_id, skill_data in skills_data.items():
            # 获取已学习的技能等级
            skill_level = 0
            is_disabled = False
            if player:
                skill_level = player.skills.get(skill_id, 0)
                is_disabled = player.is_skill_disabled(skill_id)

            # 获取技能名称
            skill_name = skill_data["name"]

            # 创建技能按钮
            skill_button = self.ui_manager.create_button(
                pygame.Rect(80, y_offset, button_width, button_height),
                f"{skill_name} Lv.{skill_level}/{skill_data['max_level']}",
                lambda s_id=skill_id: self._on_skill_click(s_id),
                "chinese_normal"
            )
            self.add_component(skill_button)
            self.skill_buttons.append(skill_button)

            # 如果技能等级为0，调整按钮样式
            if skill_level == 0:
                skill_button.colors["normal"] = (50, 50, 70)
                skill_button.colors["hover"] = (60, 60, 80)
                skill_button.colors["text"] = (150, 150, 150)

            # 为已学习的技能添加开关按钮
            if skill_level > 0:
                # 创建开关按钮
                toggle_text = "禁用" if not is_disabled else "启用"
                toggle_color = (200, 50, 50) if not is_disabled else (50, 200, 50)

                toggle_button = self.ui_manager.create_button(
                    pygame.Rect(290, y_offset, 60, button_height),
                    toggle_text,
                    lambda s_id=skill_id: self._on_toggle_click(s_id),
                    "chinese_small"
                )
                toggle_button.colors["normal"] = toggle_color
                toggle_button.colors["hover"] = (min(toggle_color[0] + 20, 255),
                                               min(toggle_color[1] + 20, 255),
                                               min(toggle_color[2] + 20, 255))

                self.add_component(toggle_button)
                self.skill_buttons.append(toggle_button)

            y_offset += button_height + button_gap

    def _on_skill_click(self, skill_id):
        """处理技能按钮点击"""
        self.selected_skill_id = skill_id

        # 更新技能信息
        self._update_skill_info(skill_id)

        # 检查是否可以升级技能
        player = self.game_manager.player if hasattr(self.game_manager, 'player') else None
        if player:
            can_upgrade = player.can_learn_skill(skill_id)
            self.components_map["upgrade_button"].set_active(can_upgrade)
        else:
            self.components_map["upgrade_button"].set_active(False)

    def _wrap_text(self, text, max_chars_per_line=25):
        """智能文本换行，支持中文和标点符号"""
        if not text:
            return ""

        # 首先处理已有的换行符
        text = text.replace('\\n', '\n')

        # 如果文本已经包含换行符，按行处理
        lines = text.split('\n')
        wrapped_lines = []

        for line in lines:
            if len(line) <= max_chars_per_line:
                wrapped_lines.append(line)
            else:
                # 对长行进行智能换行
                wrapped_line = self._wrap_single_line(line, max_chars_per_line)
                wrapped_lines.append(wrapped_line)

        return '\n'.join(wrapped_lines)

    def _wrap_single_line(self, text, max_chars_per_line):
        """对单行文本进行智能换行"""
        if len(text) <= max_chars_per_line:
            return text

        result = ""
        current_line = ""

        # 中文标点符号和英文标点符号
        punctuation = "。，、；：！？.,:;!?）】》"

        i = 0
        while i < len(text):
            char = text[i]
            current_line += char

            # 检查是否需要换行
            if len(current_line) >= max_chars_per_line:
                # 如果当前字符是标点符号，直接换行
                if char in punctuation:
                    result += current_line + "\n"
                    current_line = ""
                # 如果下一个字符是标点符号，等待下一个字符
                elif i + 1 < len(text) and text[i + 1] in punctuation:
                    pass  # 继续添加字符
                # 否则在当前位置换行
                else:
                    result += current_line + "\n"
                    current_line = ""

            i += 1

        # 添加最后一行
        if current_line:
            result += current_line

        return result

    def _update_skill_info(self, skill_id):
        """更新右侧技能详细信息"""
        if not skill_id:
            # 清空信息
            self.components_map["skill_name"].set_text("")
            self.components_map["skill_level"].set_text("")
            self.components_map["skill_cost"].set_text("")
            self.components_map["skill_cooldown"].set_text("")
            self.components_map["skill_effect"].set_text("")
            self.components_map["skill_req"].set_text("")
            self.components_map["skill_desc"].set_text("")
            self.components_map["upgrade_button"].set_active(False)
            # 清空熟练度需求文本
            if "skill_proficiency_req" in self.components_map:
                self.components_map["skill_proficiency_req"].set_text("")
                self.components_map["skill_proficiency_req"].visible = False
            return

        try:
            player = self.game_manager.player
            character_class = "战士" # 默认值
            current_level = 0
            if player:
                current_level = player.skills.get(skill_id, 0)
                if hasattr(player, 'character_class'):
                     character_class = player.character_class # 获取玩家职业

            # 获取技能配置信息 - 使用 get_skill 并传入职业
            skill_config = GameConfig.get_skill(skill_id, character_class) # 修改这里
            if not skill_config:
                logger.error(f"无法获取技能 {skill_id} (职业: {character_class}) 的配置")
                return

            # 更新技能名称
            skill_name = skill_config.get('name', skill_id)
            self.components_map["skill_name"].set_text(f"技能: {skill_name}")

            # 更新技能等级
            self.components_map["skill_level"].set_text(f"当前等级: {current_level}")


            # 更新技能消耗
            cost_type = skill_config.get('cost_type', 'mp')
            cost_value = skill_config.get('cost_value', 0)
            self.components_map["skill_cost"].set_text(f"消耗: {cost_value} {cost_type.upper()}")

            # 更新技能冷却
            cooldown = skill_config.get('cooldown', 0)
            self.components_map["skill_cooldown"].set_text(f"冷却时间: {cooldown} 秒")

            # 更新技能效果 (简化描述)
            effect_type = skill_config.get('effect_type', '无')
            effect_value = skill_config.get('effect_value', '无')
            effect_desc = self._get_effect_description(effect_type, effect_value)
            # 为效果描述也应用换行
            wrapped_effect = self._wrap_text(effect_desc, max_chars_per_line=25)
            self.components_map["skill_effect"].set_text(f"效果: {wrapped_effect}")

            # 更新技能等级需求
            req_level = skill_config.get('required_level', 1)
            self.components_map["skill_req"].set_text(f"等级: {req_level}")

            # 更新技能描述
            description = skill_config.get('description', '暂无描述')
            # 处理换行符并应用自动换行
            wrapped_description = self._wrap_text(description, max_chars_per_line=30)
            self.components_map["skill_desc"].set_text(f"描述: {wrapped_description}")

            # --- 更新熟练度需求和升级按钮状态 ---
            proficiency_req_text_comp = self.components_map.get("skill_proficiency_req") # 使用新的键名获取组件
            upgrade_button_comp = self.components_map.get("upgrade_button")

            if proficiency_req_text_comp and upgrade_button_comp:
                if player:
                    max_level = skill_config.get('max_level', 1)
                    # 获取熟练度要求列表
                    points_required_list = skill_config.get('points_required')

                    if current_level < max_level:
                        # 检查此技能是否使用熟练度
                        if points_required_list and isinstance(points_required_list, list) and current_level < len(points_required_list):
                            # === 使用熟练度 ===
                            required_proficiency = points_required_list[current_level] # 获取下一级所需熟练度

                            # 获取当前熟练度
                            current_proficiency = player.skill_proficiencies.get(skill_id, 0)

                            # 更新文本显示
                            proficiency_req_text_comp.set_text(f"需要熟练度: {current_proficiency} / {required_proficiency}")
                            proficiency_req_text_comp.visible = True

                            # 检查升级条件 (只需满足熟练度)
                            proficiency_met = current_proficiency >= required_proficiency
                            can_upgrade = proficiency_met
                            upgrade_button_comp.set_active(can_upgrade)

                            # 设置按钮颜色提示
                            if not proficiency_met:
                                # 熟练度不足 - 设置文本为黄色
                                upgrade_button_comp.text_color = (255, 200, 100)
                            else:
                                # 条件满足 - 恢复默认文本颜色 (假设为白色或浅灰)
                                upgrade_button_comp.text_color = (200, 200, 200) # 或者使用按钮的默认文本颜色

                        else:
                             # === 不使用熟练度 (或配置缺失) ===
                             proficiency_req_text_comp.set_text("") # 隐藏熟练度文本
                             proficiency_req_text_comp.visible = False

                             # 升级始终可用 (如果没有熟练度要求)
                             upgrade_button_comp.set_active(True)
                             upgrade_button_comp.text_color = (200, 200, 200)

                    else:
                        # 技能已满级
                        proficiency_req_text_comp.set_text("技能已满级")
                        proficiency_req_text_comp.visible = True
                        upgrade_button_comp.set_active(False)
                else:
                    # 没有玩家数据
                    proficiency_req_text_comp.set_text("")
                    proficiency_req_text_comp.visible = False
                    upgrade_button_comp.set_active(False)
            else:
                logger.warning("未能找到熟练度需求文本或升级按钮组件")

        except Exception as e:
            logger.error(f"更新技能信息时出错: {e}", exc_info=True)
            # 出错时清空或显示错误提示
            self.components_map["skill_name"].set_text("加载错误")
            self.components_map["skill_level"].set_text("")
            # ... (清空其他组件) ...
            proficiency_req_text_comp = self.components_map.get("skill_proficiency_req") # 错误时也更新这个组件
            if proficiency_req_text_comp:
                proficiency_req_text_comp.set_text("获取需求出错")
                proficiency_req_text_comp.visible = True
            if "upgrade_button" in self.components_map:
                self.components_map["upgrade_button"].set_active(False)

    def _get_effect_description(self, effect_type, effect_value):
        """根据效果类型生成描述"""
        if effect_type == "accuracy":
            return f"增加{effect_value}点准确度"
        elif effect_type == "damage_percent":
            return f"增加{effect_value}%最终伤害"
        elif effect_type == "extra_damage":
            return f"附加{effect_value}%额外伤害"
        elif effect_type == "aoe_damage":
            return f"对附近目标造成{effect_value}%伤害"
        elif effect_type == "stun":
            return f"眩晕目标{effect_value}秒"
        elif effect_type == "damage_multiplier":
            return f"造成{effect_value}倍伤害"
        else:
            return f"{effect_type}: {effect_value}"

    def _on_upgrade_click(self):
        """处理升级技能按钮点击"""
        if not self.selected_skill_id:
            return

        player = self.game_manager.player if hasattr(self.game_manager, 'player') else None
        if not player:
            self.ui_manager.show_message("错误", "无法获取玩家数据")
            return

        skill_id = self.selected_skill_id

        # 获取技能配置以供后续使用 (例如显示名称)
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        if not skill_config:
             logger.error(f"无法获取技能 {skill_id} (职业: {player.character_class}) 的配置，无法升级")
             self.ui_manager.show_message("错误", "无法获取技能数据")
             return

        # 尝试学习/升级技能
        success = player.learn_skill(skill_id)

        if success:
            # 更新技能信息
            self._update_skill_info(skill_id)

            # 更新技能列表
            self._create_skill_list()

            # 显示成功消息
            skill_name = skill_config.get("name", skill_id)
            skill_level = player.skills.get(skill_id, 0)
            self.ui_manager.show_message("技能升级", f"成功将{skill_name}升级至{skill_level}级！")
        else:
            # 显示失败消息
            self.ui_manager.show_message("无法升级", "请检查要求")

    def _on_back_click(self):
        """处理返回按钮点击"""
        self.ui_manager.show_screen("game")

    def _on_toggle_click(self, skill_id):
        """处理技能开关按钮点击"""
        if not hasattr(self.game_manager, 'player') or self.game_manager.player is None:
            return

        player = self.game_manager.player
        if skill_id not in player.skills or player.skills[skill_id] <= 0:
            return

        # 切换技能的禁用状态
        is_disabled = player.toggle_skill_disabled(skill_id)

        # 获取技能数据，用于消息显示
        skill_config = GameConfig.get_skill(skill_id, player.character_class)
        skill_name = skill_config["name"] if skill_config else skill_id
        skill_type = skill_config.get("type") if skill_config else "unknown"

        # 显示状态变更消息
        status = "禁用" if is_disabled else "启用"

        # 为主动技能添加自动释放提示
        if skill_type == "active":
            if is_disabled:
                self.ui_manager.show_message("技能状态", f"已{status}技能：{skill_name}\n该技能将不会自动释放")
                if hasattr(self.game_manager, 'add_log'):
                    self.game_manager.add_log(f"已禁用技能 {skill_name}，将不会自动释放")
            else:
                self.ui_manager.show_message("技能状态", f"已{status}技能：{skill_name}\n该技能将会自动释放")
                if hasattr(self.game_manager, 'add_log'):
                    self.game_manager.add_log(f"已启用技能 {skill_name}，将会自动释放")
        else:
            self.ui_manager.show_message("技能状态", f"已{status}技能：{skill_name}")

        # 重新创建技能列表，更新UI
        self._create_skill_list()

        # 如果当前选中的是被切换的技能，更新详情面板
        if self.selected_skill_id and self.selected_skill_id == skill_id:
            self._update_skill_info(skill_id)

        # 更新游戏界面的技能槽位
        try:
            # 获取GameScreen实例
            game_screen = self.ui_manager.screens.get("game")
            if not game_screen:
                logger.error("无法获取游戏屏幕实例 (GameScreen)")
            else:
                logger.info(f"重新初始化游戏界面技能槽位，响应技能{skill_name}的{status}操作")
                game_screen._init_skill_slots()
        except Exception as e:
            logger.error(f"更新技能槽位时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def show(self):
        """显示技能界面"""
        super().show()

        # 更新职业标签
        if hasattr(self.game_manager, 'player') and self.game_manager.player is not None:
            player = self.game_manager.player
            if hasattr(player, 'character_class') and "class_text" in self.components_map:
                self.components_map["class_text"].set_text(f"职业: {player.character_class}")

        # 重新创建技能列表
        self._create_skill_list()

        # 清除选中的技能
        self.selected_skill_id = None
        self.components_map["skill_name"].set_text("")
        self.components_map["skill_level"].set_text("")
        self.components_map["skill_type"].set_text("")
        self.components_map["skill_cost"].set_text("")
        self.components_map["skill_cooldown"].set_text("")
        self.components_map["skill_effect"].set_text("")
        self.components_map["skill_req"].set_text("")
        self.components_map["skill_desc"].set_text("")
        self.components_map["upgrade_button"].set_active(False)
        # 清空熟练度需求文本
        if "skill_proficiency_req" in self.components_map:
            self.components_map["skill_proficiency_req"].set_text("")
            self.components_map["skill_proficiency_req"].visible = False