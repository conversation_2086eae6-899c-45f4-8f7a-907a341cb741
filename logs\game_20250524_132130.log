[2025-05-24 13:21:30] [WARNING] [main.py:173] 游戏启动
[2025-05-24 13:21:30] [WARNING] [main.py:313] 开始创建游戏界面
[2025-05-24 13:21:32] [WARNING] [main.py:419] 游戏界面创建完成
[2025-05-24 13:22:01] [WARNING] [game_screen.py:4095] 使用火球术失败: 没有目标
[2025-05-24 13:22:23] [ERROR] [battle.py:964] 玩家死亡回调执行失败: 'Game' object has no attribute 'ui_manager'
[2025-05-24 13:22:23] [ERROR] [battle.py:679] 怪物攻击时发生错误: 'NoneType' object has no attribute 'is_dead'
[2025-05-24 13:22:23] [ERROR] [battle.py:681] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 671, in monster_attack
    self.handle_player_death()
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 971, in handle_player_death
    self.player.is_dead = True
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'is_dead'

