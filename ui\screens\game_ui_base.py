# -*- coding: utf-8 -*-
"""
游戏UI面板基类
为游戏界面的各个面板提供统一的基础功能
"""

import pygame
import time
from typing import Dict, Any, Optional, TYPE_CHECKING
from utils.logger import logger

if TYPE_CHECKING:
    from core.game import Game

class GameUIPanel:
    """游戏UI面板基类"""
    
    def __init__(self, ui_manager, game_manager: 'Game', panel_name: str):
        """
        初始化UI面板
        
        参数:
            ui_manager: UI管理器
            game_manager: 游戏管理器
            panel_name: 面板名称
        """
        self.ui_manager = ui_manager
        self.game_manager = game_manager
        self.panel_name = panel_name
        self.components = []
        self.components_map = {}
        self.visible = True
        self.last_update_time = 0
        self.update_interval = 1.0  # 默认1秒更新一次
        
        logger.debug(f"初始化UI面板: {panel_name}")
    
    def add_component(self, component):
        """添加UI组件"""
        if component not in self.components:
            self.components.append(component)
    
    def remove_component(self, component):
        """移除UI组件"""
        if component in self.components:
            self.components.remove(component)
    
    def clear_components(self):
        """清除所有组件"""
        self.components.clear()
        self.components_map.clear()
    
    def create_components(self):
        """创建UI组件 - 子类必须实现"""
        raise NotImplementedError(f"{self.panel_name} 面板必须实现 create_components 方法")
    
    def update(self, dt: float):
        """更新面板 - 子类可以重写"""
        current_time = time.time()
        if current_time - self.last_update_time >= self.update_interval:
            self.update_data()
            self.last_update_time = current_time
    
    def update_data(self):
        """更新数据 - 子类可以重写"""
        pass
    
    def show(self):
        """显示面板"""
        self.visible = True
        for component in self.components:
            if hasattr(component, 'visible'):
                component.visible = True
    
    def hide(self):
        """隐藏面板"""
        self.visible = False
        for component in self.components:
            if hasattr(component, 'visible'):
                component.visible = False
    
    def draw(self, surface: pygame.Surface):
        """绘制面板"""
        if not self.visible:
            return
        
        for component in self.components:
            if hasattr(component, 'draw') and hasattr(component, 'visible'):
                if component.visible:
                    component.draw(surface)
    
    def handle_event(self, event: pygame.event.Event):
        """处理事件 - 子类可以重写"""
        for component in self.components:
            if hasattr(component, 'handle_event') and hasattr(component, 'visible'):
                if component.visible:
                    component.handle_event(event)


class GameUIConstants:
    """游戏UI常量"""
    
    # 颜色主题
    COLORS = {
        "background": (30, 30, 40),
        "panel_bg": (40, 40, 60),
        "panel_border": (60, 60, 80),
        "text_primary": (220, 220, 220),
        "text_secondary": (200, 200, 200),
        "text_disabled": (150, 150, 150),
        "button_normal": (60, 60, 100),
        "button_hover": (80, 80, 120),
        "button_pressed": (40, 40, 80),
        "button_selected": (100, 100, 180),
        "hp_bar": (200, 50, 50),
        "mp_bar": (50, 50, 200),
        "exp_bar": (255, 215, 0),
        "gold_text": (255, 215, 0),
        "yuanbao_text": (0, 191, 255)
    }
    
    # 布局尺寸
    SIZES = {
        "panel_margin": 20,
        "component_spacing": 10,
        "button_height": 40,
        "text_height": 25,
        "bar_height": 20,
        "border_width": 1,
        "left_panel_width": 250,
        "right_panel_width": 250,
        "top_bar_height": 50,
        "bottom_bar_height": 50
    }
    
    # 字体配置
    FONTS = {
        "title": "chinese_title",
        "large": "chinese_large", 
        "normal": "chinese_normal",
        "small": "chinese_small"
    }
    
    # 更新间隔
    UPDATE_INTERVALS = {
        "player_stats": 1.0,      # 玩家属性1秒更新
        "monster_info": 0.5,      # 怪物信息0.5秒更新
        "battle_logs": 0.1,       # 战斗日志0.1秒更新
        "skill_cooldown": 0.1,    # 技能冷却0.1秒更新
        "equipment": 2.0,         # 装备信息2秒更新
        "inventory": 1.0          # 背包信息1秒更新
    }


class UIComponentFactory:
    """UI组件工厂"""
    
    @staticmethod
    def create_panel_with_title(ui_manager, rect: pygame.Rect, title: str, 
                               title_color=None, panel_color=None, border_color=None):
        """创建带标题的面板"""
        if panel_color is None:
            panel_color = GameUIConstants.COLORS["panel_bg"]
        if border_color is None:
            border_color = GameUIConstants.COLORS["panel_border"]
        if title_color is None:
            title_color = GameUIConstants.COLORS["text_primary"]
        
        # 创建面板
        panel = ui_manager.create_panel(
            rect,
            color=panel_color,
            border_color=border_color,
            border_width=GameUIConstants.SIZES["border_width"]
        )
        
        # 创建标题
        title_rect = pygame.Rect(
            rect.x + GameUIConstants.SIZES["panel_margin"],
            rect.y + 10,
            rect.width - 2 * GameUIConstants.SIZES["panel_margin"],
            30
        )
        title_text = ui_manager.create_text(
            title_rect,
            title,
            GameUIConstants.FONTS["normal"],
            title_color,
            "center"
        )
        
        return panel, title_text
    
    @staticmethod
    def create_stat_display(ui_manager, rect: pygame.Rect, label: str, value: str,
                           label_color=None, value_color=None):
        """创建属性显示组件"""
        if label_color is None:
            label_color = GameUIConstants.COLORS["text_secondary"]
        if value_color is None:
            value_color = GameUIConstants.COLORS["text_primary"]
        
        # 标签
        label_rect = pygame.Rect(rect.x, rect.y, rect.width // 2, rect.height)
        label_text = ui_manager.create_text(
            label_rect,
            f"{label}:",
            GameUIConstants.FONTS["small"],
            label_color,
            "left"
        )
        
        # 数值
        value_rect = pygame.Rect(
            rect.x + rect.width // 2, rect.y, 
            rect.width // 2, rect.height
        )
        value_text = ui_manager.create_text(
            value_rect,
            value,
            GameUIConstants.FONTS["small"],
            value_color,
            "right"
        )
        
        return label_text, value_text
    
    @staticmethod
    def create_action_button(ui_manager, rect: pygame.Rect, text: str, callback,
                            button_color=None, text_color=None):
        """创建操作按钮"""
        button = ui_manager.create_button(
            rect,
            text,
            callback,
            GameUIConstants.FONTS["normal"]
        )
        
        if button_color:
            button.colors["normal"] = button_color
        if text_color:
            button.text_color = text_color
            
        return button
    
    @staticmethod
    def create_progress_bar(ui_manager, rect: pygame.Rect, current: int, maximum: int,
                           bar_type: str = "hp"):
        """创建进度条"""
        if bar_type == "hp":
            return ui_manager.create_hp_bar(rect, current, maximum)
        elif bar_type == "mp":
            return ui_manager.create_mp_bar(rect, current, maximum)
        elif bar_type == "exp":
            return ui_manager.create_exp_bar(rect, current, maximum)
        else:
            # 默认进度条
            return ui_manager.create_hp_bar(rect, current, maximum)


def calculate_layout_positions(screen_size: tuple) -> Dict[str, pygame.Rect]:
    """计算各个面板的布局位置"""
    screen_width, screen_height = screen_size
    
    # 基础尺寸
    left_width = GameUIConstants.SIZES["left_panel_width"]
    right_width = GameUIConstants.SIZES["right_panel_width"]
    top_height = GameUIConstants.SIZES["top_bar_height"]
    bottom_height = GameUIConstants.SIZES["bottom_bar_height"]
    margin = GameUIConstants.SIZES["panel_margin"]
    
    # 计算中央区域
    central_x = left_width + margin
    central_width = screen_width - left_width - right_width - 2 * margin
    central_y = top_height + margin
    central_height = screen_height - top_height - bottom_height - 2 * margin
    
    return {
        "screen": pygame.Rect(0, 0, screen_width, screen_height),
        "top_bar": pygame.Rect(0, 0, screen_width, top_height),
        "left_panel": pygame.Rect(margin, top_height + margin, 
                                 left_width, screen_height - top_height - bottom_height - 2 * margin),
        "central_area": pygame.Rect(central_x, central_y, central_width, central_height),
        "right_panel": pygame.Rect(screen_width - right_width - margin, top_height + margin,
                                  right_width, screen_height - top_height - bottom_height - 2 * margin),
        "bottom_bar": pygame.Rect(0, screen_height - bottom_height, screen_width, bottom_height)
    }
