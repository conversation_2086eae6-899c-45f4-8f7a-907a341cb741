[2025-05-22 12:46:41] [WARNING] [main.py:105] 游戏启动
[2025-05-22 12:46:41] [WARNING] [main.py:231] 开始创建游戏界面
[2025-05-22 12:46:41] [WARNING] [main.py:323] 游戏界面创建完成
[2025-05-22 12:46:47] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:46:47] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:46:49] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:46:49] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:46:50] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:46:50] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:46:50] [WARNING] [battle.py:856] 战斗日志: 获得了 12 经验值
[2025-05-22 12:46:50] [WARNING] [battle.py:856] 战斗日志: 获得了 96 金币
[2025-05-22 12:47:03] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:03] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:04] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:04] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:05] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:05] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:07] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:07] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:08] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:08] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:09] [ERROR] [battle.py:653] 怪物攻击时发生错误: list indices must be integers or slices, not str
[2025-05-22 12:47:09] [ERROR] [battle.py:655] Traceback (most recent call last):
  File "e:\BaiduNetdiskDownload\IDM\Demo\老版 (2)\老版\core\battle.py", line 526, in monster_attack
    min_damage = self.monster.attack_range["min"]
                 ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
TypeError: list indices must be integers or slices, not str

[2025-05-22 12:47:09] [WARNING] [battle.py:856] 战斗日志: 获得了 10 经验值
[2025-05-22 12:47:09] [WARNING] [battle.py:856] 战斗日志: 获得了 109 金币
